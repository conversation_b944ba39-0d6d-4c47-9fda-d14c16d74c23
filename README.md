# Portfolio Pro - Professional Portfolio

A modern, responsive professional portfolio built with React and Vite, optimized for deployment on Netlify.

## 🚀 Features

- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Interactive Components**: Smooth animations and transitions
- **SEO Optimized**: Comprehensive meta tags for search engines and social sharing
- **Fast Loading**: Optimized build with Vite for quick page loads
- **Netlify Ready**: Pre-configured for seamless Netlify deployment

## 🛠️ Tech Stack

- **React 18** - Modern React with hooks
- **Vite** - Fast build tool and dev server
- **CSS3** - Custom styling with modern CSS features
- **React Icons** - Icon library
- **React Intersection Observer** - Scroll-based animations

## 📦 Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🌐 Netlify Deployment

This project is pre-configured for Netlify deployment:

1. **Connect your repository** to Netlify
2. **Build settings** are automatically detected from `netlify.toml`:
   - Build command: `npm run build`
   - Publish directory: `build`
3. **Deploy** - <PERSON>lify will automatically build and deploy your site

### Manual Deployment

You can also deploy manually:

```bash
# Build the project
npm run build

# Deploy the build folder to Netlify
# (or drag and drop the build folder to Netlify's deploy interface)
```

## 📁 Project Structure

```
├── public/
│   ├── Logos/            # Company logos and assets
│   ├── _redirects        # Netlify redirects for SPA
│   ├── manifest.json     # PWA manifest
│   ├── sw.js            # Service worker
│   └── vite.svg         # Favicon
├── src/
│   ├── components/       # React components
│   │   ├── *.jsx        # Component files
│   │   └── *.css        # Component styles
│   ├── data/            # Resume data
│   ├── hooks/           # Custom React hooks
│   ├── services/        # Data services
│   ├── test/            # Test setup
│   ├── hooks.jsx        # Additional hooks
│   ├── index.css        # Global styles
│   ├── main.jsx         # App entry point
│   └── App.jsx          # Main App component
├── scripts/             # Build and utility scripts
├── netlify.toml         # Netlify configuration
├── vite.config.js       # Vite configuration
└── package.json         # Dependencies and scripts
```

## 🔧 Configuration

### Netlify Configuration (`netlify.toml`)

- **Build settings**: Automated build process
- **Redirects**: SPA routing support
- **Headers**: Security and caching headers
- **Environment**: Node.js version specification

### Vite Configuration (`vite.config.js`)

- **React plugin**: JSX support
- **Build optimization**: Minification and asset handling
- **Test setup**: Vitest configuration

## 🎨 Customization

To customize the portfolio content:

1. Edit the data in `src/data/resume.json`
2. Modify components in `src/components/`
3. Update styles in `src/index.css`

## 🧪 Testing

```bash
# Run tests
npm run test

# Run tests with UI
npm run test:ui
```

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

---

Built with ❤️ using Portfolio Pro
