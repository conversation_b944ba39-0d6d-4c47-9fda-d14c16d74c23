{"timestamp": "2025-06-27T19:10:48.956Z", "tests": [{"name": "CSS Responsiveness", "passed": false, "issues": ["Consider using more mobile-first media queries"], "mediaQueries": 36, "mobileFirstQueries": 21}, {"name": "Component Responsiveness", "passed": false, "issues": ["Component AppIcon.jsx missing accessibility attributes", "Component AppImage.jsx missing accessibility attributes", "Component DeviceTestRunner.jsx missing accessibility attributes", "Component Education.jsx missing accessibility attributes", "Component ErrorBoundary.jsx missing accessibility attributes", "Component FadeInUp.jsx missing accessibility attributes", "Component Footer.jsx missing accessibility attributes", "Component Publications.jsx missing accessibility attributes", "Component ScrollToTop.jsx missing accessibility attributes"], "componentCount": 17, "responsiveHooksUsage": 3}, {"name": "Performance Optimizations", "passed": true, "issues": []}], "summary": {"total": 3, "passed": 1, "failed": 2}}