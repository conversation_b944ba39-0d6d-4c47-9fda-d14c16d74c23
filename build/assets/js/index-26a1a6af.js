import{r as L,a as qi,R as ul}from"./vendor-b1791c80.js";import{B as pl,a as hl,F as Fa,b as ml,c as fl,d as gl,e as vl,f as wl,g as bl,h as yl}from"./icons-36ac8ea8.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const a of o)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const a={};return o.integrity&&(a.integrity=o.integrity),o.referrerPolicy&&(a.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?a.credentials="include":o.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function r(o){if(o.ep)return;o.ep=!0;const a=n(o);fetch(o.href,a)}})();var Gi={exports:{}},Kr={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _l=L,xl=Symbol.for("react.element"),kl=Symbol.for("react.fragment"),Sl=Object.prototype.hasOwnProperty,Cl=_l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,El={key:!0,ref:!0,__self:!0,__source:!0};function Zi(e,t,n){var r,o={},a=null,i=null;n!==void 0&&(a=""+n),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Sl.call(t,r)&&!El.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:xl,type:e,key:a,ref:i,props:o,_owner:Cl.current}}Kr.Fragment=kl;Kr.jsx=Zi;Kr.jsxs=Zi;Gi.exports=Kr;var f=Gi.exports,Eo={},Da=qi;Eo.createRoot=Da.createRoot,Eo.hydrateRoot=Da.hydrateRoot;const Nl=()=>{const[e,t]=L.useState("hero");return L.useEffect(()=>{const n=()=>{const r=document.querySelectorAll("section"),o=document.getElementById("navbar").offsetHeight,a=window.scrollY+o+50;r.forEach(i=>{const s=i.offsetTop,c=i.offsetHeight,l=i.getAttribute("id");a>=s&&a<s+c&&t(l)})};return window.addEventListener("scroll",n),()=>window.removeEventListener("scroll",n)},[]),e},Al=()=>{const[e,t]=L.useState(!1),[n,r]=L.useState(!1),[o,a]=L.useState({width:typeof window<"u"?window.innerWidth:0,height:typeof window<"u"?window.innerHeight:0});return L.useEffect(()=>{const i=()=>{const s=window.innerWidth,c=window.innerHeight;a({width:s,height:c}),t(s<=768),r(s>768&&s<=1024)};return i(),window.addEventListener("resize",i),window.addEventListener("orientationchange",i),()=>{window.removeEventListener("resize",i),window.removeEventListener("orientationchange",i)}},[]),{isMobile:e,isTablet:n,isDesktop:!e&&!n,screenSize:o,orientation:o.width>o.height?"landscape":"portrait"}},Ml=()=>{const[e,t]=L.useState("up"),[n,r]=L.useState(0),o=L.useRef(0);return L.useEffect(()=>{const a=()=>{const i=window.scrollY,s=i>o.current?"down":"up";s!==e&&Math.abs(i-o.current)>10&&t(s),r(i),o.current=i>0?i:0};return window.addEventListener("scroll",a),()=>window.removeEventListener("scroll",a)},[e]),{scrollDirection:e,scrollY:n}},Il="https://raw.githubusercontent.com/jsonresume/resume-schema/v1.0.0/schema.json",$l={name:"Nicholas Gerasimatos",label:"Principal Technical Consultant | Cloud and Platform Engineering",image:"",url:"https://www.linkedin.com/in/nicholas-gerasimatos",summary:"Specializing in innovative cloud and platform solutions, I align technology with client goals to drive impactful results. With deep expertise in open-source and cloud technologies, I communicate complex systems in a clear, accessible manner and prioritize a client-centered approach. I bring a proven track record in designing and implementing scalable, secure, resilient solutions, leading modernization initiatives, and developing actionable architectures that achieve measurable business outcomes.",location:{countryCode:"US",address:"United States"},profiles:[{network:"LinkedIn",username:"ngerasimatos",url:"https://www.linkedin.com/in/ngerasimatos/"}]},Pl=[{name:"AHEAD",position:"Principal - Platform Engineering and Modernization",startDate:"2024-05-31",endDate:"2024-12-31",highlights:[],summary:`Emerging Technologies Expertise:

Maintain a leading-edge understanding of emerging technologies, with a particular focus on artificial intelligence, and machine learning. Implement AI-driven solutions that streamline operations, and unlock new business value.

Advanced Solution Development:

Architect, design, and implement cutting-edge solutions leveraging open-source frameworks and cloud-native technologies, including Infrastructure as Code (IaC), Platform as a Service (PaaS), and Infrastructure as a Service (IaaS). Integrate advanced AI/ML and AI capabilities to enhance system intelligence, automate workflows, and drive improvements in performance, scalability, and cost efficiency.

Cloud and Hybrid Architecture Optimization:

Optimize enterprise cloud and hybrid infrastructures by applying distributed computing principles and best practices. Conduct in-depth architectural assessments to identify bottlenecks, reduce latency, and maximize system uptime. Deliver robust, resilient solutions that support critical business operations and ensure seamless scalability for large-scale enterprise clients.

Effective Technical Communication:

Translate complex technical concepts into clear, actionable insights for both technical and non-technical stakeholders. Develop and deliver compelling presentations and technical documentation to secure executive buy-in, expedite project approvals, and accelerate delivery timelines. 

Leadership in Cross-Functional Delivery:

Lead diverse, cross-functional teams through the full project lifecycle, from initial scoping to final delivery. Utilize agile methodologies and GitOps practices to drive collaboration, transparency, and continuous improvement.

Advocacy for Best Practices:

Actively champion industry best practices in cloud architecture and software delivery. Mentor teams, develop and disseminate technical guidelines, and establish governance frameworks to elevate the quality and consistency of project outcomes.`,url:"https://www.linkedin.com/company/ahead_2/"},{name:"Amazon Web Services (AWS)",position:"Partner Cloud Architect",startDate:"2022-12-31",endDate:"2024-05-31",highlights:[],summary:`Cloud Solution Architecture and Optimization:

Design, implement, and optimize scalable, secure, and cost-effective AWS cloud solutions with a focus on resiliency, high availability, performance, and cost efficiency. Leverage ROSA/OpenShift, AWS Bedrock, and various other AWS services to deliver intelligent, automated solutions that drive business value.

Cross-Functional Collaboration:

Collaborate with partners, customers, and cross-functional teams to understand diverse requirements and translate them into robust cloud architectures.

Complex Cloud Infrastructure Deployment:

Architect and deploy sophisticated cloud infrastructures tailored to client needs, ensuring seamless integration of AI/ML and generative AI solutions for improved performance and automation.

Training and Enablement Leadership:

Lead training sessions and workshops for internal teams and clients, focusing on Red Hat, Open-Source, and AWS architecture best practices, security compliance, and the adoption of emerging technologies. 

Partner and Community Engagement:

Conduct internal and partner training, participate in industry events, and engage in global webinars and roadshows to showcase innovative AWS and partner solutions. 

Technical Curriculum Development:

Build and implement comprehensive education programs for technical teams, including Immersion Days, Game Days, workshops, and technical documentation that highlight AWS services and partner integrations.

Sales Enablement and Competitive Positioning:

Create concise battle cards to showcase partner solutions differentiators and value. Develop playbooks outlining synergies between partner offerings and AWS services. 

Customer Advocacy and Success Story Development:

Collect and curate customer references and success stories, including co-branded content and showcases of GSI/ISV-developed solutions that demonstrate the impact of AWS and partner driven cloud transformations.`,url:"https://www.linkedin.com/company/amazon-web-services/"},{name:"Red Hat",position:" Emerging Technologies Cloud Engineer ",startDate:"2015-12-31",endDate:"2022-12-31",highlights:[],summary:`Strategic Cloud Modernization and Digital Transformation:

Lead modernization and digital transformation initiatives by driving strategic cloud adoption. Unlock new business opportunities and deliver measurable business benefits through the integration of emerging technologies, blockchain, edge computing, OpenShift, AI, ML, and serverless architectures. Enable organizations to become more agile and future-ready.

Stakeholder Engagement and Consensus Building:

Clearly articulate the value of cloud solutions and emerging technologies to a wide range of stakeholders. Address concerns, answer technical and business questions, and build consensus to ensure buy-in from executive, technical, and operational teams.

Seamless Transition to Cloud-Centric Operating Models:

Facilitate smooth transitions to cloud-centric operating models by ensuring effective integration of advanced technologies such as GenAI, and AI/ML into existing and new workflows. Oversee the adoption of hybrid and multi-cloud strategies to maximize flexibility and support ongoing innovation.

Cloud Readiness Assessments and Migration Strategy:

Develop and implement tailored migration strategies and multiphase roadmaps that align short-term wins with long-term business objectives. Ensure each migration is smooth and cost-effective.

Thought Leadership and Industry Influence:

Establish thought leadership by publishing technical articles, delivering presentations at industry conferences, and actively participating in cloud and emerging technology communities. Share insights on digital transformation, cloud adoption, and practical applications of AI, and ML. 

Collaboration and Innovation Culture:

Build strong partnerships with internal teams, cross-functional stakeholders, and technology vendors to drive continuous innovation. Foster a culture of learning and collaboration that encourages exploration and adoption of emerging technologies in cloud-based solutions.`,url:"https://www.linkedin.com/company/red-hat/"},{name:"FICO",position:"Director of Cloud Service Platforms",startDate:"2013-12-31",endDate:"2015-12-31",highlights:[],summary:`Strategic Cloud Platform Planning and Implementation:

Lead the strategic planning, design, and implementation of cloud service platforms for organizations of varying scale and complexity. Align cloud solutions with business needs to ensure robust, scalable, and secure platforms that support growth and innovation.

Digital Transformation and Service Delivery:

Drive digital transformation initiatives by optimizing infrastructure performance and ensuring seamless delivery of cloud services to both internal and external stakeholders. Focus on enhancing agility, reliability, and user experience across the organization.

Comprehensive Cloud Strategy Development:

Develop and execute comprehensive cloud strategies that align with organizational objectives. Lead cross-functional teams to deliver cloud services that meet business goals, enhance operational efficiency, and foster a culture of innovation.

Expertise in Cloud Service Models:

Demonstrate deep expertise in infrastructure-as-a-service (IaaS), platform-as-a-service (PaaS), and software-as-a-service (SaaS) models. Select and implement effective service models to meet diverse business requirements.

Team Leadership and Talent Development:

Foster a high-performance culture by building collaborative teams, recruiting top talent, and providing mentorship and professional development opportunities. Empower team members to excel in cloud technologies and deliver outstanding results.

Security, Compliance, and Data Protection:
Ensure compliance with industry standards and regulations by establishing robust security controls and data protection measures. Proactively manage risk and safeguard organizational data across all cloud environments.

Vendor and Partner Relationship Management:
Establish and maintain strong relationships with cloud service providers, vendors, and partners. Manage service level agreements (SLAs), and drive value-added partnerships that support organizational goals and deliver long-term value.`,url:"https://www.linkedin.com/company/amazon-web-services"},{name:"American Express",position:"Senior Data Architect",startDate:"2010-12-31",endDate:"2014-12-31",highlights:[],summary:`Designed and implemented scalable, performant, and secure data solutions driving data-driven insights and business value

Applied data architecture principles and a wide range of technologies to solve complex business challenges

Collaborated with cross-functional teams to translate diverse requirements into robust, actionable data solutions aligned with strategic organizational goals

Crafted conceptual, logical, and physical data models ensuring data integrity, accessibility, and optimal organization for diverse analytical workloads

Aligned data architectures with enterprise-wide data governance standards and incorporated long-term scalability considerations

Designed big data environments (Hadoop, Spark, etc.) enabling acquisition, transformation, and analysis of high-volume, high-velocity, and high-variety datasets

Architected solutions empowering advanced analytics, machine learning initiatives, and real-time decision-making

Demonstrated expertise in data privacy regulations, security frameworks, and access control methodologies

Extensive experience with cloud-native or hybrid data solutions (AWS, Azure, GCP) optimizing cost and performance through strategic selection of cloud services and technologies

Identified root causes of data-related bottlenecks and inefficiencies, proposing innovative solutions balancing immediate needs with long-term sustainability`,url:"https://www.linkedin.com/company/american-express/"},{name:"VCE",position:"Principal Architect",startDate:"2009-12-31",endDate:"2012-12-31",highlights:[],summary:`Led the design and implementation of complex and innovative solutions, collaborating with cross-functional teams and stakeholders to align technology initiatives with business goals, enhance performance, and drive organizational growth.

Demonstrated expertise in architecting scalable, reliable, and secure solutions across a wide range of domains and technologies, with a deep understanding of architectural patterns, best practices, and industry standards.

Developed technology roadmaps and long-term architectural visions, leading and mentoring teams to foster a culture of innovation and drive initiatives that deliver value to the organization.

Possessed comprehensive knowledge of various technologies, frameworks, and platforms across cloud computing, distributed systems, microservices, and enterprise architecture, leveraging emerging technologies to drive business innovation and competitive advantage.

Designed and implemented large-scale, distributed, and highly available systems, integrating disparate systems and ensuring interoperability, with proficiency in scalability, performance optimization, and capacity planning.

Demonstrated excellent communication and interpersonal skills, engaging and influencing stakeholders at all levels of the organization, building relationships, fostering collaboration, and presenting complex technical concepts in a clear and concise manner.`,url:"https://www.linkedin.com/company/vce/"},{name:"Microsoft",position:"Senior System Engineer",startDate:"2006-12-31",endDate:"2009-12-31",highlights:[],summary:`Designed, implemented, and managed the Microsoft CORE software development infrastructure platform

Leveraged Microsoft and open-source technologies to optimize system performance, enhance security, and streamline operations

Drove successful projects and collaborated with cross-functional teams to deliver innovative solutions aligned with business objectives

Demonstrated deep knowledge and hands-on experience with Microsoft technologies such as Windows Server, Active Directory, Microsoft Exchange, Microsoft 365, Azure, and PowerShell

Proficient in designing, configuring, and managing Microsoft-based systems and services   Architected and designed scalable and resilient systems based on Microsoft technologies

Skilled in capacity planning, performance tuning, and optimizing infrastructure for maximum efficiency and availability

Integrated on-premises systems with Microsoft Azure and proficient in hybrid cloud design, migration strategies, and managing cloud-based infrastructure

Strong understanding of Microsoft security technologies and best practices, including Identity and Access Management, Active Directory security, and data protection

Implemented security controls and ensured compliance with industry standards

Demonstrated exceptional analytical and problem-solving skills to diagnose and resolve`,url:"https://www.linkedin.com/company/microsoft/",location:"Greater Seattle Area"}],Tl=[{organization:"HUMANE SOCIETY",position:"Volunteer",startDate:"2010-01-01",endDate:"2015-12-31",summary:"Volunteer work supporting animal welfare and community outreach programs.",highlights:[],url:"https://www.linkedin.com/company/9539229"}],Rl=[{title:"RED HAT HONORS OUTSTANDING ACHIEVEMENTS IN OPEN SOURCE WITH NINTH ANNUAL RED HAT INNOVATION AWARDS",date:"2015-10-26",awarder:"Red Hat",summary:`Recognizing striking flexibility, scalability, cost effectiveness, performance, and security within an infrastructure.

Winner: FICO
Leading analytics software company FICO helps businesses in more than 90 countries make better decisions that drive higher levels of growth, profitability and customer satisfaction. FICO wanted to extend its successful, high-end analytics business into new industries and markets. To capitalize on the growing demand for Big Data analytics among companies of all sizes, the company sought to complement its on-premise solution with a web-based service. Using OpenShift Enterprise, the company developed FICO® Analytic Cloud. The new Platform-as-a-Service (PaaS) offering has driven more than US$10 million in sales in a short time frame, winning business from companies that otherwise couldn't have afforded the time or cost of implementing FICO’s sophisticated analytics solutions. Using the Red Hat technology, FICO reduced infrastructure operations staffing by 20 percent, saved hundreds of thousands of dollars in hardware costs, and improved time to market by 70 percent.`}],zl=[{name:"Certified to architect, deploy and implement Vblock 100/200/300/700 infrastructure systems",issuer:"VCE"},{name:"VMware Certified Professional - Data Center Virtualization",issuer:"VMware"},{name:"VMware Sales Professional, Application Modernization, Data Management, Business Continuity, Virtualization of Business Critical Applications, Management, Cloud IaaS, Desktop Virtualization",issuer:"VMware"},{name:"VMware Technical Sales Professional - Business Continuity, Virtualization of Business Critical Applications, Data Management,  Infrastructure Virtualization",issuer:"VMware"},{name:"VMware Certified Associate - Data Center Virtualization, Cloud, Workforce Mobility",issuer:"VMware"},{name:"Red Hat Certificate of Expertise in Platform-as-a-Service",issuer:"Red Hat"},{name:"Red Hat Certificate of Expertise in Data Virtualization",issuer:"Red Hat"},{name:"Red Hat Certificate of Expertise in Clustering and Storage Management",issuer:"Red Hat"},{name:"AWS Certified Solutions Architect - Associate",issuer:"Amazon Web Services (AWS)",endDate:"2025-02-28",startDate:"2023-02-28",url:"https://www.credly.com/badges/3f30bd86-6157-4c5d-9ff5-5b47f38cdb07/public_url"},{name:"Google Cloud - Introduction to Generative AI",issuer:"Google",startDate:"2023-06-30",url:"https://cdn.qwiklabs.com/%2FTntrCzBhpKkF9LHUgvevvIKQb2%2Bufpupa1zPSlY%2Fcs%3D"}],Ol=[{name:"DELL-EMC MERGER LEAVES IT PROS HOPEFUL AND CONCERNED",publisher:"Search Storage",releaseDate:"2015-12-31",summary:`Nick Gerasimatos, director of cloud service and engineering at Fair Isaac Corp. (FICO), sees the merger as a way for Dell to "finally get enterprise-grade storage" because "Compellent was not really enterprise-ready." He also said the deal is also good for VMware to "hopefully allow them to spin and eliminate the influence of EMC and increase their innovation."

But Gerasimatos doesn't see the merger having an impact on FICO's purchasing plans. He said the company is shifting to OpenStack, Ceph software-defined storage and public cloud options to try to move away from VMware Enterprise, vCloud Director and EMC's VMAX, VNX and Avamar within two to three years. He said the main impediment is waiting for hardware depreciation.

"The ROI is not there" with VMware/EMC products, Gerasimatos wrote in an e-mail. He added that FICO staff prefers OpenStack options because they can participate in the software evolution and modify source code to fit their specific needs. "Also, EMC and VMware have a very poor OpenStack story that is less than impressive," he added.

FICO uses SolidFire all-flash arrays for workloads that need high performance or replication and Cisco's UCS as its hardware standard. Only legacy environments use EMC and NetApp, according to Gerasimatos. `,url:"http://searchstorage.techtarget.com/news/4500255416/Dell-EMC-merger-leaves-IT-pros-hopeful-and-concerned"},{name:"FICO CHOOSES RED HAT TO DEPLOY OPENSTACK, MANAGEMENT, AND STORAGE SOLUTIONS FOR AGILE CLOUD INFRASTRUCTURE",publisher:"Market Watch",releaseDate:"2015-12-31",summary:`Nick Gerasimatos, Cloud Development Director, FICO 
“With Red Hat's OpenStack on Cisco UCS and Ceph, we've been able to create an elastic scalable infrastructure that delivers all of the benefits of cloud – speed of innovation, agility, the ability to deliver Software-as-a-Service – but with the ability to securely manage our resources in a private datacenter. This gives us the cloud platform we need to create FICO's offerings, but with control of our data, workloads, compliance and security.”`,url:"http://www.marketwatch.com/story/fico-chooses-red-hat-to-deploy-openstack-management-and-storage-solutions-for-agile-cloud-infrastructure-2015-10-26"},{name:"WHAT’S BEHIND THE ODD COUPLE MICROSOFT-RED HAT PARTNERSHIP",publisher:"Network World",releaseDate:"2015-12-31",summary:"Red Hat customers seemed to embrace the news too. “I think it’s a big win for both companies but a bigger win for Red Hat since Microsoft is now ‘all in’ with their distribution and technologies,” says Nicholas Gerasimatos, director of cloud services engineering at FICO, a big Red Hat user.",url:"http://www.networkworld.com/article/3001391/microsoft-subnet/what-s-behind-the-odd-couple-microsoft-red-hat-partnership.html"},{name:"WILL OPEN SOURCE STORAGE MAKE THE HYPER SCALE DREAM REAL?",publisher:"The Register",releaseDate:"2015-12-31",summary:`Open-source software has become an important presence in many areas of IT, and now, as storage increasingly becomes software-defined storage, it is storage's turn. The darling of the open-source storage movement – though it is by no means the only viable and popular option – is Ceph.

A unified storage platform originally developed for a PhD dissertation in California, Ceph has become one of the most popular, if not THE most popular, storage layers for OpenStack deployments, and with OpenStack leading the cloud computing charge, Ceph is benefiting considerably.`,url:"http://www.theregister.co.uk/2015/11/09/open_source_hyperscale_storage/?mt=1447100084309"},{name:"FICO EMBRACES OPENSTACK | #OPENSTACK",publisher:"SiliconANGLE",releaseDate:"2015-08-21",summary:`FICO’s agressive adoption model

“We adopted it aggressively,” said Gerasimatos when asked how FICO made the transition to OpenStack. FICO’s timeframe from zero to a fully functioning solution was just 12 months.
“What we needed to do was to become a little more agile as we were going global, so that pushed us to go toward the more OpenStack design,” he said. Gerasimatos credits FICO’s close relationship with Red Hat, Inc. as being mutually beneficial.
‘Don’t be afraid; embrace it!’

Gerasimatos sees the benefits of OpenStack as the low point of entry, scalability, software-defined networking and storage without having to pay the penalties. He lists problems encountered during the transition, including how the FICO operations team struggled to get their heads around the distributed scale-out design, as well as difficulties finding qualified engineers with open-source experience. But despite the challenges getting up and going, Gerasimatos said that FICO is very happy with open source and encourages others to follow in the company’s footsteps.

“Everyone is learning as they go along,” he stated. “Every major company, even including Microsoft, is embracing containers and the new scale-out design architecture. I would say don’t be afraid; embrace it!”`,url:"http://siliconangle.com/blog/2015/08/21/fico-embraces-openstack-openstack/"},{name:"FICO PROVES THE MAINSTREAM OPENSTACK ADOPTION POINT",publisher:"Forbes",releaseDate:"2015-06-09",summary:"Analysis of FICO's successful OpenStack adoption as a mainstream enterprise use case, demonstrating the maturity and viability of OpenStack for large-scale production environments.",url:"http://www.forbes.com/sites/benkepes/2015/06/09/fico-proves-the-mainstream-openstack-adoption-point/"},{name:"FICO SAYS OPENSTACK ENTERPRISE IS READY FOR PRIMETIME",publisher:"SearchCloudComputing",releaseDate:"2015-05-01",summary:`FICO, an analytics software company in San Jose, Calif., uses Red Hat to deploy its OpenStack private cloud as opposed to one of the larger public cloud options because it's a more distributed architecture with a higher number of availability zones internationally, said Nick Gerasimatos, director of engineering and cloud services.

The workloads have been in production for 90 days and everything has gone smoothly so far, but there are still issues with some of the projects not maturing fast enough, Gerasimatos said.

"The extensibility of Neutron, it's more for flat networks and it's not great for large scalable infrastructure," Gerasimatos said.  "A lot of that is supposedly changing in Kilo but we would have liked to have seen that a few versions ago."`,url:"http://searchcloudcomputing.techtarget.com/news/4500246812/OpenStack-enterprise-users-say-its-ready-for-primetime"},{name:"HOW STORAGE WORKS IN CONTAINERS",publisher:"OpenStack Superuser",releaseDate:"2015-04-01",summary:"Nick Gerasimatos, senior director of cloud services engineering at FICO, dives into the lack of persistent storage with containers and how Docker volumes and data containers provide a fix.",url:"http://superuser.openstack.org/articles/how-storage-works-in-containers?awesm=awe.sm_jOEqS"},{name:"HOW TO BUILD A LARGE SCALE MULTI-TENANT CLOUD SOLUTION",publisher:"LinkedIn",releaseDate:"2015-03-01",summary:`Its not terribly difficult to design and build a turnkey integrated configured SDDC ready to use solution. 
However building one that completely abstracts the compute, storage and network physical resources and provides multiple tenants a pool of logical resources along with all the necessary management, operational and application level services and allows to scale resources with seamless addition of new rack units. `,url:"https://www.linkedin.com/pulse/how-build-large-scale-multi-tenant-cloud-solution-gerasimatos?trk=prof-post"},{name:"THINK FICO IS A CREDIT SCORING COMPANY? NOPE: IT'S ABOUT LARGE-SCALE ANALYTICS",publisher:"OpenStack Superuser",releaseDate:"2015-02-01",summary:`“We’re always known as a credit-scoring company, but we’re a actually large-scale analytics company,” says Nick Gerasimatos, director of engineering and cloud services at FICO. While 90 percent of all lending decisions in the U.S. currently rely on FICO scores, in 2013 the company launched the FICO analytic cloud for creating, customizing and deploying analytic-driven applications and services. “There are a lot of financial and government institutions that we integrate with and we spend a lot of time deploying in different countries.”

In just the last 12-18 months, he says that the company has tripled in size and number of deployments thanks to OpenStack — reaching Australia, Turkey South Africa, China, Japan in addition to spanning the United States.`,url:"http://superuser.openstack.org/articles/think-fico-is-a-credit-scoring-company-nope-it-s-about-large-scale-analytics"}],jl=[{name:"OpenStack",level:"",keywords:[]},{name:"Cloud Computing IaaS",level:"",keywords:[]},{name:"Continuous Integration and Continuous Delivery (CI/CD)",level:"",keywords:[]},{name:"GitOps",level:"",keywords:[]},{name:"Business Ownership",level:"",keywords:[]},{name:"Containers",level:"",keywords:[]},{name:"OpenShift",level:"",keywords:[]},{name:"Amazon EKS",level:"",keywords:[]},{name:"Load Balancing",level:"",keywords:[]},{name:"Docker",level:"",keywords:[]},{name:"Hybrid Cloud",level:"",keywords:[]},{name:"Red Hat Linux",level:"",keywords:[]},{name:"VMware",level:"",keywords:[]},{name:"Amazon Web Services (AWS)",level:"",keywords:[]},{name:"Private Cloud",level:"",keywords:[]},{name:"Converged Infrastructure",level:"",keywords:[]},{name:"Cloud Development",level:"",keywords:[]},{name:"Research",level:"",keywords:[]},{name:"Defining Requirements",level:"",keywords:[]},{name:"Cloud Security",level:"",keywords:[]},{name:"Kubernetes",level:"",keywords:[]},{name:"Cloud Applications",level:"",keywords:[]},{name:"Engineering",level:"",keywords:[]},{name:"Red Hat Enterprise Linux (RHEL)",level:"",keywords:[]},{name:"Requirements Gathering",level:"",keywords:[]},{name:"Large Language Models (LLM)",level:"",keywords:[]},{name:"Open Systems Architecture",level:"",keywords:[]},{name:"Amazon Bedrock",level:"",keywords:[]},{name:"Distributed Systems",level:"",keywords:[]},{name:"Systems Engineering",level:"",keywords:[]},{name:"Professional Services",level:"",keywords:[]},{name:"Virtualization",level:"",keywords:[]},{name:"Windows Server",level:"",keywords:[]},{name:"Management",level:"",keywords:[]},{name:"Software Defined Storage",level:"",keywords:[]},{name:"Linux",level:"",keywords:[]},{name:"Cloud Computing",level:"",keywords:[]},{name:"Software Defined Networking",level:"",keywords:[]},{name:"Open-Source Software",level:"",keywords:[]},{name:"Release Engineering",level:"",keywords:[]},{name:"Capacity Planning",level:"",keywords:[]},{name:"DevOps",level:"",keywords:[]},{name:"Software Quality Assurance",level:"",keywords:[]},{name:"IaaS",level:"",keywords:[]},{name:"Machine Learning",level:"",keywords:[]},{name:"Vagrant",level:"",keywords:[]},{name:"Software Development",level:"",keywords:[]},{name:"PaaS",level:"",keywords:[]},{name:"Artificial Intelligence (AI)",level:"",keywords:[]}],Ll=[{fluency:"Native Speaker",language:"English"}],Fl=[],Dl=[{name:"David Mitrany",reference:"Nicholas Gerasimatos is a very bright individual in high demand due to his extensive leadership and problem solving skills.  You only need to glance at the list of high profile companies Nicholas has worked for - which is impressive in itself but once you interview Nicholas, that is when you realize Nicholas is the real deal."},{name:"Al Eskew",reference:"I had the opportunity and privilege of working with Nick during his time with Amex.  His technical expertise is of the highest caliber and I would highly recommend him in any of his listed skill sets.  "},{name:"Tony Peters",reference:"I have had the opportunity to work with Nick at FICO for the past two years designing the architecture of FICO's cloud infrastructure.  There are few people out in the industry that equal Nick's combined knowledge of Cloud, Virtualization, Compute and Storage.  The most impressive part of working with Nick is not just that he is knowledgeable but more importantly he's an outstanding communicator and leader, both for the teams he represents as well as working with FICO's business partners, which I am fortunate to take part in."},{name:"Todd N Marinich",reference:"I had pleasure to have Nicholas on our team at AMEX. Nicholas is a dedicated technologist and trusted advisor in his field. His dedication and commitment to his craft is very impressive. Always striving to achieve the client’s goals and a team player. I would recommend Nicholas to anyone looking to solve challenging objectives."},{name:"Justin Watson",reference:"Nick is one of the most talented professionals I have had the honor to work with in technology.  He came in to address performance issues with the virtual environment and a mis-configured UCS system.  Nick hit the ground running, extremely knowledgeable and confident.  He proposed bold changes and produced big results very quickly.  He is a subject matter expert across many disciples and continues to embrace emerging technologies."},{name:"Kyle Bardet",reference:"I had the pleasure of working with Nick during his Residency at PayPal for VCE. Nick was instrumental in the initial implementation of the Vblock infrastructure, as well as leading the Managed Services team that was onsite. Nick is the consummate professional and worked very closely with the customer, understanding their needs and offering direction, when required. He worked well with the other team members to ensure the highest level of customer satisfaction. He brought a lot to the table on the technical side, applying his vast experience to this new implementation. Nick is a hard worker and will stay with an issue until it becomes fully resolved, no matter the timeframe or the effort required."}],Bl=[],Vl={version:"v1.0.0",canonical:"https://github.com/jsonresume/resume-schema/blob/v1.0.0/schema.json"},Hl={$schema:Il,basics:$l,work:Pl,volunteer:Tl,awards:Rl,certificates:zl,publications:Ol,skills:jl,languages:Ll,interests:Fl,references:Dl,projects:Bl,meta:Vl};class Wl{constructor(){this.data=Hl}getBasics(){return this.data.basics}getWork(){return this.data.work}getSkills(){return this.data.skills}getEducation(){return this.data.education||[]}getCertificates(){return this.data.certificates}getPublications(){return this.data.publications}getAwards(){return this.data.awards}getReferences(){return this.data.references}getFormattedWorkExperience(){return this.data.work.map(t=>({title:t.position,company:t.name,duration:this.formatDateRange(t.startDate,t.endDate),location:t.location,responsibilities:t.summary.split(`

`).filter(n=>n.trim()),url:t.url}))}getFormattedSkills(){const t={"Cloud & Virtualization":[],"DevOps & Containerization":[],"Operating Systems & Infrastructure":[],"AI & Data":[],"Software Development & Methodologies":[],"Business & Strategy":[],"Architecture & Core Engineering":[],"Tools & Other":[]};return this.data.skills.forEach(n=>{const r=n.name;let o=!1;r.includes("Cloud")||r.includes("AWS")||r.includes("Amazon Web Services")||r.includes("Azure")||r.includes("Hybrid Cloud")||r.includes("VMware")||r.includes("Private Cloud")||r.includes("IaaS")||r.includes("PaaS")||r.includes("OpenStack")||r.includes("Cloud Security")||r.includes("Virtualization")||r.includes("Cloud Computing")?(t["Cloud & Virtualization"].push(r),o=!0):r.includes("CI/CD")||r.includes("Continuous Integration")||r.includes("GitOps")||r.includes("Docker")||r.includes("Containers")||r.includes("OpenShift")||r.includes("EKS")||r.includes("Amazon EKS")||r.includes("Kubernetes")||r.includes("DevOps")||r.includes("Release Engineering")||r.includes("Vagrant")?(t["DevOps & Containerization"].push(r),o=!0):r.includes("Red Hat Linux")||r.includes("Red Hat Enterprise Linux (RHEL)")||r.includes("Windows Server")||r.includes("Load Balancing")||r.includes("Software Defined Storage")||r.includes("Software Defined Networking")||r.includes("Linux")||r.includes("Converged Infrastructure")?(t["Operating Systems & Infrastructure"].push(r),o=!0):r.includes("AI")||r.includes("Machine Learning")||r.includes("LLM")||r.includes("Amazon Bedrock")||r.includes("Artificial Intelligence")||r.includes("Large Language Models")?(t["AI & Data"].push(r),o=!0):r.includes("Software Development")||r.includes("Software Quality Assurance")||r.includes("Open-Source Software")?(t["Software Development & Methodologies"].push(r),o=!0):r.includes("Business Ownership")||r.includes("Professional Services")||r.includes("Management")||r.includes("Research")||r.includes("Defining Requirements")||r.includes("Requirements Gathering")?(t["Business & Strategy"].push(r),o=!0):(r.includes("Open Systems Architecture")||r.includes("Distributed Systems")||r.includes("Systems Engineering")||r.includes("Capacity Planning")||r.includes("Engineering"))&&(t["Architecture & Core Engineering"].push(r),o=!0),o||t["Tools & Other"].push(r)}),Object.keys(t).forEach(n=>{t[n].length===0&&delete t[n]}),t}getFormattedCertificates(){return this.data.certificates.map(t=>({name:t.name,issuer:t.issuer,date:t.date}))}getFormattedPublications(){return this.data.publications.map(t=>({name:t.name,publisher:t.publisher,releaseDate:t.releaseDate,url:t.url,summary:t.summary}))}formatDateRange(t,n){const r=new Date(t),o=n?new Date(n):null,a=i=>i.toLocaleDateString("en-US",{year:"numeric",month:"short"});return o?`${a(r)} - ${a(o)}`:`${a(r)} - Present`}}const Qe=new Wl,Ul=()=>{const e=Nl(),[t,n]=L.useState(!1),{isMobile:r}=Al(),{scrollDirection:o,scrollY:a}=Ml(),i=r&&o==="down"&&a>100,s=d=>{let p=`# ${d.basics.name}

`;if(p+=`${d.basics.label} | ${d.basics.email} | ${d.basics.phone} | ${d.basics.url}

`,p+=`## Summary

${d.basics.summary}

`,d.work&&d.work.length>0&&(p+=`## Experience

`,d.work.forEach(m=>{p+=`### ${m.position} at ${m.name}
`,p+=`${m.startDate} - ${m.endDate}

`,p+=`${m.summary}

`})),d.education&&d.education.length>0&&(p+=`## Education

`,d.education.forEach(m=>{p+=`### ${m.studyType} from ${m.institution}
`,p+=`${m.startDate} - ${m.endDate}

`})),d.skills&&d.skills.length>0){p+=`## Skills

`;const m=Qe.getFormattedSkills();Object.keys(m).forEach(v=>{p+=`### ${v}
`,p+=m[v].map(g=>`- ${g}`).join(`
`)+`

`})}return d.certificates&&d.certificates.length>0&&(p+=`## Certifications

`,d.certificates.forEach(m=>{p+=`- ${m.name} (${m.issuer})
`}),p+=`
`),d.publications&&d.publications.length>0&&(p+=`## Publications

`,d.publications.forEach(m=>{p+=`### [${m.name}](${m.url})
`,p+=`${m.publisher}, ${m.releaseDate}

`,p+=`${m.summary}

`})),p},c=d=>{const p=Qe.data;let m=`Nicholas_Gerasimatos_Resume.${d}`,v,g;if(d==="json")v=JSON.stringify(p,null,2),g="application/json";else if(d==="md")v=s(p),g="text/markdown";else return;const w=new Blob([v],{type:g}),b=URL.createObjectURL(w),y=document.createElement("a");y.href=b,y.download=m,document.body.appendChild(y),y.click(),document.body.removeChild(y),URL.revokeObjectURL(b)},l=()=>{const d=!t;n(d),r&&(d?document.body.classList.add("mobile-menu-open"):document.body.classList.remove("mobile-menu-open"))},u=()=>{n(!1),document.body.classList.remove("mobile-menu-open")};return L.useEffect(()=>{const d=p=>{const m=document.getElementById("navbar");document.getElementById("hamburger"),t&&m&&!m.contains(p.target)&&n(!1)};return document.addEventListener("mousedown",d),()=>{document.removeEventListener("mousedown",d)}},[t]),L.useEffect(()=>{const d=p=>{p.key==="Escape"&&t&&n(!1)};return document.addEventListener("keydown",d),()=>{document.removeEventListener("keydown",d)}},[t]),f.jsx("nav",{className:`navbar ${i?"navbar--hidden":""} ${t?"navbar--menu-open":""}`,id:"navbar",role:"navigation","aria-label":"Main navigation",children:f.jsxs("div",{className:"nav-container",children:[f.jsx("div",{className:"nav-logo",children:f.jsx("span",{className:"nav-name",role:"banner",children:"Nicholas Gerasimatos"})}),f.jsxs("ul",{className:`nav-menu ${t?"active":""}`,id:"nav-menu",role:"menubar","aria-label":"Main menu","aria-hidden":!t&&r,children:[f.jsx("li",{role:"none",children:f.jsx("a",{href:"#hero",className:`nav-link ${e==="hero"?"active":""}`,onClick:u,role:"menuitem","aria-current":e==="hero"?"page":void 0,children:"Home"})}),f.jsx("li",{role:"none",children:f.jsx("a",{href:"#about",className:`nav-link ${e==="about"?"active":""}`,onClick:u,role:"menuitem","aria-current":e==="about"?"page":void 0,children:"About"})}),f.jsx("li",{role:"none",children:f.jsx("a",{href:"#experience",className:`nav-link ${e==="experience"?"active":""}`,onClick:u,role:"menuitem","aria-current":e==="experience"?"page":void 0,children:"Experience"})}),f.jsx("li",{role:"none",children:f.jsx("a",{href:"#skills",className:`nav-link ${e==="skills"?"active":""}`,onClick:u,role:"menuitem","aria-current":e==="skills"?"page":void 0,children:"Skills"})}),f.jsx("li",{role:"none",children:f.jsx("a",{href:"#education",className:`nav-link ${e==="education"?"active":""}`,onClick:u,role:"menuitem","aria-current":e==="education"?"page":void 0,children:"Education"})}),f.jsx("li",{role:"none",children:f.jsx("a",{href:"#publications",className:`nav-link ${e==="publications"?"active":""}`,onClick:u,role:"menuitem","aria-current":e==="publications"?"page":void 0,children:"Publications"})}),f.jsxs("li",{className:"download-buttons",role:"none",children:[f.jsx("button",{onClick:()=>c("json"),className:"btn btn--primary btn--sm","aria-label":"Download resume as JSON file",children:"Download JSON"}),f.jsx("button",{onClick:()=>c("md"),className:"btn btn--secondary btn--sm","aria-label":"Download resume as Markdown file",children:"Download Markdown"})]})]}),f.jsxs("button",{className:`hamburger ${t?"active":""}`,id:"hamburger",onClick:l,"aria-label":t?"Close mobile menu":"Open mobile menu","aria-expanded":t,"aria-controls":"nav-menu",children:[f.jsx("span",{"aria-hidden":"true"}),f.jsx("span",{"aria-hidden":"true"}),f.jsx("span",{"aria-hidden":"true"})]})]})})};var No=new Map,vr=new WeakMap,Ba=0,ql=void 0;function Gl(e){return e?(vr.has(e)||(Ba+=1,vr.set(e,Ba.toString())),vr.get(e)):"0"}function Zl(e){return Object.keys(e).sort().filter(t=>e[t]!==void 0).map(t=>`${t}_${t==="root"?Gl(e.root):e[t]}`).toString()}function Yl(e){const t=Zl(e);let n=No.get(t);if(!n){const r=new Map;let o;const a=new IntersectionObserver(i=>{i.forEach(s=>{var c;const l=s.isIntersecting&&o.some(u=>s.intersectionRatio>=u);e.trackVisibility&&typeof s.isVisible>"u"&&(s.isVisible=l),(c=r.get(s.target))==null||c.forEach(u=>{u(l,s)})})},e);o=a.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:a,elements:r},No.set(t,n)}return n}function Kl(e,t,n={},r=ql){if(typeof window.IntersectionObserver>"u"&&r!==void 0){const c=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:typeof n.threshold=="number"?n.threshold:0,time:0,boundingClientRect:c,intersectionRect:c,rootBounds:c}),()=>{}}const{id:o,observer:a,elements:i}=Yl(n),s=i.get(e)||[];return i.has(e)||i.set(e,s),s.push(t),a.observe(e),function(){s.splice(s.indexOf(t),1),s.length===0&&(i.delete(e),a.unobserve(e)),i.size===0&&(a.disconnect(),No.delete(o))}}function Ql({threshold:e,delay:t,trackVisibility:n,rootMargin:r,root:o,triggerOnce:a,skip:i,initialInView:s,fallbackInView:c,onChange:l}={}){var u;const[d,p]=L.useState(null),m=L.useRef(l),[v,g]=L.useState({inView:!!s,entry:void 0});m.current=l,L.useEffect(()=>{if(i||!d)return;let S;return S=Kl(d,(M,O)=>{g({inView:M,entry:O}),m.current&&m.current(M,O),O.isIntersecting&&a&&S&&(S(),S=void 0)},{root:o,rootMargin:r,threshold:e,trackVisibility:n,delay:t},c),()=>{S&&S()}},[Array.isArray(e)?e.toString():e,d,o,r,a,i,n,c,t]);const w=(u=v.entry)==null?void 0:u.target,b=L.useRef(void 0);!d&&w&&!a&&!i&&b.current!==w&&(b.current=w,g({inView:!!s,entry:void 0}));const y=[p,v.inView,v.entry];return y.ref=y[0],y.inView=y[1],y.entry=y[2],y}const rt=({children:e,delay:t=0})=>{const{ref:n,inView:r}=Ql({triggerOnce:!0,threshold:.1});return f.jsx("div",{ref:n,className:r?"fade-in-up":"",style:{animationDelay:`${t}s`},children:e})};const Xl=()=>{const e=Qe.getBasics(),t=e.profiles.find(r=>r.network==="LinkedIn"),n=["Cloud Architecture","AI/ML Integration","Platform Engineering","Digital Transformation"];return f.jsxs("section",{id:"hero",className:"hero","aria-labelledby":"hero-title",children:[f.jsxs("div",{className:"video-background",children:[f.jsx("iframe",{src:"https://www.youtube.com/embed/tXxzOXwaf1c?autoplay=1&mute=1&loop=1&playlist=tXxzOXwaf1c&start=87&controls=0&showinfo=0&rel=0",title:"Background Video",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,className:"video-iframe"}),f.jsx("div",{className:"video-overlay"})]}),f.jsx("div",{className:"hero-banner","aria-hidden":"true",children:f.jsx("div",{className:"banner-pattern"})}),f.jsx("div",{className:"hero-container",children:f.jsx("div",{className:"hero-content",children:f.jsxs("div",{className:"hero-text",children:[f.jsx(rt,{delay:.1,children:f.jsx("h1",{id:"hero-title",className:"hero-title",children:e.name})}),f.jsx(rt,{delay:.2,children:f.jsxs("h2",{className:"hero-headline",children:[e.label.split("|")[0].trim()," • ",n.slice(0,3).join(" • ")]})}),f.jsx(rt,{delay:.3,children:f.jsx("p",{className:"hero-value-prop",children:"Driving business value through innovative cloud solutions and AI integration"})}),f.jsx(rt,{delay:.4,children:f.jsx("div",{className:"hero-buttons",children:t&&f.jsxs("a",{href:t.url,target:"_blank",rel:"noopener noreferrer",className:"btn btn--linkedin",children:[f.jsx("span",{children:"LinkedIn Profile"}),f.jsx("span",{className:"btn-icon",children:"↗"})]})})})]})})})]})};const Jl=()=>{Qe.getBasics().profiles.find(r=>r.network==="LinkedIn");const t=[{icon:"🏆",title:"Award Winner",description:"Red Hat Innovation Award Winner • Recognized for delivering $10M+ in business value through innovative cloud solutions"},{icon:"🚀",title:"Innovation Driver",description:"Led FICO's $10M+ analytics platform transformation with 70% faster time-to-market"},{icon:"☁️",title:"Cloud Expert",description:"AWS Partner Cloud Architect with 15+ years in enterprise cloud solutions"},{icon:"🤖",title:"AI Integration",description:"Implementing cutting-edge AI/ML solutions for Fortune 500 companies"}],n=[{quote:"Nicholas delivered exceptional results on our cloud transformation, reducing costs by 20% while improving performance.",author:"Enterprise Client",role:"Fortune 500 Company"},{quote:"His expertise in AI/ML integration helped us unlock new business opportunities worth millions.",author:"Technology Partner",role:"Cloud Solutions Provider"}];return f.jsx("section",{id:"about",className:"about","aria-labelledby":"about-title",children:f.jsxs("div",{className:"container",children:[f.jsx("h2",{id:"about-title",className:"section-title",children:"About Me"}),f.jsxs("div",{className:"about-content",children:[f.jsx("div",{className:"about-intro",children:f.jsx("p",{className:"about-text",children:"With 15+ years of experience in cloud and platform solutions, I specialize in transforming complex technical challenges into measurable business value. My expertise spans AI/ML integration, multi-cloud architectures, and enterprise modernization initiatives."})}),f.jsx("div",{className:"value-props-grid",children:t.map((r,o)=>f.jsxs("div",{className:"value-prop-card",children:[f.jsx("span",{className:"value-prop-icon",children:r.icon}),f.jsx("h3",{className:"value-prop-title",children:r.title}),f.jsx("p",{className:"value-prop-description",children:r.description})]},o))}),f.jsxs("div",{className:"testimonials-section",children:[f.jsx("h3",{className:"testimonials-title",children:"What Clients Say"}),f.jsx("div",{className:"testimonials-grid",children:n.map((r,o)=>f.jsxs("div",{className:"testimonial-card",children:[f.jsxs("p",{className:"testimonial-quote",children:['"',r.quote,'"']}),f.jsxs("div",{className:"testimonial-author",children:[f.jsx("span",{className:"author-name",children:r.author}),f.jsx("span",{className:"author-role",children:r.role})]})]},o))})]})]})]})})};const ed=()=>{var n;const e=Qe.getCertificates(),t=[{type:"achievement",title:"Red Hat Innovation Award Winner",description:"Recognized for outstanding achievements in open source with FICO's $10M+ analytics platform",icon:"🏆",link:"#awards",highlight:"Reduced infrastructure costs by 20% and improved time to market by 70%"},{type:"certification",title:"AWS Solutions Architect",description:"Current AWS certification demonstrating cloud architecture expertise",icon:"☁️",link:((n=e.find(r=>r.name.includes("AWS")))==null?void 0:n.url)||"#certificates",highlight:"Valid through 2025"},{type:"expertise",title:"AI/ML Integration Specialist",description:"Leading AI-driven solutions and automation implementations",icon:"🤖",link:"#experience",highlight:"Implementing cutting-edge AI solutions at AHEAD"},{type:"thought-leadership",title:"OpenStack Speakers Bureau",description:"Published articles and industry conference presentations",icon:"🎤",link:"#publications",highlight:"Recognized thought leader in cloud technologies"}];return f.jsx("section",{id:"featured",className:"featured","aria-labelledby":"featured-title",children:f.jsxs("div",{className:"container",children:[f.jsxs(rt,{children:[f.jsx("h2",{id:"featured-title",className:"section-title",children:"Featured Highlights"}),f.jsx("p",{className:"section-subtitle",children:"Key achievements and expertise that drive results"})]}),f.jsx("div",{className:"featured-grid",children:t.map((r,o)=>f.jsx(rt,{delay:.1*(o+1),children:f.jsxs("div",{className:"featured-card",children:[f.jsxs("div",{className:"featured-card-header",children:[f.jsx("span",{className:"featured-icon",children:r.icon}),f.jsx("div",{className:"featured-badge",children:r.type})]}),f.jsx("h3",{className:"featured-title",children:r.title}),f.jsx("p",{className:"featured-description",children:r.description})]})},o))})]})})};const td=()=>{const[e,t]=L.useState(0),n=o=>{t(e===o?null:o)},r=Qe.getFormattedWorkExperience();return f.jsx("section",{id:"experience",className:"experience",children:f.jsxs("div",{className:"container",children:[f.jsx("h2",{className:"section-title",children:"Experience"}),f.jsx("div",{className:"timeline",children:r.map((o,a)=>f.jsx(rt,{children:f.jsx("div",{className:"timeline-item","data-company":o.company,children:f.jsxs("div",{className:"timeline-content",children:[f.jsxs("div",{className:"timeline-header",onClick:()=>n(a),children:[f.jsx("h3",{className:"job-title",children:o.title}),f.jsx("span",{className:"company",children:o.company}),f.jsx("span",{className:"duration",children:o.duration}),o.location&&f.jsx("span",{className:"location",children:o.location}),f.jsx("button",{className:`expand-btn ${e===a?"active":""}`,"aria-label":"Expand details",children:e===a?"−":"+"})]}),f.jsx("div",{className:`timeline-details ${e===a?"expanded":""}`,children:f.jsx("ul",{className:"responsibilities",children:o.responsibilities.map((i,s)=>f.jsx("li",{children:i},s))})})]})})},a))})]})})};var aa=ir(),H=e=>ar(e,aa),ia=ir();H.write=e=>ar(e,ia);var Qr=ir();H.onStart=e=>ar(e,Qr);var sa=ir();H.onFrame=e=>ar(e,sa);var ca=ir();H.onFinish=e=>ar(e,ca);var tn=[];H.setTimeout=(e,t)=>{const n=H.now()+t,r=()=>{const a=tn.findIndex(i=>i.cancel==r);~a&&tn.splice(a,1),Et-=~a?1:0},o={time:n,handler:e,cancel:r};return tn.splice(Yi(n),0,o),Et+=1,Ki(),o};var Yi=e=>~(~tn.findIndex(t=>t.time>e)||~tn.length);H.cancel=e=>{Qr.delete(e),sa.delete(e),ca.delete(e),aa.delete(e),ia.delete(e)};H.sync=e=>{Ao=!0,H.batchedUpdates(e),Ao=!1};H.throttle=e=>{let t;function n(){try{e(...t)}finally{t=null}}function r(...o){t=o,H.onStart(n)}return r.handler=e,r.cancel=()=>{Qr.delete(n),t=null},r};var la=typeof window<"u"?window.requestAnimationFrame:()=>{};H.use=e=>la=e;H.now=typeof performance<"u"?()=>performance.now():Date.now;H.batchedUpdates=e=>e();H.catch=console.error;H.frameLoop="always";H.advance=()=>{H.frameLoop!=="demand"?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):Xi()};var Ct=-1,Et=0,Ao=!1;function ar(e,t){Ao?(t.delete(e),e(0)):(t.add(e),Ki())}function Ki(){Ct<0&&(Ct=0,H.frameLoop!=="demand"&&la(Qi))}function nd(){Ct=-1}function Qi(){~Ct&&(la(Qi),H.batchedUpdates(Xi))}function Xi(){const e=Ct;Ct=H.now();const t=Yi(Ct);if(t&&(Ji(tn.splice(0,t),n=>n.handler()),Et-=t),!Et){nd();return}Qr.flush(),aa.flush(e?Math.min(64,Ct-e):16.667),sa.flush(),ia.flush(),ca.flush()}function ir(){let e=new Set,t=e;return{add(n){Et+=t==e&&!e.has(n)?1:0,e.add(n)},delete(n){return Et-=t==e&&e.has(n)?1:0,e.delete(n)},flush(n){t.size&&(e=new Set,Et-=t.size,Ji(t,r=>r(n)&&e.add(r)),Et+=e.size,t=e)}}}function Ji(e,t){e.forEach(n=>{try{t(n)}catch(r){H.catch(r)}})}var rd=Object.defineProperty,od=(e,t)=>{for(var n in t)rd(e,n,{get:t[n],enumerable:!0})},Je={};od(Je,{assign:()=>id,colors:()=>Nt,createStringInterpolator:()=>ua,skipAnimation:()=>ts,to:()=>es,willAdvance:()=>pa});function Mo(){}var ad=(e,t,n)=>Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0}),k={arr:Array.isArray,obj:e=>!!e&&e.constructor.name==="Object",fun:e=>typeof e=="function",str:e=>typeof e=="string",num:e=>typeof e=="number",und:e=>e===void 0};function mt(e,t){if(k.arr(e)){if(!k.arr(t)||e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}return e===t}var J=(e,t)=>e.forEach(t);function st(e,t,n){if(k.arr(e)){for(let r=0;r<e.length;r++)t.call(n,e[r],`${r}`);return}for(const r in e)e.hasOwnProperty(r)&&t.call(n,e[r],r)}var Ve=e=>k.und(e)?[]:k.arr(e)?e:[e];function Mn(e,t){if(e.size){const n=Array.from(e);e.clear(),J(n,t)}}var Sn=(e,...t)=>Mn(e,n=>n(...t)),da=()=>typeof window>"u"||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),ua,es,Nt=null,ts=!1,pa=Mo,id=e=>{e.to&&(es=e.to),e.now&&(H.now=e.now),e.colors!==void 0&&(Nt=e.colors),e.skipAnimation!=null&&(ts=e.skipAnimation),e.createStringInterpolator&&(ua=e.createStringInterpolator),e.requestAnimationFrame&&H.use(e.requestAnimationFrame),e.batchedUpdates&&(H.batchedUpdates=e.batchedUpdates),e.willAdvance&&(pa=e.willAdvance),e.frameLoop&&(H.frameLoop=e.frameLoop)},In=new Set,qe=[],uo=[],Pr=0,Xr={get idle(){return!In.size&&!qe.length},start(e){Pr>e.priority?(In.add(e),H.onStart(sd)):(ns(e),H(Io))},advance:Io,sort(e){if(Pr)H.onFrame(()=>Xr.sort(e));else{const t=qe.indexOf(e);~t&&(qe.splice(t,1),rs(e))}},clear(){qe=[],In.clear()}};function sd(){In.forEach(ns),In.clear(),H(Io)}function ns(e){qe.includes(e)||rs(e)}function rs(e){qe.splice(cd(qe,t=>t.priority>e.priority),0,e)}function Io(e){const t=uo;for(let n=0;n<qe.length;n++){const r=qe[n];Pr=r.priority,r.idle||(pa(r),r.advance(e),r.idle||t.push(r))}return Pr=0,uo=qe,uo.length=0,qe=t,qe.length>0}function cd(e,t){const n=e.findIndex(t);return n<0?e.length:n}var ld=(e,t,n)=>Math.min(Math.max(n,e),t),dd={transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199},Ye="[-+]?\\d*\\.?\\d+",Tr=Ye+"%";function Jr(...e){return"\\(\\s*("+e.join(")\\s*,\\s*(")+")\\s*\\)"}var ud=new RegExp("rgb"+Jr(Ye,Ye,Ye)),pd=new RegExp("rgba"+Jr(Ye,Ye,Ye,Ye)),hd=new RegExp("hsl"+Jr(Ye,Tr,Tr)),md=new RegExp("hsla"+Jr(Ye,Tr,Tr,Ye)),fd=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,gd=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,vd=/^#([0-9a-fA-F]{6})$/,wd=/^#([0-9a-fA-F]{8})$/;function bd(e){let t;return typeof e=="number"?e>>>0===e&&e>=0&&e<=4294967295?e:null:(t=vd.exec(e))?parseInt(t[1]+"ff",16)>>>0:Nt&&Nt[e]!==void 0?Nt[e]:(t=ud.exec(e))?(Kt(t[1])<<24|Kt(t[2])<<16|Kt(t[3])<<8|255)>>>0:(t=pd.exec(e))?(Kt(t[1])<<24|Kt(t[2])<<16|Kt(t[3])<<8|Wa(t[4]))>>>0:(t=fd.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=wd.exec(e))?parseInt(t[1],16)>>>0:(t=gd.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=hd.exec(e))?(Va(Ha(t[1]),wr(t[2]),wr(t[3]))|255)>>>0:(t=md.exec(e))?(Va(Ha(t[1]),wr(t[2]),wr(t[3]))|Wa(t[4]))>>>0:null}function po(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Va(e,t,n){const r=n<.5?n*(1+t):n+t-n*t,o=2*n-r,a=po(o,r,e+1/3),i=po(o,r,e),s=po(o,r,e-1/3);return Math.round(a*255)<<24|Math.round(i*255)<<16|Math.round(s*255)<<8}function Kt(e){const t=parseInt(e,10);return t<0?0:t>255?255:t}function Ha(e){return(parseFloat(e)%360+360)%360/360}function Wa(e){const t=parseFloat(e);return t<0?0:t>1?255:Math.round(t*255)}function wr(e){const t=parseFloat(e);return t<0?0:t>100?1:t/100}function Ua(e){let t=bd(e);if(t===null)return e;t=t||0;const n=(t&4278190080)>>>24,r=(t&16711680)>>>16,o=(t&65280)>>>8,a=(t&255)/255;return`rgba(${n}, ${r}, ${o}, ${a})`}var zn=(e,t,n)=>{if(k.fun(e))return e;if(k.arr(e))return zn({range:e,output:t,extrapolate:n});if(k.str(e.output[0]))return ua(e);const r=e,o=r.output,a=r.range||[0,1],i=r.extrapolateLeft||r.extrapolate||"extend",s=r.extrapolateRight||r.extrapolate||"extend",c=r.easing||(l=>l);return l=>{const u=_d(l,a);return yd(l,a[u],a[u+1],o[u],o[u+1],c,i,s,r.map)}};function yd(e,t,n,r,o,a,i,s,c){let l=c?c(e):e;if(l<t){if(i==="identity")return l;i==="clamp"&&(l=t)}if(l>n){if(s==="identity")return l;s==="clamp"&&(l=n)}return r===o?r:t===n?e<=t?r:o:(t===-1/0?l=-l:n===1/0?l=l-t:l=(l-t)/(n-t),l=a(l),r===-1/0?l=-l:o===1/0?l=l+r:l=l*(o-r)+r,l)}function _d(e,t){for(var n=1;n<t.length-1&&!(t[n]>=e);++n);return n-1}var xd=(e,t="end")=>n=>{n=t==="end"?Math.min(n,.999):Math.max(n,.001);const r=n*e,o=t==="end"?Math.floor(r):Math.ceil(r);return ld(0,1,o/e)},Rr=1.70158,br=Rr*1.525,qa=Rr+1,Ga=2*Math.PI/3,Za=2*Math.PI/4.5,yr=e=>e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375,kd={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>1-(1-e)*(1-e),easeInOutQuad:e=>e<.5?2*e*e:1-Math.pow(-2*e+2,2)/2,easeInCubic:e=>e*e*e,easeOutCubic:e=>1-Math.pow(1-e,3),easeInOutCubic:e=>e<.5?4*e*e*e:1-Math.pow(-2*e+2,3)/2,easeInQuart:e=>e*e*e*e,easeOutQuart:e=>1-Math.pow(1-e,4),easeInOutQuart:e=>e<.5?8*e*e*e*e:1-Math.pow(-2*e+2,4)/2,easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>1-Math.pow(1-e,5),easeInOutQuint:e=>e<.5?16*e*e*e*e*e:1-Math.pow(-2*e+2,5)/2,easeInSine:e=>1-Math.cos(e*Math.PI/2),easeOutSine:e=>Math.sin(e*Math.PI/2),easeInOutSine:e=>-(Math.cos(Math.PI*e)-1)/2,easeInExpo:e=>e===0?0:Math.pow(2,10*e-10),easeOutExpo:e=>e===1?1:1-Math.pow(2,-10*e),easeInOutExpo:e=>e===0?0:e===1?1:e<.5?Math.pow(2,20*e-10)/2:(2-Math.pow(2,-20*e+10))/2,easeInCirc:e=>1-Math.sqrt(1-Math.pow(e,2)),easeOutCirc:e=>Math.sqrt(1-Math.pow(e-1,2)),easeInOutCirc:e=>e<.5?(1-Math.sqrt(1-Math.pow(2*e,2)))/2:(Math.sqrt(1-Math.pow(-2*e+2,2))+1)/2,easeInBack:e=>qa*e*e*e-Rr*e*e,easeOutBack:e=>1+qa*Math.pow(e-1,3)+Rr*Math.pow(e-1,2),easeInOutBack:e=>e<.5?Math.pow(2*e,2)*((br+1)*2*e-br)/2:(Math.pow(2*e-2,2)*((br+1)*(e*2-2)+br)+2)/2,easeInElastic:e=>e===0?0:e===1?1:-Math.pow(2,10*e-10)*Math.sin((e*10-10.75)*Ga),easeOutElastic:e=>e===0?0:e===1?1:Math.pow(2,-10*e)*Math.sin((e*10-.75)*Ga)+1,easeInOutElastic:e=>e===0?0:e===1?1:e<.5?-(Math.pow(2,20*e-10)*Math.sin((20*e-11.125)*Za))/2:Math.pow(2,-20*e+10)*Math.sin((20*e-11.125)*Za)/2+1,easeInBounce:e=>1-yr(1-e),easeOutBounce:yr,easeInOutBounce:e=>e<.5?(1-yr(1-2*e))/2:(1+yr(2*e-1))/2,steps:xd},On=Symbol.for("FluidValue.get"),an=Symbol.for("FluidValue.observers"),Ue=e=>!!(e&&e[On]),Oe=e=>e&&e[On]?e[On]():e,Ya=e=>e[an]||null;function Sd(e,t){e.eventObserved?e.eventObserved(t):e(t)}function jn(e,t){const n=e[an];n&&n.forEach(r=>{Sd(r,t)})}var os=class{constructor(e){if(!e&&!(e=this.get))throw Error("Unknown getter");Cd(this,e)}},Cd=(e,t)=>as(e,On,t);function mn(e,t){if(e[On]){let n=e[an];n||as(e,an,n=new Set),n.has(t)||(n.add(t),e.observerAdded&&e.observerAdded(n.size,t))}return t}function Ln(e,t){const n=e[an];if(n&&n.has(t)){const r=n.size-1;r?n.delete(t):e[an]=null,e.observerRemoved&&e.observerRemoved(r,t)}}var as=(e,t,n)=>Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0}),Ar=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,Ed=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,Ka=new RegExp(`(${Ar.source})(%|[a-z]+)`,"i"),Nd=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,eo=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/,is=e=>{const[t,n]=Ad(e);if(!t||da())return e;const r=window.getComputedStyle(document.documentElement).getPropertyValue(t);if(r)return r.trim();if(n&&n.startsWith("--")){const o=window.getComputedStyle(document.documentElement).getPropertyValue(n);return o||e}else{if(n&&eo.test(n))return is(n);if(n)return n}return e},Ad=e=>{const t=eo.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]},ho,Md=(e,t,n,r,o)=>`rgba(${Math.round(t)}, ${Math.round(n)}, ${Math.round(r)}, ${o})`,ss=e=>{ho||(ho=Nt?new RegExp(`(${Object.keys(Nt).join("|")})(?!\\w)`,"g"):/^\b$/);const t=e.output.map(a=>Oe(a).replace(eo,is).replace(Ed,Ua).replace(ho,Ua)),n=t.map(a=>a.match(Ar).map(Number)),o=n[0].map((a,i)=>n.map(s=>{if(!(i in s))throw Error('The arity of each "output" value must be equal');return s[i]})).map(a=>zn({...e,output:a}));return a=>{var c;const i=!Ka.test(t[0])&&((c=t.find(l=>Ka.test(l)))==null?void 0:c.replace(Ar,""));let s=0;return t[0].replace(Ar,()=>`${o[s++](a)}${i||""}`).replace(Nd,Md)}},ha="react-spring: ",cs=e=>{const t=e;let n=!1;if(typeof t!="function")throw new TypeError(`${ha}once requires a function parameter`);return(...r)=>{n||(t(...r),n=!0)}},Id=cs(console.warn);function $d(){Id(`${ha}The "interpolate" function is deprecated in v9 (use "to" instead)`)}var Pd=cs(console.warn);function Td(){Pd(`${ha}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`)}function to(e){return k.str(e)&&(e[0]=="#"||/\d/.test(e)||!da()&&eo.test(e)||e in(Nt||{}))}var ma=da()?L.useEffect:L.useLayoutEffect,Rd=()=>{const e=L.useRef(!1);return ma(()=>(e.current=!0,()=>{e.current=!1}),[]),e};function ls(){const e=L.useState()[1],t=Rd();return()=>{t.current&&e(Math.random())}}var ds=e=>L.useEffect(e,zd),zd=[];function Qa(e){const t=L.useRef(void 0);return L.useEffect(()=>{t.current=e}),t.current}var Fn=Symbol.for("Animated:node"),Od=e=>!!e&&e[Fn]===e,nt=e=>e&&e[Fn],fa=(e,t)=>ad(e,Fn,t),no=e=>e&&e[Fn]&&e[Fn].getPayload(),us=class{constructor(){fa(this,this)}getPayload(){return this.payload||[]}},ro=class ps extends us{constructor(t){super(),this._value=t,this.done=!0,this.durationProgress=0,k.num(this._value)&&(this.lastPosition=this._value)}static create(t){return new ps(t)}getPayload(){return[this]}getValue(){return this._value}setValue(t,n){return k.num(t)&&(this.lastPosition=t,n&&(t=Math.round(t/n)*n,this.done&&(this.lastPosition=t))),this._value===t?!1:(this._value=t,!0)}reset(){const{done:t}=this;this.done=!1,k.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,t&&(this.lastVelocity=null),this.v0=null)}},zr=class hs extends ro{constructor(t){super(0),this._string=null,this._toString=zn({output:[t,t]})}static create(t){return new hs(t)}getValue(){const t=this._string;return t??(this._string=this._toString(this._value))}setValue(t){if(k.str(t)){if(t==this._string)return!1;this._string=t,this._value=1}else if(super.setValue(t))this._string=null;else return!1;return!0}reset(t){t&&(this._toString=zn({output:[this.getValue(),t]})),this._value=0,super.reset()}},Or={dependencies:null},oo=class extends us{constructor(e){super(),this.source=e,this.setValue(e)}getValue(e){const t={};return st(this.source,(n,r)=>{Od(n)?t[r]=n.getValue(e):Ue(n)?t[r]=Oe(n):e||(t[r]=n)}),t}setValue(e){this.source=e,this.payload=this._makePayload(e)}reset(){this.payload&&J(this.payload,e=>e.reset())}_makePayload(e){if(e){const t=new Set;return st(e,this._addToPayload,t),Array.from(t)}}_addToPayload(e){Or.dependencies&&Ue(e)&&Or.dependencies.add(e);const t=no(e);t&&J(t,n=>this.add(n))}},jd=class ms extends oo{constructor(t){super(t)}static create(t){return new ms(t)}getValue(){return this.source.map(t=>t.getValue())}setValue(t){const n=this.getPayload();return t.length==n.length?n.map((r,o)=>r.setValue(t[o])).some(Boolean):(super.setValue(t.map(Ld)),!0)}};function Ld(e){return(to(e)?zr:ro).create(e)}function $o(e){const t=nt(e);return t?t.constructor:k.arr(e)?jd:to(e)?zr:ro}var Xa=(e,t)=>{const n=!k.fun(e)||e.prototype&&e.prototype.isReactComponent;return L.forwardRef((r,o)=>{const a=L.useRef(null),i=n&&L.useCallback(v=>{a.current=Bd(o,v)},[o]),[s,c]=Dd(r,t),l=ls(),u=()=>{const v=a.current;if(n&&!v)return;(v?t.applyAnimatedValues(v,s.getValue(!0)):!1)===!1&&l()},d=new Fd(u,c),p=L.useRef(void 0);ma(()=>(p.current=d,J(c,v=>mn(v,d)),()=>{p.current&&(J(p.current.deps,v=>Ln(v,p.current)),H.cancel(p.current.update))})),L.useEffect(u,[]),ds(()=>()=>{const v=p.current;J(v.deps,g=>Ln(g,v))});const m=t.getComponentProps(s.getValue());return L.createElement(e,{...m,ref:i})})},Fd=class{constructor(e,t){this.update=e,this.deps=t}eventObserved(e){e.type=="change"&&H.write(this.update)}};function Dd(e,t){const n=new Set;return Or.dependencies=n,e.style&&(e={...e,style:t.createAnimatedStyle(e.style)}),e=new oo(e),Or.dependencies=null,[e,n]}function Bd(e,t){return e&&(k.fun(e)?e(t):e.current=t),t}var Ja=Symbol.for("AnimatedComponent"),Vd=(e,{applyAnimatedValues:t=()=>!1,createAnimatedStyle:n=o=>new oo(o),getComponentProps:r=o=>o}={})=>{const o={applyAnimatedValues:t,createAnimatedStyle:n,getComponentProps:r},a=i=>{const s=ei(i)||"Anonymous";return k.str(i)?i=a[i]||(a[i]=Xa(i,o)):i=i[Ja]||(i[Ja]=Xa(i,o)),i.displayName=`Animated(${s})`,i};return st(e,(i,s)=>{k.arr(e)&&(s=ei(i)),a[s]=a(i)}),{animated:a}},ei=e=>k.str(e)?e:e&&k.str(e.displayName)?e.displayName:k.fun(e)&&e.name||null;function Dt(e,...t){return k.fun(e)?e(...t):e}var $n=(e,t)=>e===!0||!!(t&&e&&(k.fun(e)?e(t):Ve(e).includes(t))),fs=(e,t)=>k.obj(e)?t&&e[t]:e,gs=(e,t)=>e.default===!0?e[t]:e.default?e.default[t]:void 0,Hd=e=>e,ga=(e,t=Hd)=>{let n=Wd;e.default&&e.default!==!0&&(e=e.default,n=Object.keys(e));const r={};for(const o of n){const a=t(e[o],o);k.und(a)||(r[o]=a)}return r},Wd=["config","onProps","onStart","onChange","onPause","onResume","onRest"],Ud={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function qd(e){const t={};let n=0;if(st(e,(r,o)=>{Ud[o]||(t[o]=r,n++)}),n)return t}function vs(e){const t=qd(e);if(t){const n={to:t};return st(e,(r,o)=>o in t||(n[o]=r)),n}return{...e}}function Dn(e){return e=Oe(e),k.arr(e)?e.map(Dn):to(e)?Je.createStringInterpolator({range:[0,1],output:[e,e]})(1):e}function Gd(e){for(const t in e)return!0;return!1}function Po(e){return k.fun(e)||k.arr(e)&&k.obj(e[0])}function Zd(e,t){var n;(n=e.ref)==null||n.delete(e),t==null||t.delete(e)}function Yd(e,t){var n;t&&e.ref!==t&&((n=e.ref)==null||n.delete(e),t.add(e),e.ref=t)}var Kd={default:{tension:170,friction:26},gentle:{tension:120,friction:14},wobbly:{tension:180,friction:12},stiff:{tension:210,friction:20},slow:{tension:280,friction:60},molasses:{tension:280,friction:120}},To={...Kd.default,mass:1,damping:1,easing:kd.linear,clamp:!1},Qd=class{constructor(){this.velocity=0,Object.assign(this,To)}};function Xd(e,t,n){n&&(n={...n},ti(n,t),t={...n,...t}),ti(e,t),Object.assign(e,t);for(const i in To)e[i]==null&&(e[i]=To[i]);let{frequency:r,damping:o}=e;const{mass:a}=e;return k.und(r)||(r<.01&&(r=.01),o<0&&(o=0),e.tension=Math.pow(2*Math.PI/r,2)*a,e.friction=4*Math.PI*o*a/r),e}function ti(e,t){if(!k.und(t.decay))e.duration=void 0;else{const n=!k.und(t.tension)||!k.und(t.friction);(n||!k.und(t.frequency)||!k.und(t.damping)||!k.und(t.mass))&&(e.duration=void 0,e.decay=void 0),n&&(e.frequency=void 0)}}var ni=[],Jd=class{constructor(){this.changed=!1,this.values=ni,this.toValues=null,this.fromValues=ni,this.config=new Qd,this.immediate=!1}};function ws(e,{key:t,props:n,defaultProps:r,state:o,actions:a}){return new Promise((i,s)=>{let c,l,u=$n(n.cancel??(r==null?void 0:r.cancel),t);if(u)m();else{k.und(n.pause)||(o.paused=$n(n.pause,t));let v=r==null?void 0:r.pause;v!==!0&&(v=o.paused||$n(v,t)),c=Dt(n.delay||0,t),v?(o.resumeQueue.add(p),a.pause()):(a.resume(),p())}function d(){o.resumeQueue.add(p),o.timeouts.delete(l),l.cancel(),c=l.time-H.now()}function p(){c>0&&!Je.skipAnimation?(o.delayed=!0,l=H.setTimeout(m,c),o.pauseQueue.add(d),o.timeouts.add(l)):m()}function m(){o.delayed&&(o.delayed=!1),o.pauseQueue.delete(d),o.timeouts.delete(l),e<=(o.cancelId||0)&&(u=!0);try{a.start({...n,callId:e,cancel:u},i)}catch(v){s(v)}}})}var va=(e,t)=>t.length==1?t[0]:t.some(n=>n.cancelled)?nn(e.get()):t.every(n=>n.noop)?bs(e.get()):Ze(e.get(),t.every(n=>n.finished)),bs=e=>({value:e,noop:!0,finished:!0,cancelled:!1}),Ze=(e,t,n=!1)=>({value:e,finished:t,cancelled:n}),nn=e=>({value:e,cancelled:!0,finished:!1});function ys(e,t,n,r){const{callId:o,parentId:a,onRest:i}=t,{asyncTo:s,promise:c}=n;return!a&&e===s&&!t.reset?c:n.promise=(async()=>{n.asyncId=o,n.asyncTo=e;const l=ga(t,(w,b)=>b==="onRest"?void 0:w);let u,d;const p=new Promise((w,b)=>(u=w,d=b)),m=w=>{const b=o<=(n.cancelId||0)&&nn(r)||o!==n.asyncId&&Ze(r,!1);if(b)throw w.result=b,d(w),w},v=(w,b)=>{const y=new ri,S=new oi;return(async()=>{if(Je.skipAnimation)throw Bn(n),S.result=Ze(r,!1),d(S),S;m(y);const M=k.obj(w)?{...w}:{...b,to:w};M.parentId=o,st(l,(re,Z)=>{k.und(M[Z])&&(M[Z]=re)});const O=await r.start(M);return m(y),n.paused&&await new Promise(re=>{n.resumeQueue.add(re)}),O})()};let g;if(Je.skipAnimation)return Bn(n),Ze(r,!1);try{let w;k.arr(e)?w=(async b=>{for(const y of b)await v(y)})(e):w=Promise.resolve(e(v,r.stop.bind(r))),await Promise.all([w.then(u),p]),g=Ze(r.get(),!0,!1)}catch(w){if(w instanceof ri)g=w.result;else if(w instanceof oi)g=w.result;else throw w}finally{o==n.asyncId&&(n.asyncId=a,n.asyncTo=a?s:void 0,n.promise=a?c:void 0)}return k.fun(i)&&H.batchedUpdates(()=>{i(g,r,r.item)}),g})()}function Bn(e,t){Mn(e.timeouts,n=>n.cancel()),e.pauseQueue.clear(),e.resumeQueue.clear(),e.asyncId=e.asyncTo=e.promise=void 0,t&&(e.cancelId=t)}var ri=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},oi=class extends Error{constructor(){super("SkipAnimationSignal")}},Ro=e=>e instanceof wa,eu=1,wa=class extends os{constructor(){super(...arguments),this.id=eu++,this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){const e=nt(this);return e&&e.getValue()}to(...e){return Je.to(this,e)}interpolate(...e){return $d(),Je.to(this,e)}toJSON(){return this.get()}observerAdded(e){e==1&&this._attach()}observerRemoved(e){e==0&&this._detach()}_attach(){}_detach(){}_onChange(e,t=!1){jn(this,{type:"change",parent:this,value:e,idle:t})}_onPriorityChange(e){this.idle||Xr.sort(this),jn(this,{type:"priority",parent:this,priority:e})}},Ut=Symbol.for("SpringPhase"),_s=1,zo=2,Oo=4,mo=e=>(e[Ut]&_s)>0,_t=e=>(e[Ut]&zo)>0,yn=e=>(e[Ut]&Oo)>0,ai=(e,t)=>t?e[Ut]|=zo|_s:e[Ut]&=~zo,ii=(e,t)=>t?e[Ut]|=Oo:e[Ut]&=~Oo,tu=class extends wa{constructor(e,t){if(super(),this.animation=new Jd,this.defaultProps={},this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._pendingCalls=new Set,this._lastCallId=0,this._lastToId=0,this._memoizedDuration=0,!k.und(e)||!k.und(t)){const n=k.obj(e)?{...e}:{...t,from:e};k.und(n.default)&&(n.default=!0),this.start(n)}}get idle(){return!(_t(this)||this._state.asyncTo)||yn(this)}get goal(){return Oe(this.animation.to)}get velocity(){const e=nt(this);return e instanceof ro?e.lastVelocity||0:e.getPayload().map(t=>t.lastVelocity||0)}get hasAnimated(){return mo(this)}get isAnimating(){return _t(this)}get isPaused(){return yn(this)}get isDelayed(){return this._state.delayed}advance(e){let t=!0,n=!1;const r=this.animation;let{toValues:o}=r;const{config:a}=r,i=no(r.to);!i&&Ue(r.to)&&(o=Ve(Oe(r.to))),r.values.forEach((l,u)=>{if(l.done)return;const d=l.constructor==zr?1:i?i[u].lastPosition:o[u];let p=r.immediate,m=d;if(!p){if(m=l.lastPosition,a.tension<=0){l.done=!0;return}let v=l.elapsedTime+=e;const g=r.fromValues[u],w=l.v0!=null?l.v0:l.v0=k.arr(a.velocity)?a.velocity[u]:a.velocity;let b;const y=a.precision||(g==d?.005:Math.min(1,Math.abs(d-g)*.001));if(k.und(a.duration))if(a.decay){const S=a.decay===!0?.998:a.decay,M=Math.exp(-(1-S)*v);m=g+w/(1-S)*(1-M),p=Math.abs(l.lastPosition-m)<=y,b=w*M}else{b=l.lastVelocity==null?w:l.lastVelocity;const S=a.restVelocity||y/10,M=a.clamp?0:a.bounce,O=!k.und(M),re=g==d?l.v0>0:g<d;let Z,I=!1;const Q=1,Ce=Math.ceil(e/Q);for(let te=0;te<Ce&&(Z=Math.abs(b)>S,!(!Z&&(p=Math.abs(d-m)<=y,p)));++te){O&&(I=m==d||m>d==re,I&&(b=-b*M,m=d));const ye=-a.tension*1e-6*(m-d),B=-a.friction*.001*b,U=(ye+B)/a.mass;b=b+U*Q,m=m+b*Q}}else{let S=1;a.duration>0&&(this._memoizedDuration!==a.duration&&(this._memoizedDuration=a.duration,l.durationProgress>0&&(l.elapsedTime=a.duration*l.durationProgress,v=l.elapsedTime+=e)),S=(a.progress||0)+v/this._memoizedDuration,S=S>1?1:S<0?0:S,l.durationProgress=S),m=g+a.easing(S)*(d-g),b=(m-l.lastPosition)/e,p=S==1}l.lastVelocity=b,Number.isNaN(m)&&(console.warn("Got NaN while animating:",this),p=!0)}i&&!i[u].done&&(p=!1),p?l.done=!0:t=!1,l.setValue(m,a.round)&&(n=!0)});const s=nt(this),c=s.getValue();if(t){const l=Oe(r.to);(c!==l||n)&&!a.decay?(s.setValue(l),this._onChange(l)):n&&a.decay&&this._onChange(c),this._stop()}else n&&this._onChange(c)}set(e){return H.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(_t(this)){const{to:e,config:t}=this.animation;H.batchedUpdates(()=>{this._onStart(),t.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,t){let n;return k.und(e)?(n=this.queue||[],this.queue=[]):n=[k.obj(e)?e:{...t,to:e}],Promise.all(n.map(r=>this._update(r))).then(r=>va(this,r))}stop(e){const{to:t}=this.animation;return this._focus(this.get()),Bn(this._state,e&&this._lastCallId),H.batchedUpdates(()=>this._stop(t,e)),this}reset(){this._update({reset:!0})}eventObserved(e){e.type=="change"?this._start():e.type=="priority"&&(this.priority=e.priority+1)}_prepareNode(e){const t=this.key||"";let{to:n,from:r}=e;n=k.obj(n)?n[t]:n,(n==null||Po(n))&&(n=void 0),r=k.obj(r)?r[t]:r,r==null&&(r=void 0);const o={to:n,from:r};return mo(this)||(e.reverse&&([n,r]=[r,n]),r=Oe(r),k.und(r)?nt(this)||this._set(n):this._set(r)),o}_update({...e},t){const{key:n,defaultProps:r}=this;e.default&&Object.assign(r,ga(e,(i,s)=>/^on/.test(s)?fs(i,n):i)),ci(this,e,"onProps"),xn(this,"onProps",e,this);const o=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");const a=this._state;return ws(++this._lastCallId,{key:n,props:e,defaultProps:r,state:a,actions:{pause:()=>{yn(this)||(ii(this,!0),Sn(a.pauseQueue),xn(this,"onPause",Ze(this,_n(this,this.animation.to)),this))},resume:()=>{yn(this)&&(ii(this,!1),_t(this)&&this._resume(),Sn(a.resumeQueue),xn(this,"onResume",Ze(this,_n(this,this.animation.to)),this))},start:this._merge.bind(this,o)}}).then(i=>{if(e.loop&&i.finished&&!(t&&i.noop)){const s=xs(e);if(s)return this._update(s,!0)}return i})}_merge(e,t,n){if(t.cancel)return this.stop(!0),n(nn(this));const r=!k.und(e.to),o=!k.und(e.from);if(r||o)if(t.callId>this._lastToId)this._lastToId=t.callId;else return n(nn(this));const{key:a,defaultProps:i,animation:s}=this,{to:c,from:l}=s;let{to:u=c,from:d=l}=e;o&&!r&&(!t.default||k.und(u))&&(u=d),t.reverse&&([u,d]=[d,u]);const p=!mt(d,l);p&&(s.from=d),d=Oe(d);const m=!mt(u,c);m&&this._focus(u);const v=Po(t.to),{config:g}=s,{decay:w,velocity:b}=g;(r||o)&&(g.velocity=0),t.config&&!v&&Xd(g,Dt(t.config,a),t.config!==i.config?Dt(i.config,a):void 0);let y=nt(this);if(!y||k.und(u))return n(Ze(this,!0));const S=k.und(t.reset)?o&&!t.default:!k.und(d)&&$n(t.reset,a),M=S?d:this.get(),O=Dn(u),re=k.num(O)||k.arr(O)||to(O),Z=!v&&(!re||$n(i.immediate||t.immediate,a));if(m){const te=$o(u);if(te!==y.constructor)if(Z)y=this._set(O);else throw Error(`Cannot animate between ${y.constructor.name} and ${te.name}, as the "to" prop suggests`)}const I=y.constructor;let Q=Ue(u),Ce=!1;if(!Q){const te=S||!mo(this)&&p;(m||te)&&(Ce=mt(Dn(M),O),Q=!Ce),(!mt(s.immediate,Z)&&!Z||!mt(g.decay,w)||!mt(g.velocity,b))&&(Q=!0)}if(Ce&&_t(this)&&(s.changed&&!S?Q=!0:Q||this._stop(c)),!v&&((Q||Ue(c))&&(s.values=y.getPayload(),s.toValues=Ue(u)?null:I==zr?[1]:Ve(O)),s.immediate!=Z&&(s.immediate=Z,!Z&&!S&&this._set(c)),Q)){const{onRest:te}=s;J(ru,B=>ci(this,t,B));const ye=Ze(this,_n(this,c));Sn(this._pendingCalls,ye),this._pendingCalls.add(n),s.changed&&H.batchedUpdates(()=>{var B;s.changed=!S,te==null||te(ye,this),S?Dt(i.onRest,ye):(B=s.onStart)==null||B.call(s,ye,this)})}S&&this._set(M),v?n(ys(t.to,t,this._state,this)):Q?this._start():_t(this)&&!m?this._pendingCalls.add(n):n(bs(M))}_focus(e){const t=this.animation;e!==t.to&&(Ya(this)&&this._detach(),t.to=e,Ya(this)&&this._attach())}_attach(){let e=0;const{to:t}=this.animation;Ue(t)&&(mn(t,this),Ro(t)&&(e=t.priority+1)),this.priority=e}_detach(){const{to:e}=this.animation;Ue(e)&&Ln(e,this)}_set(e,t=!0){const n=Oe(e);if(!k.und(n)){const r=nt(this);if(!r||!mt(n,r.getValue())){const o=$o(n);!r||r.constructor!=o?fa(this,o.create(n)):r.setValue(n),r&&H.batchedUpdates(()=>{this._onChange(n,t)})}}return nt(this)}_onStart(){const e=this.animation;e.changed||(e.changed=!0,xn(this,"onStart",Ze(this,_n(this,e.to)),this))}_onChange(e,t){t||(this._onStart(),Dt(this.animation.onChange,e,this)),Dt(this.defaultProps.onChange,e,this),super._onChange(e,t)}_start(){const e=this.animation;nt(this).reset(Oe(e.to)),e.immediate||(e.fromValues=e.values.map(t=>t.lastPosition)),_t(this)||(ai(this,!0),yn(this)||this._resume())}_resume(){Je.skipAnimation?this.finish():Xr.start(this)}_stop(e,t){if(_t(this)){ai(this,!1);const n=this.animation;J(n.values,o=>{o.done=!0}),n.toValues&&(n.onChange=n.onPause=n.onResume=void 0),jn(this,{type:"idle",parent:this});const r=t?nn(this.get()):Ze(this.get(),_n(this,e??n.to));Sn(this._pendingCalls,r),n.changed&&(n.changed=!1,xn(this,"onRest",r,this))}}};function _n(e,t){const n=Dn(t),r=Dn(e.get());return mt(r,n)}function xs(e,t=e.loop,n=e.to){const r=Dt(t);if(r){const o=r!==!0&&vs(r),a=(o||e).reverse,i=!o||o.reset;return Vn({...e,loop:t,default:!1,pause:void 0,to:!a||Po(n)?n:void 0,from:i?e.from:void 0,reset:i,...o})}}function Vn(e){const{to:t,from:n}=e=vs(e),r=new Set;return k.obj(t)&&si(t,r),k.obj(n)&&si(n,r),e.keys=r.size?Array.from(r):null,e}function nu(e){const t=Vn(e);return k.und(t.default)&&(t.default=ga(t)),t}function si(e,t){st(e,(n,r)=>n!=null&&t.add(r))}var ru=["onStart","onRest","onChange","onPause","onResume"];function ci(e,t,n){e.animation[n]=t[n]!==gs(t,n)?fs(t[n],e.key):void 0}function xn(e,t,...n){var r,o,a,i;(o=(r=e.animation)[t])==null||o.call(r,...n),(i=(a=e.defaultProps)[t])==null||i.call(a,...n)}var ou=["onStart","onChange","onRest"],au=1,iu=class{constructor(e,t){this.id=au++,this.springs={},this.queue=[],this._lastAsyncId=0,this._active=new Set,this._changed=new Set,this._started=!1,this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._events={onStart:new Map,onChange:new Map,onRest:new Map},this._onFrame=this._onFrame.bind(this),t&&(this._flush=t),e&&this.start({default:!0,...e})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(e=>e.idle&&!e.isDelayed&&!e.isPaused)}get item(){return this._item}set item(e){this._item=e}get(){const e={};return this.each((t,n)=>e[n]=t.get()),e}set(e){for(const t in e){const n=e[t];k.und(n)||this.springs[t].set(n)}}update(e){return e&&this.queue.push(Vn(e)),this}start(e){let{queue:t}=this;return e?t=Ve(e).map(Vn):this.queue=[],this._flush?this._flush(this,t):(Ns(this,t),jo(this,t))}stop(e,t){if(e!==!!e&&(t=e),t){const n=this.springs;J(Ve(t),r=>n[r].stop(!!e))}else Bn(this._state,this._lastAsyncId),this.each(n=>n.stop(!!e));return this}pause(e){if(k.und(e))this.start({pause:!0});else{const t=this.springs;J(Ve(e),n=>t[n].pause())}return this}resume(e){if(k.und(e))this.start({pause:!1});else{const t=this.springs;J(Ve(e),n=>t[n].resume())}return this}each(e){st(this.springs,e)}_onFrame(){const{onStart:e,onChange:t,onRest:n}=this._events,r=this._active.size>0,o=this._changed.size>0;(r&&!this._started||o&&!this._started)&&(this._started=!0,Mn(e,([s,c])=>{c.value=this.get(),s(c,this,this._item)}));const a=!r&&this._started,i=o||a&&n.size?this.get():null;o&&t.size&&Mn(t,([s,c])=>{c.value=i,s(c,this,this._item)}),a&&(this._started=!1,Mn(n,([s,c])=>{c.value=i,s(c,this,this._item)}))}eventObserved(e){if(e.type=="change")this._changed.add(e.parent),e.idle||this._active.add(e.parent);else if(e.type=="idle")this._active.delete(e.parent);else return;H.onFrame(this._onFrame)}};function jo(e,t){return Promise.all(t.map(n=>ks(e,n))).then(n=>va(e,n))}async function ks(e,t,n){const{keys:r,to:o,from:a,loop:i,onRest:s,onResolve:c}=t,l=k.obj(t.default)&&t.default;i&&(t.loop=!1),o===!1&&(t.to=null),a===!1&&(t.from=null);const u=k.arr(o)||k.fun(o)?o:void 0;u?(t.to=void 0,t.onRest=void 0,l&&(l.onRest=void 0)):J(ou,g=>{const w=t[g];if(k.fun(w)){const b=e._events[g];t[g]=({finished:y,cancelled:S})=>{const M=b.get(w);M?(y||(M.finished=!1),S&&(M.cancelled=!0)):b.set(w,{value:null,finished:y||!1,cancelled:S||!1})},l&&(l[g]=t[g])}});const d=e._state;t.pause===!d.paused?(d.paused=t.pause,Sn(t.pause?d.pauseQueue:d.resumeQueue)):d.paused&&(t.pause=!0);const p=(r||Object.keys(e.springs)).map(g=>e.springs[g].start(t)),m=t.cancel===!0||gs(t,"cancel")===!0;(u||m&&d.asyncId)&&p.push(ws(++e._lastAsyncId,{props:t,state:d,actions:{pause:Mo,resume:Mo,start(g,w){m?(Bn(d,e._lastAsyncId),w(nn(e))):(g.onRest=s,w(ys(u,g,d,e)))}}})),d.paused&&await new Promise(g=>{d.resumeQueue.add(g)});const v=va(e,await Promise.all(p));if(i&&v.finished&&!(n&&v.noop)){const g=xs(t,i,o);if(g)return Ns(e,[g]),ks(e,g,!0)}return c&&H.batchedUpdates(()=>c(v,e,e.item)),v}function li(e,t){const n={...e.springs};return t&&J(Ve(t),r=>{k.und(r.keys)&&(r=Vn(r)),k.obj(r.to)||(r={...r,to:void 0}),Es(n,r,o=>Cs(o))}),Ss(e,n),n}function Ss(e,t){st(t,(n,r)=>{e.springs[r]||(e.springs[r]=n,mn(n,e))})}function Cs(e,t){const n=new tu;return n.key=e,t&&mn(n,t),n}function Es(e,t,n){t.keys&&J(t.keys,r=>{(e[r]||(e[r]=n(r)))._prepareNode(t)})}function Ns(e,t){J(t,n=>{Es(e.springs,n,r=>Cs(r,e))})}var su=L.createContext({pause:!1,immediate:!1}),cu=()=>{const e=[],t=function(r){Td();const o=[];return J(e,(a,i)=>{if(k.und(r))o.push(a.start());else{const s=n(r,a,i);s&&o.push(a.start(s))}}),o};t.current=e,t.add=function(r){e.includes(r)||e.push(r)},t.delete=function(r){const o=e.indexOf(r);~o&&e.splice(o,1)},t.pause=function(){return J(e,r=>r.pause(...arguments)),this},t.resume=function(){return J(e,r=>r.resume(...arguments)),this},t.set=function(r){J(e,(o,a)=>{const i=k.fun(r)?r(a,o):r;i&&o.set(i)})},t.start=function(r){const o=[];return J(e,(a,i)=>{if(k.und(r))o.push(a.start());else{const s=this._getProps(r,a,i);s&&o.push(a.start(s))}}),o},t.stop=function(){return J(e,r=>r.stop(...arguments)),this},t.update=function(r){return J(e,(o,a)=>o.update(this._getProps(r,o,a))),this};const n=function(r,o,a){return k.fun(r)?r(a,o):r};return t._getProps=n,t};function lu(e,t,n){const r=k.fun(t)&&t;r&&!n&&(n=[]);const o=L.useMemo(()=>r||arguments.length==3?cu():void 0,[]),a=L.useRef(0),i=ls(),s=L.useMemo(()=>({ctrls:[],queue:[],flush(b,y){const S=li(b,y);return a.current>0&&!s.queue.length&&!Object.keys(S).some(O=>!b.springs[O])?jo(b,y):new Promise(O=>{Ss(b,S),s.queue.push(()=>{O(jo(b,y))}),i()})}}),[]),c=L.useRef([...s.ctrls]),l=L.useRef([]),u=Qa(e)||0;L.useMemo(()=>{J(c.current.slice(e,u),b=>{Zd(b,o),b.stop(!0)}),c.current.length=e,d(u,e)},[e]),L.useMemo(()=>{d(0,Math.min(u,e))},n);function d(b,y){for(let S=b;S<y;S++){const M=c.current[S]||(c.current[S]=new iu(null,s.flush)),O=r?r(S,M):t[S];O&&(l.current[S]=nu(O))}}const p=c.current.map((b,y)=>li(b,l.current[y])),m=L.useContext(su),v=Qa(m),g=m!==v&&Gd(m);ma(()=>{a.current++,s.ctrls=c.current;const{queue:b}=s;b.length&&(s.queue=[],J(b,y=>y())),J(c.current,(y,S)=>{o==null||o.add(y),g&&y.start({default:m});const M=l.current[S];M&&(Yd(y,M.ref),y.ref?y.queue.push(M):y.start(M))})}),ds(()=>()=>{J(s.ctrls,b=>b.stop(!0))});const w=p.map(b=>({...b}));return o?[w,o]:w}function du(e,t){const n=k.fun(e),[[r],o]=lu(1,n?e:[e],n?t||[]:t);return n||arguments.length==2?[r,o]:r}var uu=class extends wa{constructor(e,t){super(),this.source=e,this.idle=!0,this._active=new Set,this.calc=zn(...t);const n=this._get(),r=$o(n);fa(this,r.create(n))}advance(e){const t=this._get(),n=this.get();mt(t,n)||(nt(this).setValue(t),this._onChange(t,this.idle)),!this.idle&&di(this._active)&&fo(this)}_get(){const e=k.arr(this.source)?this.source.map(Oe):Ve(Oe(this.source));return this.calc(...e)}_start(){this.idle&&!di(this._active)&&(this.idle=!1,J(no(this),e=>{e.done=!1}),Je.skipAnimation?(H.batchedUpdates(()=>this.advance()),fo(this)):Xr.start(this))}_attach(){let e=1;J(Ve(this.source),t=>{Ue(t)&&mn(t,this),Ro(t)&&(t.idle||this._active.add(t),e=Math.max(e,t.priority+1))}),this.priority=e,this._start()}_detach(){J(Ve(this.source),e=>{Ue(e)&&Ln(e,this)}),this._active.clear(),fo(this)}eventObserved(e){e.type=="change"?e.idle?this.advance():(this._active.add(e.parent),this._start()):e.type=="idle"?this._active.delete(e.parent):e.type=="priority"&&(this.priority=Ve(this.source).reduce((t,n)=>Math.max(t,(Ro(n)?n.priority:0)+1),0))}};function pu(e){return e.idle!==!1}function di(e){return!e.size||Array.from(e).every(pu)}function fo(e){e.idle||(e.idle=!0,J(no(e),t=>{t.done=!0}),jn(e,{type:"idle",parent:e}))}Je.assign({createStringInterpolator:ss,to:(e,t)=>new uu(e,t)});var As=/^--/;function hu(e,t){return t==null||typeof t=="boolean"||t===""?"":typeof t=="number"&&t!==0&&!As.test(e)&&!(Pn.hasOwnProperty(e)&&Pn[e])?t+"px":(""+t).trim()}var ui={};function mu(e,t){if(!e.nodeType||!e.setAttribute)return!1;const n=e.nodeName==="filter"||e.parentNode&&e.parentNode.nodeName==="filter",{className:r,style:o,children:a,scrollTop:i,scrollLeft:s,viewBox:c,...l}=t,u=Object.values(l),d=Object.keys(l).map(p=>n||e.hasAttribute(p)?p:ui[p]||(ui[p]=p.replace(/([A-Z])/g,m=>"-"+m.toLowerCase())));a!==void 0&&(e.textContent=a);for(const p in o)if(o.hasOwnProperty(p)){const m=hu(p,o[p]);As.test(p)?e.style.setProperty(p,m):e.style[p]=m}d.forEach((p,m)=>{e.setAttribute(p,u[m])}),r!==void 0&&(e.className=r),i!==void 0&&(e.scrollTop=i),s!==void 0&&(e.scrollLeft=s),c!==void 0&&e.setAttribute("viewBox",c)}var Pn={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},fu=(e,t)=>e+t.charAt(0).toUpperCase()+t.substring(1),gu=["Webkit","Ms","Moz","O"];Pn=Object.keys(Pn).reduce((e,t)=>(gu.forEach(n=>e[fu(n,t)]=e[t]),e),Pn);var vu=/^(matrix|translate|scale|rotate|skew)/,wu=/^(translate)/,bu=/^(rotate|skew)/,go=(e,t)=>k.num(e)&&e!==0?e+t:e,Mr=(e,t)=>k.arr(e)?e.every(n=>Mr(n,t)):k.num(e)?e===t:parseFloat(e)===t,yu=class extends oo{constructor({x:e,y:t,z:n,...r}){const o=[],a=[];(e||t||n)&&(o.push([e||0,t||0,n||0]),a.push(i=>[`translate3d(${i.map(s=>go(s,"px")).join(",")})`,Mr(i,0)])),st(r,(i,s)=>{if(s==="transform")o.push([i||""]),a.push(c=>[c,c===""]);else if(vu.test(s)){if(delete r[s],k.und(i))return;const c=wu.test(s)?"px":bu.test(s)?"deg":"";o.push(Ve(i)),a.push(s==="rotate3d"?([l,u,d,p])=>[`rotate3d(${l},${u},${d},${go(p,c)})`,Mr(p,0)]:l=>[`${s}(${l.map(u=>go(u,c)).join(",")})`,Mr(l,s.startsWith("scale")?1:0)])}}),o.length&&(r.transform=new _u(o,a)),super(r)}},_u=class extends os{constructor(e,t){super(),this.inputs=e,this.transforms=t,this._value=null}get(){return this._value||(this._value=this._get())}_get(){let e="",t=!0;return J(this.inputs,(n,r)=>{const o=Oe(n[0]),[a,i]=this.transforms[r](k.arr(o)?o:n.map(Oe));e+=" "+a,t=t&&i}),t?"none":e}observerAdded(e){e==1&&J(this.inputs,t=>J(t,n=>Ue(n)&&mn(n,this)))}observerRemoved(e){e==0&&J(this.inputs,t=>J(t,n=>Ue(n)&&Ln(n,this)))}eventObserved(e){e.type=="change"&&(this._value=null),jn(this,e)}},xu=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"];Je.assign({batchedUpdates:qi.unstable_batchedUpdates,createStringInterpolator:ss,colors:dd});var ku=Vd(xu,{applyAnimatedValues:mu,createAnimatedStyle:e=>new yu(e),getComponentProps:({scrollTop:e,scrollLeft:t,...n})=>n}),Su=ku.animated;const Ms=e=>{const n={"Cloud & DevOps":ml,Backend:fl,Frontend:Fa,"AI & ML":gl,Architecture:vl,Tools:wl,Analytics:bl,"System Design":yl}[e]||Fa;return f.jsx(n,{})},Cu=({skill:e,category:t})=>{const[n,r]=du(()=>({xys:[0,0,1],config:{mass:5,tension:350,friction:40}})),o=(i,s)=>[-(s-window.innerHeight/2)/20,(i-window.innerWidth/2)/20,1.1],a=(i,s,c)=>`perspective(600px) rotateX(${i}deg) rotateY(${s}deg) scale(${c})`;return f.jsxs(Su.div,{className:"skill-card",onMouseMove:({clientX:i,clientY:s})=>r.start({xys:o(i,s)}),onMouseLeave:()=>r.start({xys:[0,0,1]}),style:{transform:n.xys.to(a)},children:[f.jsx("div",{className:"skill-icon",children:Ms(t)}),f.jsx("h3",{children:e})]})},Eu=()=>{const e=Qe.getFormattedSkills(),[t,n]=L.useState(Object.keys(e)[0]),[r,o]=L.useState(0),a=L.useRef(null),i=6,s=e[t]||[],c=Math.ceil(s.length/i),l=()=>{o(p=>(p+1)%c)},u=()=>{o(p=>(p-1+c)%c)},d=s.slice(r*i,(r+1)*i).map(p=>({skill:p}));return f.jsx("section",{id:"skills",className:"skills-section","aria-labelledby":"skills-title",children:f.jsxs("div",{className:"container",children:[f.jsx("h2",{id:"skills-title",className:"section-title",children:"Technical Skills"}),f.jsx("p",{className:"section-subtitle",children:"Explore my technical expertise across different domains"}),f.jsxs("div",{className:"skills-container",ref:a,children:[f.jsx("div",{className:"category-tabs",children:Object.keys(e).map(p=>f.jsxs("button",{className:`category-tab ${t===p?"active":""}`,onClick:()=>{n(p),o(0)},children:[Ms(p),f.jsx("span",{children:p})]},p))}),f.jsx("div",{className:"skills-grid",children:d.map(({skill:p,proficiency:m},v)=>f.jsx(Cu,{skill:p,category:t},`${p}-${v}`))}),c>1&&f.jsxs("div",{className:"pagination-controls",children:[f.jsx("button",{className:"nav-button prev",onClick:u,disabled:r===0,children:f.jsx(pl,{})}),f.jsxs("span",{className:"page-indicator",children:[r+1," / ",c]}),f.jsx("button",{className:"nav-button next",onClick:l,disabled:r===c-1,children:f.jsx(hl,{})})]})]})]})})};const Nu=()=>{Qe.getEducation();const e=Qe.getFormattedCertificates(),t=Qe.getAwards();return f.jsx("section",{id:"education",className:"education",children:f.jsxs("div",{className:"container",children:[f.jsx("h2",{className:"section-title",children:"Certifications & Awards"}),f.jsxs("div",{className:"education-grid",children:[f.jsx(rt,{delay:.2,children:f.jsxs("div",{className:"education-card",children:[f.jsx("h3",{className:"card-title",children:"Certifications"}),f.jsx("ul",{className:"certification-list",children:e.map((n,r)=>f.jsxs("li",{children:[f.jsx("strong",{children:n.name}),n.issuer&&f.jsxs("span",{children:[" - ",n.issuer]}),n.date&&f.jsxs("span",{className:"cert-date",children:[" (",n.date,")"]})]},r))})]})}),f.jsx(rt,{delay:.4,children:f.jsxs("div",{className:"education-card",children:[f.jsx("h3",{className:"card-title",children:"Awards"}),t.map((n,r)=>f.jsxs("div",{className:"award-item",children:[f.jsx("h4",{children:n.title}),f.jsx("p",{children:n.summary}),n.date&&f.jsx("span",{className:"award-date",children:n.date})]},r))]})})]})]})})};const Au=()=>{const e=Qe.getFormattedPublications();return f.jsx("section",{id:"publications",className:"publications",children:f.jsxs("div",{className:"container",children:[f.jsx("h2",{className:"section-title",children:"Publications & Thought Leadership"}),f.jsx("div",{className:"publications-grid",children:e.map((t,n)=>f.jsx(rt,{delay:n*.1,children:f.jsxs("div",{className:"publication-item",children:[f.jsx("h3",{children:t.name}),t.publisher&&f.jsx("p",{className:"publisher",children:t.publisher}),t.url&&f.jsx("a",{href:t.url,target:"_blank",rel:"noopener noreferrer",className:"publication-link",children:"Read More"})]})},n))})]})})},Mu=()=>f.jsx("footer",{className:"footer",children:f.jsx("div",{className:"container",children:f.jsx("p",{children:"© 2025 Nicholas Gerasimatos. All rights reserved."})})});var sr,q,Is,Bt,pi,$s,Ps,Ts,ba,Lo,Fo,Rs,Hn={},zs=[],Iu=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,cr=Array.isArray;function ot(e,t){for(var n in t)e[n]=t[n];return e}function ya(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function Le(e,t,n){var r,o,a,i={};for(a in t)a=="key"?r=t[a]:a=="ref"?o=t[a]:i[a]=t[a];if(arguments.length>2&&(i.children=arguments.length>3?sr.call(arguments,2):n),typeof e=="function"&&e.defaultProps!=null)for(a in e.defaultProps)i[a]==null&&(i[a]=e.defaultProps[a]);return Tn(e,i,r,o,null)}function Tn(e,t,n,r,o){var a={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:o??++Is,__i:-1,__u:0};return o==null&&q.vnode!=null&&q.vnode(a),a}function Do(){return{current:null}}function je(e){return e.children}function at(e,t){this.props=e,this.context=t}function sn(e,t){if(t==null)return e.__?sn(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if((n=e.__k[t])!=null&&n.__e!=null)return n.__e;return typeof e.type=="function"?sn(e):null}function Os(e){var t,n;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((n=e.__k[t])!=null&&n.__e!=null){e.__e=e.__c.base=n.__e;break}return Os(e)}}function Bo(e){(!e.__d&&(e.__d=!0)&&Bt.push(e)&&!jr.__r++||pi!=q.debounceRendering)&&((pi=q.debounceRendering)||$s)(jr)}function jr(){for(var e,t,n,r,o,a,i,s=1;Bt.length;)Bt.length>s&&Bt.sort(Ps),e=Bt.shift(),s=Bt.length,e.__d&&(n=void 0,o=(r=(t=e).__v).__e,a=[],i=[],t.__P&&((n=ot({},r)).__v=r.__v+1,q.vnode&&q.vnode(n),_a(t.__P,n,r,t.__n,t.__P.namespaceURI,32&r.__u?[o]:null,a,o??sn(r),!!(32&r.__u),i),n.__v=r.__v,n.__.__k[n.__i]=n,Fs(a,n,i),n.__e!=o&&Os(n)));jr.__r=0}function js(e,t,n,r,o,a,i,s,c,l,u){var d,p,m,v,g,w,b=r&&r.__k||zs,y=t.length;for(c=$u(n,t,b,c,y),d=0;d<y;d++)(m=n.__k[d])!=null&&(p=m.__i==-1?Hn:b[m.__i]||Hn,m.__i=d,w=_a(e,m,p,o,a,i,s,c,l,u),v=m.__e,m.ref&&p.ref!=m.ref&&(p.ref&&xa(p.ref,null,m),u.push(m.ref,m.__c||v,m)),g==null&&v!=null&&(g=v),4&m.__u||p.__k===m.__k?c=Ls(m,c,e):typeof m.type=="function"&&w!==void 0?c=w:v&&(c=v.nextSibling),m.__u&=-7);return n.__e=g,c}function $u(e,t,n,r,o){var a,i,s,c,l,u=n.length,d=u,p=0;for(e.__k=new Array(o),a=0;a<o;a++)(i=t[a])!=null&&typeof i!="boolean"&&typeof i!="function"?(c=a+p,(i=e.__k[a]=typeof i=="string"||typeof i=="number"||typeof i=="bigint"||i.constructor==String?Tn(null,i,null,null,null):cr(i)?Tn(je,{children:i},null,null,null):i.constructor==null&&i.__b>0?Tn(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,s=null,(l=i.__i=Pu(i,n,c,d))!=-1&&(d--,(s=n[l])&&(s.__u|=2)),s==null||s.__v==null?(l==-1&&(o>u?p--:o<u&&p++),typeof i.type!="function"&&(i.__u|=4)):l!=c&&(l==c-1?p--:l==c+1?p++:(l>c?p--:p++,i.__u|=4))):e.__k[a]=null;if(d)for(a=0;a<u;a++)(s=n[a])!=null&&!(2&s.__u)&&(s.__e==r&&(r=sn(s)),Bs(s,s));return r}function Ls(e,t,n){var r,o;if(typeof e.type=="function"){for(r=e.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=e,t=Ls(r[o],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=sn(e)),n.insertBefore(e.__e,t||null),t=e.__e);do t=t&&t.nextSibling;while(t!=null&&t.nodeType==8);return t}function gt(e,t){return t=t||[],e==null||typeof e=="boolean"||(cr(e)?e.some(function(n){gt(n,t)}):t.push(e)),t}function Pu(e,t,n,r){var o,a,i=e.key,s=e.type,c=t[n];if(c===null&&e.key==null||c&&i==c.key&&s==c.type&&!(2&c.__u))return n;if(r>(c!=null&&!(2&c.__u)?1:0))for(o=n-1,a=n+1;o>=0||a<t.length;){if(o>=0){if((c=t[o])&&!(2&c.__u)&&i==c.key&&s==c.type)return o;o--}if(a<t.length){if((c=t[a])&&!(2&c.__u)&&i==c.key&&s==c.type)return a;a++}}return-1}function hi(e,t,n){t[0]=="-"?e.setProperty(t,n??""):e[t]=n==null?"":typeof n!="number"||Iu.test(t)?n:n+"px"}function _r(e,t,n,r,o){var a;e:if(t=="style")if(typeof n=="string")e.style.cssText=n;else{if(typeof r=="string"&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||hi(e.style,t,"");if(n)for(t in n)r&&n[t]==r[t]||hi(e.style,t,n[t])}else if(t[0]=="o"&&t[1]=="n")a=t!=(t=t.replace(Ts,"$1")),t=t.toLowerCase()in e||t=="onFocusOut"||t=="onFocusIn"?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=n,n?r?n.u=r.u:(n.u=ba,e.addEventListener(t,a?Fo:Lo,a)):e.removeEventListener(t,a?Fo:Lo,a);else{if(o=="http://www.w3.org/2000/svg")t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!="width"&&t!="height"&&t!="href"&&t!="list"&&t!="form"&&t!="tabIndex"&&t!="download"&&t!="rowSpan"&&t!="colSpan"&&t!="role"&&t!="popover"&&t in e)try{e[t]=n??"";break e}catch{}typeof n=="function"||(n==null||n===!1&&t[4]!="-"?e.removeAttribute(t):e.setAttribute(t,t=="popover"&&n==1?"":n))}}function mi(e){return function(t){if(this.l){var n=this.l[t.type+e];if(t.t==null)t.t=ba++;else if(t.t<n.u)return;return n(q.event?q.event(t):t)}}}function _a(e,t,n,r,o,a,i,s,c,l){var u,d,p,m,v,g,w,b,y,S,M,O,re,Z,I,Q,Ce,te=t.type;if(t.constructor!=null)return null;128&n.__u&&(c=!!(32&n.__u),a=[s=t.__e=n.__e]),(u=q.__b)&&u(t);e:if(typeof te=="function")try{if(b=t.props,y="prototype"in te&&te.prototype.render,S=(u=te.contextType)&&r[u.__c],M=u?S?S.props.value:u.__:r,n.__c?w=(d=t.__c=n.__c).__=d.__E:(y?t.__c=d=new te(b,M):(t.__c=d=new at(b,M),d.constructor=te,d.render=Ru),S&&S.sub(d),d.props=b,d.state||(d.state={}),d.context=M,d.__n=r,p=d.__d=!0,d.__h=[],d._sb=[]),y&&d.__s==null&&(d.__s=d.state),y&&te.getDerivedStateFromProps!=null&&(d.__s==d.state&&(d.__s=ot({},d.__s)),ot(d.__s,te.getDerivedStateFromProps(b,d.__s))),m=d.props,v=d.state,d.__v=t,p)y&&te.getDerivedStateFromProps==null&&d.componentWillMount!=null&&d.componentWillMount(),y&&d.componentDidMount!=null&&d.__h.push(d.componentDidMount);else{if(y&&te.getDerivedStateFromProps==null&&b!==m&&d.componentWillReceiveProps!=null&&d.componentWillReceiveProps(b,M),!d.__e&&d.shouldComponentUpdate!=null&&d.shouldComponentUpdate(b,d.__s,M)===!1||t.__v==n.__v){for(t.__v!=n.__v&&(d.props=b,d.state=d.__s,d.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some(function(ye){ye&&(ye.__=t)}),O=0;O<d._sb.length;O++)d.__h.push(d._sb[O]);d._sb=[],d.__h.length&&i.push(d);break e}d.componentWillUpdate!=null&&d.componentWillUpdate(b,d.__s,M),y&&d.componentDidUpdate!=null&&d.__h.push(function(){d.componentDidUpdate(m,v,g)})}if(d.context=M,d.props=b,d.__P=e,d.__e=!1,re=q.__r,Z=0,y){for(d.state=d.__s,d.__d=!1,re&&re(t),u=d.render(d.props,d.state,d.context),I=0;I<d._sb.length;I++)d.__h.push(d._sb[I]);d._sb=[]}else do d.__d=!1,re&&re(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++Z<25);d.state=d.__s,d.getChildContext!=null&&(r=ot(ot({},r),d.getChildContext())),y&&!p&&d.getSnapshotBeforeUpdate!=null&&(g=d.getSnapshotBeforeUpdate(m,v)),Q=u,u!=null&&u.type===je&&u.key==null&&(Q=Ds(u.props.children)),s=js(e,cr(Q)?Q:[Q],t,n,r,o,a,i,s,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&i.push(d),w&&(d.__E=d.__=null)}catch(ye){if(t.__v=null,c||a!=null)if(ye.then){for(t.__u|=c?160:128;s&&s.nodeType==8&&s.nextSibling;)s=s.nextSibling;a[a.indexOf(s)]=null,t.__e=s}else for(Ce=a.length;Ce--;)ya(a[Ce]);else t.__e=n.__e,t.__k=n.__k;q.__e(ye,t,n)}else a==null&&t.__v==n.__v?(t.__k=n.__k,t.__e=n.__e):s=t.__e=Tu(n.__e,t,n,r,o,a,i,c,l);return(u=q.diffed)&&u(t),128&t.__u?void 0:s}function Fs(e,t,n){for(var r=0;r<n.length;r++)xa(n[r],n[++r],n[++r]);q.__c&&q.__c(t,e),e.some(function(o){try{e=o.__h,o.__h=[],e.some(function(a){a.call(o)})}catch(a){q.__e(a,o.__v)}})}function Ds(e){return typeof e!="object"||e==null||e.__b&&e.__b>0?e:cr(e)?e.map(Ds):ot({},e)}function Tu(e,t,n,r,o,a,i,s,c){var l,u,d,p,m,v,g,w=n.props,b=t.props,y=t.type;if(y=="svg"?o="http://www.w3.org/2000/svg":y=="math"?o="http://www.w3.org/1998/Math/MathML":o||(o="http://www.w3.org/1999/xhtml"),a!=null){for(l=0;l<a.length;l++)if((m=a[l])&&"setAttribute"in m==!!y&&(y?m.localName==y:m.nodeType==3)){e=m,a[l]=null;break}}if(e==null){if(y==null)return document.createTextNode(b);e=document.createElementNS(o,y,b.is&&b),s&&(q.__m&&q.__m(t,a),s=!1),a=null}if(y==null)w===b||s&&e.data==b||(e.data=b);else{if(a=a&&sr.call(e.childNodes),w=n.props||Hn,!s&&a!=null)for(w={},l=0;l<e.attributes.length;l++)w[(m=e.attributes[l]).name]=m.value;for(l in w)if(m=w[l],l!="children"){if(l=="dangerouslySetInnerHTML")d=m;else if(!(l in b)){if(l=="value"&&"defaultValue"in b||l=="checked"&&"defaultChecked"in b)continue;_r(e,l,null,m,o)}}for(l in b)m=b[l],l=="children"?p=m:l=="dangerouslySetInnerHTML"?u=m:l=="value"?v=m:l=="checked"?g=m:s&&typeof m!="function"||w[l]===m||_r(e,l,m,w[l],o);if(u)s||d&&(u.__html==d.__html||u.__html==e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),js(t.type=="template"?e.content:e,cr(p)?p:[p],t,n,r,y=="foreignObject"?"http://www.w3.org/1999/xhtml":o,a,i,a?a[0]:n.__k&&sn(n,0),s,c),a!=null)for(l=a.length;l--;)ya(a[l]);s||(l="value",y=="progress"&&v==null?e.removeAttribute("value"):v!=null&&(v!==e[l]||y=="progress"&&!v||y=="option"&&v!=w[l])&&_r(e,l,v,w[l],o),l="checked",g!=null&&g!=e[l]&&_r(e,l,g,w[l],o))}return e}function xa(e,t,n){try{if(typeof e=="function"){var r=typeof e.__u=="function";r&&e.__u(),r&&t==null||(e.__u=e(t))}else e.current=t}catch(o){q.__e(o,n)}}function Bs(e,t,n){var r,o;if(q.unmount&&q.unmount(e),(r=e.ref)&&(r.current&&r.current!=e.__e||xa(r,null,t)),(r=e.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(a){q.__e(a,t)}r.base=r.__P=null}if(r=e.__k)for(o=0;o<r.length;o++)r[o]&&Bs(r[o],t,n||typeof e.type!="function");n||ya(e.__e),e.__c=e.__=e.__e=void 0}function Ru(e,t,n){return this.constructor(e,n)}function cn(e,t,n){var r,o,a,i;t==document&&(t=document.documentElement),q.__&&q.__(e,t),o=(r=typeof n=="function")?null:n&&n.__k||t.__k,a=[],i=[],_a(t,e=(!r&&n||t).__k=Le(je,null,[e]),o||Hn,Hn,t.namespaceURI,!r&&n?[n]:o?null:t.firstChild?sr.call(t.childNodes):null,a,!r&&n?n:o?o.__e:t.firstChild,r,i),Fs(a,e,i)}function Vs(e,t){cn(e,t,Vs)}function zu(e,t,n){var r,o,a,i,s=ot({},e.props);for(a in e.type&&e.type.defaultProps&&(i=e.type.defaultProps),t)a=="key"?r=t[a]:a=="ref"?o=t[a]:s[a]=t[a]==null&&i!=null?i[a]:t[a];return arguments.length>2&&(s.children=arguments.length>3?sr.call(arguments,2):n),Tn(e.type,s,r||e.key,o||e.ref,null)}function Ge(e){function t(n){var r,o;return this.getChildContext||(r=new Set,(o={})[t.__c]=this,this.getChildContext=function(){return o},this.componentWillUnmount=function(){r=null},this.shouldComponentUpdate=function(a){this.props.value!=a.value&&r.forEach(function(i){i.__e=!0,Bo(i)})},this.sub=function(a){r.add(a);var i=a.componentWillUnmount;a.componentWillUnmount=function(){r&&r.delete(a),i&&i.call(a)}}),n.children}return t.__c="__cC"+Rs++,t.__=e,t.Provider=t.__l=(t.Consumer=function(n,r){return n.children(r)}).contextType=t,t}sr=zs.slice,q={__e:function(e,t,n,r){for(var o,a,i;t=t.__;)if((o=t.__c)&&!o.__)try{if((a=o.constructor)&&a.getDerivedStateFromError!=null&&(o.setState(a.getDerivedStateFromError(e)),i=o.__d),o.componentDidCatch!=null&&(o.componentDidCatch(e,r||{}),i=o.__d),i)return o.__E=o}catch(s){e=s}throw e}},Is=0,at.prototype.setState=function(e,t){var n;n=this.__s!=null&&this.__s!=this.state?this.__s:this.__s=ot({},this.state),typeof e=="function"&&(e=e(ot({},n),this.props)),e&&ot(n,e),e!=null&&this.__v&&(t&&this._sb.push(t),Bo(this))},at.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Bo(this))},at.prototype.render=je,Bt=[],$s=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Ps=function(e,t){return e.__v.__b-t.__v.__b},jr.__r=0,Ts=/(PointerCapture)$|Capture$/i,ba=0,Lo=mi(!1),Fo=mi(!0),Rs=0;var Ou=0;function h(e,t,n,r,o,a){t||(t={});var i,s,c=t;if("ref"in c)for(s in c={},t)s=="ref"?i=t[s]:c[s]=t[s];var l={type:e,props:c,key:n,ref:i,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--Ou,__i:-1,__u:0,__source:o,__self:a};if(typeof e=="function"&&(i=e.defaultProps))for(s in i)c[s]===void 0&&(c[s]=i[s]);return q.vnode&&q.vnode(l),l}var At,me,vo,fi,ln=0,Hs=[],ve=q,gi=ve.__b,vi=ve.__r,wi=ve.diffed,bi=ve.__c,yi=ve.unmount,_i=ve.__;function fn(e,t){ve.__h&&ve.__h(me,e,ln||t),ln=0;var n=me.__H||(me.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function ie(e){return ln=1,ka(qs,e)}function ka(e,t,n){var r=fn(At++,2);if(r.t=e,!r.__c&&(r.__=[n?n(t):qs(void 0,t),function(s){var c=r.__N?r.__N[0]:r.__[0],l=r.t(c,s);c!==l&&(r.__N=[l,r.__[1]],r.__c.setState({}))}],r.__c=me,!me.__f)){var o=function(s,c,l){if(!r.__c.__H)return!0;var u=r.__c.__H.__.filter(function(p){return!!p.__c});if(u.every(function(p){return!p.__N}))return!a||a.call(this,s,c,l);var d=r.__c.props!==s;return u.forEach(function(p){if(p.__N){var m=p.__[0];p.__=p.__N,p.__N=void 0,m!==p.__[0]&&(d=!0)}}),a&&a.call(this,s,c,l)||d};me.__f=!0;var a=me.shouldComponentUpdate,i=me.componentWillUpdate;me.componentWillUpdate=function(s,c,l){if(this.__e){var u=a;a=void 0,o(s,c,l),a=u}i&&i.call(this,s,c,l)},me.shouldComponentUpdate=o}return r.__N||r.__}function fe(e,t){var n=fn(At++,3);!ve.__s&&Sa(n.__H,t)&&(n.__=e,n.u=t,me.__H.__h.push(n))}function gn(e,t){var n=fn(At++,4);!ve.__s&&Sa(n.__H,t)&&(n.__=e,n.u=t,me.__h.push(n))}function X(e){return ln=5,be(function(){return{current:e}},[])}function Ws(e,t,n){ln=6,gn(function(){if(typeof e=="function"){var r=e(t());return function(){e(null),r&&typeof r=="function"&&r()}}if(e)return e.current=t(),function(){return e.current=null}},n==null?n:n.concat(e))}function be(e,t){var n=fn(At++,7);return Sa(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function P(e,t){return ln=8,be(function(){return e},t)}function Pe(e){var t=me.context[e.__c],n=fn(At++,9);return n.c=e,t?(n.__==null&&(n.__=!0,t.sub(me)),t.props.value):e.__}function Us(e,t){ve.useDebugValue&&ve.useDebugValue(t?t(e):e)}function lr(){var e=fn(At++,11);if(!e.__){for(var t=me.__v;t!==null&&!t.__m&&t.__!==null;)t=t.__;var n=t.__m||(t.__m=[0,0]);e.__="P"+n[0]+"-"+n[1]++}return e.__}function ju(){for(var e;e=Hs.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Ir),e.__H.__h.forEach(Vo),e.__H.__h=[]}catch(t){e.__H.__h=[],ve.__e(t,e.__v)}}ve.__b=function(e){me=null,gi&&gi(e)},ve.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),_i&&_i(e,t)},ve.__r=function(e){vi&&vi(e),At=0;var t=(me=e.__c).__H;t&&(vo===me?(t.__h=[],me.__h=[],t.__.forEach(function(n){n.__N&&(n.__=n.__N),n.u=n.__N=void 0})):(t.__h.forEach(Ir),t.__h.forEach(Vo),t.__h=[],At=0)),vo=me},ve.diffed=function(e){wi&&wi(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(Hs.push(t)!==1&&fi===ve.requestAnimationFrame||((fi=ve.requestAnimationFrame)||Lu)(ju)),t.__H.__.forEach(function(n){n.u&&(n.__H=n.u),n.u=void 0})),vo=me=null},ve.__c=function(e,t){t.some(function(n){try{n.__h.forEach(Ir),n.__h=n.__h.filter(function(r){return!r.__||Vo(r)})}catch(r){t.some(function(o){o.__h&&(o.__h=[])}),t=[],ve.__e(r,n.__v)}}),bi&&bi(e,t)},ve.unmount=function(e){yi&&yi(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(r){try{Ir(r)}catch(o){t=o}}),n.__H=void 0,t&&ve.__e(t,n.__v))};var xi=typeof requestAnimationFrame=="function";function Lu(e){var t,n=function(){clearTimeout(r),xi&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);xi&&(t=requestAnimationFrame(n))}function Ir(e){var t=me,n=e.__c;typeof n=="function"&&(e.__c=void 0,n()),me=t}function Vo(e){var t=me;e.__c=e.__(),me=t}function Sa(e,t){return!e||e.length!==t.length||t.some(function(n,r){return n!==e[r]})}function qs(e,t){return typeof t=="function"?t(e):t}function Gs(e,t){for(var n in t)e[n]=t[n];return e}function Ho(e,t){for(var n in e)if(n!=="__source"&&!(n in t))return!0;for(var r in t)if(r!=="__source"&&e[r]!==t[r])return!0;return!1}function Zs(e,t){var n=t(),r=ie({t:{__:n,u:t}}),o=r[0].t,a=r[1];return gn(function(){o.__=n,o.u=t,wo(o)&&a({t:o})},[e,n,t]),fe(function(){return wo(o)&&a({t:o}),e(function(){wo(o)&&a({t:o})})},[e]),n}function wo(e){var t,n,r=e.u,o=e.__;try{var a=r();return!((t=o)===(n=a)&&(t!==0||1/t==1/n)||t!=t&&n!=n)}catch{return!0}}function Ys(e){e()}function Ks(e){return e}function Qs(){return[!1,Ys]}var Xs=gn;function Wo(e,t){this.props=e,this.context=t}function Fu(e,t){function n(o){var a=this.props.ref,i=a==o.ref;return!i&&a&&(a.call?a(null):a.current=null),t?!t(this.props,o)||!i:Ho(this.props,o)}function r(o){return this.shouldComponentUpdate=n,Le(e,o)}return r.displayName="Memo("+(e.displayName||e.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r}(Wo.prototype=new at).isPureReactComponent=!0,Wo.prototype.shouldComponentUpdate=function(e,t){return Ho(this.props,e)||Ho(this.state,t)};var ki=q.__b;q.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),ki&&ki(e)};var Du=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function dr(e){function t(n){var r=Gs({},n);return delete r.ref,e(r,n.ref||null)}return t.$$typeof=Du,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var Si=function(e,t){return e==null?null:gt(gt(e).map(t))},Bu={map:Si,forEach:Si,count:function(e){return e?gt(e).length:0},only:function(e){var t=gt(e);if(t.length!==1)throw"Children.only";return t[0]},toArray:gt},Vu=q.__e;q.__e=function(e,t,n,r){if(e.then){for(var o,a=t;a=a.__;)if((o=a.__c)&&o.__c)return t.__e==null&&(t.__e=n.__e,t.__k=n.__k),o.__c(e,t)}Vu(e,t,n,r)};var Ci=q.unmount;function Js(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach(function(r){typeof r.__c=="function"&&r.__c()}),e.__c.__H=null),(e=Gs({},e)).__c!=null&&(e.__c.__P===n&&(e.__c.__P=t),e.__c.__e=!0,e.__c=null),e.__k=e.__k&&e.__k.map(function(r){return Js(r,t,n)})),e}function ec(e,t,n){return e&&n&&(e.__v=null,e.__k=e.__k&&e.__k.map(function(r){return ec(r,t,n)}),e.__c&&e.__c.__P===t&&(e.__e&&n.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=n)),e}function $r(){this.__u=0,this.o=null,this.__b=null}function tc(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function Hu(e){var t,n,r;function o(a){if(t||(t=e()).then(function(i){n=i.default||i},function(i){r=i}),r)throw r;if(!n)throw t;return Le(n,a)}return o.displayName="Lazy",o.__f=!0,o}function Cn(){this.i=null,this.l=null}q.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),Ci&&Ci(e)},($r.prototype=new at).__c=function(e,t){var n=t.__c,r=this;r.o==null&&(r.o=[]),r.o.push(n);var o=tc(r.__v),a=!1,i=function(){a||(a=!0,n.__R=null,o?o(s):s())};n.__R=i;var s=function(){if(!--r.__u){if(r.state.__a){var c=r.state.__a;r.__v.__k[0]=ec(c,c.__c.__P,c.__c.__O)}var l;for(r.setState({__a:r.__b=null});l=r.o.pop();)l.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(i,i)},$r.prototype.componentWillUnmount=function(){this.o=[]},$r.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=Js(this.__b,n,r.__O=r.__P)}this.__b=null}var o=t.__a&&Le(je,null,e.fallback);return o&&(o.__u&=-33),[Le(je,null,t.__a?null:e.children),o]};var Ei=function(e,t,n){if(++n[1]===n[0]&&e.l.delete(t),e.props.revealOrder&&(e.props.revealOrder[0]!=="t"||!e.l.size))for(n=e.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.i=n=n[2]}};function Wu(e){return this.getChildContext=function(){return e.context},e.children}function Uu(e){var t=this,n=e.h;if(t.componentWillUnmount=function(){cn(null,t.v),t.v=null,t.h=null},t.h&&t.h!==n&&t.componentWillUnmount(),!t.v){for(var r=t.__v;r!==null&&!r.__m&&r.__!==null;)r=r.__;t.h=n,t.v={nodeType:1,parentNode:n,childNodes:[],__k:{__m:r.__m},contains:function(){return!0},appendChild:function(o){this.childNodes.push(o),t.h.appendChild(o)},insertBefore:function(o,a){this.childNodes.push(o),t.h.insertBefore(o,a)},removeChild:function(o){this.childNodes.splice(this.childNodes.indexOf(o)>>>1,1),t.h.removeChild(o)}}}cn(Le(Wu,{context:t.context},e.__v),t.v)}function qu(e,t){var n=Le(Uu,{__v:e,h:t});return n.containerInfo=t,n}(Cn.prototype=new at).__a=function(e){var t=this,n=tc(t.__v),r=t.l.get(e);return r[0]++,function(o){var a=function(){t.props.revealOrder?(r.push(o),Ei(t,e,r)):o()};n?n(a):a()}},Cn.prototype.render=function(e){this.i=null,this.l=new Map;var t=gt(e.children);e.revealOrder&&e.revealOrder[0]==="b"&&t.reverse();for(var n=t.length;n--;)this.l.set(t[n],this.i=[1,0,this.i]);return e.children},Cn.prototype.componentDidUpdate=Cn.prototype.componentDidMount=function(){var e=this;this.l.forEach(function(t,n){Ei(e,n,t)})};var nc=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,Gu=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Zu=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,Yu=/[A-Z0-9]/g,Ku=typeof document<"u",Qu=function(e){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(e)};function Xu(e,t,n){return t.__k==null&&(t.textContent=""),cn(e,t),typeof n=="function"&&n(),e?e.__c:null}function Ju(e,t,n){return Vs(e,t),typeof n=="function"&&n(),e?e.__c:null}at.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(at.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var Ni=q.event;function ep(){}function tp(){return this.cancelBubble}function np(){return this.defaultPrevented}q.event=function(e){return Ni&&(e=Ni(e)),e.persist=ep,e.isPropagationStopped=tp,e.isDefaultPrevented=np,e.nativeEvent=e};var Ca,rp={enumerable:!1,configurable:!0,get:function(){return this.class}},Ai=q.vnode;q.vnode=function(e){typeof e.type=="string"&&function(t){var n=t.props,r=t.type,o={},a=r.indexOf("-")===-1;for(var i in n){var s=n[i];if(!(i==="value"&&"defaultValue"in n&&s==null||Ku&&i==="children"&&r==="noscript"||i==="class"||i==="className")){var c=i.toLowerCase();i==="defaultValue"&&"value"in n&&n.value==null?i="value":i==="download"&&s===!0?s="":c==="translate"&&s==="no"?s=!1:c[0]==="o"&&c[1]==="n"?c==="ondoubleclick"?i="ondblclick":c!=="onchange"||r!=="input"&&r!=="textarea"||Qu(n.type)?c==="onfocus"?i="onfocusin":c==="onblur"?i="onfocusout":Zu.test(i)&&(i=c):c=i="oninput":a&&Gu.test(i)?i=i.replace(Yu,"-$&").toLowerCase():s===null&&(s=void 0),c==="oninput"&&o[i=c]&&(i="oninputCapture"),o[i]=s}}r=="select"&&o.multiple&&Array.isArray(o.value)&&(o.value=gt(n.children).forEach(function(l){l.props.selected=o.value.indexOf(l.props.value)!=-1})),r=="select"&&o.defaultValue!=null&&(o.value=gt(n.children).forEach(function(l){l.props.selected=o.multiple?o.defaultValue.indexOf(l.props.value)!=-1:o.defaultValue==l.props.value})),n.class&&!n.className?(o.class=n.class,Object.defineProperty(o,"className",rp)):(n.className&&!n.class||n.class&&n.className)&&(o.class=o.className=n.className),t.props=o}(e),e.$$typeof=nc,Ai&&Ai(e)};var Mi=q.__r;q.__r=function(e){Mi&&Mi(e),Ca=e.__c};var Ii=q.diffed;q.diffed=function(e){Ii&&Ii(e);var t=e.props,n=e.__e;n!=null&&e.type==="textarea"&&"value"in t&&t.value!==n.value&&(n.value=t.value==null?"":t.value),Ca=null};var op={ReactCurrentDispatcher:{current:{readContext:function(e){return Ca.__n[e.__c].props.value},useCallback:P,useContext:Pe,useDebugValue:Us,useDeferredValue:Ks,useEffect:fe,useId:lr,useImperativeHandle:Ws,useInsertionEffect:Xs,useLayoutEffect:gn,useMemo:be,useReducer:ka,useRef:X,useState:ie,useSyncExternalStore:Zs,useTransition:Qs}}};function ap(e){return Le.bind(null,e)}function ur(e){return!!e&&e.$$typeof===nc}function ip(e){return ur(e)&&e.type===je}function sp(e){return!!e&&!!e.displayName&&(typeof e.displayName=="string"||e.displayName instanceof String)&&e.displayName.startsWith("Memo(")}function rc(e){return ur(e)?zu.apply(null,arguments):e}function cp(e){return!!e.__k&&(cn(null,e),!0)}function lp(e){return e&&(e.base||e.nodeType===1&&e)||null}var dp=function(e,t){return e(t)},up=function(e,t){return e(t)},pp=je,hp=ur,Ea={useState:ie,useId:lr,useReducer:ka,useEffect:fe,useLayoutEffect:gn,useInsertionEffect:Xs,useTransition:Qs,useDeferredValue:Ks,useSyncExternalStore:Zs,startTransition:Ys,useRef:X,useImperativeHandle:Ws,useMemo:be,useCallback:P,useContext:Pe,useDebugValue:Us,version:"18.3.1",Children:Bu,render:Xu,hydrate:Ju,unmountComponentAtNode:cp,createPortal:qu,createElement:Le,createContext:Ge,createFactory:ap,cloneElement:rc,createRef:Do,Fragment:je,isValidElement:ur,isElement:hp,isFragment:ip,isMemo:sp,findDOMNode:lp,Component:at,PureComponent:Wo,memo:Fu,forwardRef:dr,flushSync:up,unstable_batchedUpdates:dp,StrictMode:pp,Suspense:$r,SuspenseList:Cn,lazy:Hu,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:op},mp=(e=16)=>Math.random().toString(36).substring(2,e+2),fp={maxReconnectAttempts:5,reconnectDelay:1e3,requestTimeout:3e4},gp=class{constructor(e={}){this.ws=null,this.pendingRequests=new Map,this.reconnectAttempts=0,this.methods={},this.isIntentionalClose=!1,this.options={...fp,...e}}register(e){Object.entries(e).forEach(([t,n])=>{this.methods[t]={handler:n}})}callMethod(e,t,n){if(!this.ws)throw new Error("WebSocket is not connected");const r=mp(),o={id:r,messageType:"request",method:e,payload:t};return new Promise((a,i)=>{var s;const c=setTimeout(()=>{this.pendingRequests.delete(r),i(new Error(`Request timed out: ${e}`))},this.options.requestTimeout);this.pendingRequests.set(r,{resolve:a,reject:i,timeout:c,onUpdate:n}),(s=this.ws)==null||s.send(JSON.stringify(o))})}setupWebSocketHandlers(e){e.onmessage=t=>{try{const n=JSON.parse(t.data);this.handleMessage(n)}catch(n){console.error("Error handling WebSocket message:",n)}},e.onclose=()=>{this.handleDisconnect()},e.onerror=t=>{console.error("WebSocket error:",t)}}handleMessage(e){const{messageType:t,id:n}=e;switch(t){case"request":this.handleRequest(e);break;case"response":this.handleResponse(n,e.payload);break;case"update":this.handleUpdate(n,e.payload);break;case"error":this.handleError(n,e.error.message);break;default:console.warn(`Unknown message type: ${t}`)}}async handleRequest(e){const{id:t,method:n,payload:r}=e;if(!n){this.sendError(t,"Method name is required");return}const o=this.methods[n];if(!o){this.sendError(t,`Method not found: ${n}`);return}try{const a=s=>{this.sendUpdate(t,n,s)},i=await o.handler(r,a);this.sendResponse(t,n,i)}catch(a){this.sendError(t,a instanceof Error?a.message:String(a))}}handleResponse(e,t){const n=this.pendingRequests.get(e);if(!n){console.warn(`Received response for unknown request ID: ${e}`);return}clearTimeout(n.timeout),this.pendingRequests.delete(e),n.resolve(t)}handleUpdate(e,t){const n=this.pendingRequests.get(e);if(!n||!n.onUpdate){console.warn(`Received update for unknown request ID: ${e}`);return}n.onUpdate(t)}handleError(e,t){const n=this.pendingRequests.get(e);if(!n){console.warn(`Received error for unknown request ID: ${e}`);return}clearTimeout(n.timeout),this.pendingRequests.delete(e),n.reject(new Error(t))}sendResponse(e,t,n){if(!this.ws)throw new Error("WebSocket is not connected");const r={id:e,messageType:"response",method:t,payload:n};this.ws.send(JSON.stringify(r))}sendUpdate(e,t,n){if(!this.ws)throw new Error("WebSocket is not connected");const r={id:e,messageType:"update",method:t,payload:n};this.ws.send(JSON.stringify(r))}sendError(e,t){if(!this.ws)throw new Error("WebSocket is not connected");const n={id:e,messageType:"error",error:{message:t}};this.ws.send(JSON.stringify(n))}handleDisconnect(){if(this.isIntentionalClose){console.log("WebSocket closed intentionally, not attempting to reconnect"),this.clearPendingRequests(new Error("Connection closed by user"));return}this.reconnectAttempts<this.options.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})...`),setTimeout(()=>this.reconnect(),this.options.reconnectDelay*this.reconnectAttempts)):(console.error("Max reconnection attempts reached"),this.clearPendingRequests(new Error("Connection closed")))}clearPendingRequests(e){this.pendingRequests.forEach(({reject:t})=>{t(e)}),this.pendingRequests.clear()}async close(){this.isIntentionalClose=!0,this.ws&&(this.ws.close(),this.ws=null),this.clearPendingRequests(new Error("Connection closed by user"))}};function Qt(e,t,n,r=!1){const o=e.safeParse(t);if(!o.success){const a=new Error(`Validation failed for ${n}: ${o.error.message}`);if(r)return console.error(a),t;throw a}return o.data}var vp=class{constructor(e,t){this.bridge=e,this.contract=t,this.call=new Proxy({},{get:(n,r)=>(o,a)=>this.callMethod(r,o,a)})}async callMethod(e,t,n){const r=this.contract.consumes[e];if(!r)throw new Error(`Method ${String(e)} not found in contract`);const o=Qt(r.request,t,`request for method ${String(e)}`),a=n!=null&&n.onUpdate&&r.update?s=>{var c;if(r.update)try{const l=Qt(r.update,s,`update for method ${String(e)}`,!0);(c=n.onUpdate)==null||c.call(n,l)}catch(l){console.error("Update validation failed:",l)}}:void 0,i=await this.bridge.callMethod(e,o,a);return Qt(r.response,i,`response for method ${String(e)}`)}register(e){const t={};for(const[n,r]of Object.entries(e)){const o=this.contract.serves[n];if(!o)throw new Error(`Method ${n} not found in contract`);t[n]=async(a,i)=>{const s=Qt(o.request,a,`request for method ${n}`),c=o.update&&i?u=>{if(o.update)try{const d=Qt(o.update,u,`update for method ${n}`,!0);i(d)}catch(d){console.error("Update validation failed:",d)}}:void 0,l=await r(s,{sendUpdate:c});return Qt(o.response,l,`response for method ${n}`)}}this.bridge.register(t)}async close(){await this.bridge.close()}},wp=class extends gp{constructor(e,t){super(t),this.reconnectTimer=null,this.url=e}call(e,t,n){return this.callMethod(e,t,n)}reconnect(){this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=setTimeout(async()=>{try{await this.connect()}catch{this.reconnect()}},this.options.reconnectDelay)}connect(){return new Promise((e,t)=>{try{const n=new window.WebSocket(this.url);n.onopen=()=>{this.ws=n,this.setupWebSocketHandlers(n),e()},n.onerror=()=>{t(new Error("Failed to connect to WebSocket server"))}}catch(n){t(n)}})}},bp=class extends vp{constructor(e,t,n){super(new wp(e,n),{serves:t.client||{},consumes:t.server||{}})}connect(){return this.bridge.connect()}};function oc(e,t,n){return new bp(e,t,n)}var ee;(function(e){e.assertEqual=o=>o;function t(o){}e.assertIs=t;function n(o){throw new Error}e.assertNever=n,e.arrayToEnum=o=>{const a={};for(const i of o)a[i]=i;return a},e.getValidEnumValues=o=>{const a=e.objectKeys(o).filter(s=>typeof o[o[s]]!="number"),i={};for(const s of a)i[s]=o[s];return e.objectValues(i)},e.objectValues=o=>e.objectKeys(o).map(function(a){return o[a]}),e.objectKeys=typeof Object.keys=="function"?o=>Object.keys(o):o=>{const a=[];for(const i in o)Object.prototype.hasOwnProperty.call(o,i)&&a.push(i);return a},e.find=(o,a)=>{for(const i of o)if(a(i))return i},e.isInteger=typeof Number.isInteger=="function"?o=>Number.isInteger(o):o=>typeof o=="number"&&isFinite(o)&&Math.floor(o)===o;function r(o,a=" | "){return o.map(i=>typeof i=="string"?`'${i}'`:i).join(a)}e.joinValues=r,e.jsonStringifyReplacer=(o,a)=>typeof a=="bigint"?a.toString():a})(ee||(ee={}));var Uo;(function(e){e.mergeShapes=(t,n)=>({...t,...n})})(Uo||(Uo={}));const E=ee.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ft=e=>{switch(typeof e){case"undefined":return E.undefined;case"string":return E.string;case"number":return isNaN(e)?E.nan:E.number;case"boolean":return E.boolean;case"function":return E.function;case"bigint":return E.bigint;case"symbol":return E.symbol;case"object":return Array.isArray(e)?E.array:e===null?E.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?E.promise:typeof Map<"u"&&e instanceof Map?E.map:typeof Set<"u"&&e instanceof Set?E.set:typeof Date<"u"&&e instanceof Date?E.date:E.object;default:return E.unknown}},x=ee.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),yp=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class He extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=t}format(t){const n=t||function(a){return a.message},r={_errors:[]},o=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(o);else if(i.code==="invalid_return_type")o(i.returnTypeError);else if(i.code==="invalid_arguments")o(i.argumentsError);else if(i.path.length===0)r._errors.push(n(i));else{let s=r,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(s[l]=s[l]||{_errors:[]},s[l]._errors.push(n(i))):s[l]=s[l]||{_errors:[]},s=s[l],c++}}};return o(this),r}static assert(t){if(!(t instanceof He))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ee.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=n=>n.message){const n={},r=[];for(const o of this.issues)o.path.length>0?(n[o.path[0]]=n[o.path[0]]||[],n[o.path[0]].push(t(o))):r.push(t(o));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}He.create=e=>new He(e);const dn=(e,t)=>{let n;switch(e.code){case x.invalid_type:e.received===E.undefined?n="Required":n=`Expected ${e.expected}, received ${e.received}`;break;case x.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,ee.jsonStringifyReplacer)}`;break;case x.unrecognized_keys:n=`Unrecognized key(s) in object: ${ee.joinValues(e.keys,", ")}`;break;case x.invalid_union:n="Invalid input";break;case x.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${ee.joinValues(e.options)}`;break;case x.invalid_enum_value:n=`Invalid enum value. Expected ${ee.joinValues(e.options)}, received '${e.received}'`;break;case x.invalid_arguments:n="Invalid function arguments";break;case x.invalid_return_type:n="Invalid function return type";break;case x.invalid_date:n="Invalid date";break;case x.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:ee.assertNever(e.validation):e.validation!=="regex"?n=`Invalid ${e.validation}`:n="Invalid";break;case x.too_small:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:n="Invalid input";break;case x.too_big:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?n=`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:n="Invalid input";break;case x.custom:n="Invalid input";break;case x.invalid_intersection_types:n="Intersection results could not be merged";break;case x.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case x.not_finite:n="Number must be finite";break;default:n=t.defaultError,ee.assertNever(e)}return{message:n}};let ac=dn;function _p(e){ac=e}function Lr(){return ac}const Fr=e=>{const{data:t,path:n,errorMaps:r,issueData:o}=e,a=[...n,...o.path||[]],i={...o,path:a};if(o.message!==void 0)return{...o,path:a,message:o.message};let s="";const c=r.filter(l=>!!l).slice().reverse();for(const l of c)s=l(i,{data:t,defaultError:s}).message;return{...o,path:a,message:s}},xp=[];function C(e,t){const n=Lr(),r=Fr({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===dn?void 0:dn].filter(o=>!!o)});e.common.issues.push(r)}class $e{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,n){const r=[];for(const o of n){if(o.status==="aborted")return F;o.status==="dirty"&&t.dirty(),r.push(o.value)}return{status:t.value,value:r}}static async mergeObjectAsync(t,n){const r=[];for(const o of n){const a=await o.key,i=await o.value;r.push({key:a,value:i})}return $e.mergeObjectSync(t,r)}static mergeObjectSync(t,n){const r={};for(const o of n){const{key:a,value:i}=o;if(a.status==="aborted"||i.status==="aborted")return F;a.status==="dirty"&&t.dirty(),i.status==="dirty"&&t.dirty(),a.value!=="__proto__"&&(typeof i.value<"u"||o.alwaysSet)&&(r[a.value]=i.value)}return{status:t.value,value:r}}}const F=Object.freeze({status:"aborted"}),Dr=e=>({status:"dirty",value:e}),Re=e=>({status:"valid",value:e}),qo=e=>e.status==="aborted",Go=e=>e.status==="dirty",qt=e=>e.status==="valid",Wn=e=>typeof Promise<"u"&&e instanceof Promise;function Br(e,t,n,r){if(typeof t=="function"?e!==t||!0:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(e)}function ic(e,t,n,r,o){if(typeof t=="function"?e!==t||!0:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var $;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})($||($={}));var En,Nn;class ct{constructor(t,n,r,o){this._cachedPath=[],this.parent=t,this.data=n,this._path=r,this._key=o}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const $i=(e,t)=>{if(qt(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new He(e.common.issues);return this._error=n,this._error}}};function W(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:o}=e;if(t&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:o}:{errorMap:(a,i)=>{var s,c;const{message:l}=e;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:typeof i.data>"u"?{message:(s=l??r)!==null&&s!==void 0?s:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??n)!==null&&c!==void 0?c:i.defaultError}},description:o}}class G{get description(){return this._def.description}_getType(t){return ft(t.data)}_getOrReturnCtx(t,n){return n||{common:t.parent.common,data:t.data,parsedType:ft(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new $e,ctx:{common:t.parent.common,data:t.data,parsedType:ft(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const n=this._parse(t);if(Wn(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(t){const n=this._parse(t);return Promise.resolve(n)}parse(t,n){const r=this.safeParse(t,n);if(r.success)return r.data;throw r.error}safeParse(t,n){var r;const o={common:{issues:[],async:(r=n==null?void 0:n.async)!==null&&r!==void 0?r:!1,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:ft(t)},a=this._parseSync({data:t,path:o.path,parent:o});return $i(o,a)}"~validate"(t){var n,r;const o={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:ft(t)};if(!this["~standard"].async)try{const a=this._parseSync({data:t,path:[],parent:o});return qt(a)?{value:a.value}:{issues:o.common.issues}}catch(a){!((r=(n=a==null?void 0:a.message)===null||n===void 0?void 0:n.toLowerCase())===null||r===void 0)&&r.includes("encountered")&&(this["~standard"].async=!0),o.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:o}).then(a=>qt(a)?{value:a.value}:{issues:o.common.issues})}async parseAsync(t,n){const r=await this.safeParseAsync(t,n);if(r.success)return r.data;throw r.error}async safeParseAsync(t,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:ft(t)},o=this._parse({data:t,path:r.path,parent:r}),a=await(Wn(o)?o:Promise.resolve(o));return $i(r,a)}refine(t,n){const r=o=>typeof n=="string"||typeof n>"u"?{message:n}:typeof n=="function"?n(o):n;return this._refinement((o,a)=>{const i=t(o),s=()=>a.addIssue({code:x.custom,...r(o)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>c?!0:(s(),!1)):i?!0:(s(),!1)})}refinement(t,n){return this._refinement((r,o)=>t(r)?!0:(o.addIssue(typeof n=="function"?n(r,o):n),!1))}_refinement(t){return new et({schema:this,typeName:j.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return it.create(this,this._def)}nullable(){return Pt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Xe.create(this)}promise(){return pn.create(this,this._def)}or(t){return Zn.create([this,t],this._def)}and(t){return Yn.create(this,t,this._def)}transform(t){return new et({...W(this._def),schema:this,typeName:j.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const n=typeof t=="function"?t:()=>t;return new er({...W(this._def),innerType:this,defaultValue:n,typeName:j.ZodDefault})}brand(){return new Na({typeName:j.ZodBranded,type:this,...W(this._def)})}catch(t){const n=typeof t=="function"?t:()=>t;return new tr({...W(this._def),innerType:this,catchValue:n,typeName:j.ZodCatch})}describe(t){const n=this.constructor;return new n({...this._def,description:t})}pipe(t){return pr.create(this,t)}readonly(){return nr.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const kp=/^c[^\s-]{8,}$/i,Sp=/^[0-9a-z]+$/,Cp=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Ep=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Np=/^[a-z0-9_-]{21}$/i,Ap=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Mp=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Ip=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,$p="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let bo;const Pp=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Tp=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Rp=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,zp=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Op=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,jp=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,sc="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Lp=new RegExp(`^${sc}$`);function cc(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`);const n=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}function Fp(e){return new RegExp(`^${cc(e)}$`)}function lc(e){let t=`${sc}T${cc(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function Dp(e,t){return!!((t==="v4"||!t)&&Pp.test(e)||(t==="v6"||!t)&&Rp.test(e))}function Bp(e,t){if(!Ap.test(e))return!1;try{const[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),o=JSON.parse(atob(r));return!(typeof o!="object"||o===null||!o.typ||!o.alg||t&&o.alg!==t)}catch{return!1}}function Vp(e,t){return!!((t==="v4"||!t)&&Tp.test(e)||(t==="v6"||!t)&&zp.test(e))}class Ke extends G{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==E.string){const o=this._getOrReturnCtx(t);return C(o,{code:x.invalid_type,expected:E.string,received:o.parsedType}),F}const n=new $e;let r;for(const o of this._def.checks)if(o.kind==="min")t.data.length<o.value&&(r=this._getOrReturnCtx(t,r),C(r,{code:x.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),n.dirty());else if(o.kind==="max")t.data.length>o.value&&(r=this._getOrReturnCtx(t,r),C(r,{code:x.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),n.dirty());else if(o.kind==="length"){const a=t.data.length>o.value,i=t.data.length<o.value;(a||i)&&(r=this._getOrReturnCtx(t,r),a?C(r,{code:x.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}):i&&C(r,{code:x.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}),n.dirty())}else if(o.kind==="email")Ip.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"email",code:x.invalid_string,message:o.message}),n.dirty());else if(o.kind==="emoji")bo||(bo=new RegExp($p,"u")),bo.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"emoji",code:x.invalid_string,message:o.message}),n.dirty());else if(o.kind==="uuid")Ep.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"uuid",code:x.invalid_string,message:o.message}),n.dirty());else if(o.kind==="nanoid")Np.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"nanoid",code:x.invalid_string,message:o.message}),n.dirty());else if(o.kind==="cuid")kp.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"cuid",code:x.invalid_string,message:o.message}),n.dirty());else if(o.kind==="cuid2")Sp.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"cuid2",code:x.invalid_string,message:o.message}),n.dirty());else if(o.kind==="ulid")Cp.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"ulid",code:x.invalid_string,message:o.message}),n.dirty());else if(o.kind==="url")try{new URL(t.data)}catch{r=this._getOrReturnCtx(t,r),C(r,{validation:"url",code:x.invalid_string,message:o.message}),n.dirty()}else o.kind==="regex"?(o.regex.lastIndex=0,o.regex.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"regex",code:x.invalid_string,message:o.message}),n.dirty())):o.kind==="trim"?t.data=t.data.trim():o.kind==="includes"?t.data.includes(o.value,o.position)||(r=this._getOrReturnCtx(t,r),C(r,{code:x.invalid_string,validation:{includes:o.value,position:o.position},message:o.message}),n.dirty()):o.kind==="toLowerCase"?t.data=t.data.toLowerCase():o.kind==="toUpperCase"?t.data=t.data.toUpperCase():o.kind==="startsWith"?t.data.startsWith(o.value)||(r=this._getOrReturnCtx(t,r),C(r,{code:x.invalid_string,validation:{startsWith:o.value},message:o.message}),n.dirty()):o.kind==="endsWith"?t.data.endsWith(o.value)||(r=this._getOrReturnCtx(t,r),C(r,{code:x.invalid_string,validation:{endsWith:o.value},message:o.message}),n.dirty()):o.kind==="datetime"?lc(o).test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{code:x.invalid_string,validation:"datetime",message:o.message}),n.dirty()):o.kind==="date"?Lp.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{code:x.invalid_string,validation:"date",message:o.message}),n.dirty()):o.kind==="time"?Fp(o).test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{code:x.invalid_string,validation:"time",message:o.message}),n.dirty()):o.kind==="duration"?Mp.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"duration",code:x.invalid_string,message:o.message}),n.dirty()):o.kind==="ip"?Dp(t.data,o.version)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"ip",code:x.invalid_string,message:o.message}),n.dirty()):o.kind==="jwt"?Bp(t.data,o.alg)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"jwt",code:x.invalid_string,message:o.message}),n.dirty()):o.kind==="cidr"?Vp(t.data,o.version)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"cidr",code:x.invalid_string,message:o.message}),n.dirty()):o.kind==="base64"?Op.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"base64",code:x.invalid_string,message:o.message}),n.dirty()):o.kind==="base64url"?jp.test(t.data)||(r=this._getOrReturnCtx(t,r),C(r,{validation:"base64url",code:x.invalid_string,message:o.message}),n.dirty()):ee.assertNever(o);return{status:n.value,value:t.data}}_regex(t,n,r){return this.refinement(o=>t.test(o),{validation:n,code:x.invalid_string,...$.errToObj(r)})}_addCheck(t){return new Ke({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...$.errToObj(t)})}url(t){return this._addCheck({kind:"url",...$.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...$.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...$.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...$.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...$.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...$.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...$.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...$.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...$.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...$.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...$.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...$.errToObj(t)})}datetime(t){var n,r;return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,offset:(n=t==null?void 0:t.offset)!==null&&n!==void 0?n:!1,local:(r=t==null?void 0:t.local)!==null&&r!==void 0?r:!1,...$.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,...$.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...$.errToObj(t)})}regex(t,n){return this._addCheck({kind:"regex",regex:t,...$.errToObj(n)})}includes(t,n){return this._addCheck({kind:"includes",value:t,position:n==null?void 0:n.position,...$.errToObj(n==null?void 0:n.message)})}startsWith(t,n){return this._addCheck({kind:"startsWith",value:t,...$.errToObj(n)})}endsWith(t,n){return this._addCheck({kind:"endsWith",value:t,...$.errToObj(n)})}min(t,n){return this._addCheck({kind:"min",value:t,...$.errToObj(n)})}max(t,n){return this._addCheck({kind:"max",value:t,...$.errToObj(n)})}length(t,n){return this._addCheck({kind:"length",value:t,...$.errToObj(n)})}nonempty(t){return this.min(1,$.errToObj(t))}trim(){return new Ke({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ke({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ke({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxLength(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}Ke.create=e=>{var t;return new Ke({checks:[],typeName:j.ZodString,coerce:(t=e==null?void 0:e.coerce)!==null&&t!==void 0?t:!1,...W(e)})};function Hp(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,o=n>r?n:r,a=parseInt(e.toFixed(o).replace(".","")),i=parseInt(t.toFixed(o).replace(".",""));return a%i/Math.pow(10,o)}class Mt extends G{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==E.number){const o=this._getOrReturnCtx(t);return C(o,{code:x.invalid_type,expected:E.number,received:o.parsedType}),F}let n;const r=new $e;for(const o of this._def.checks)o.kind==="int"?ee.isInteger(t.data)||(n=this._getOrReturnCtx(t,n),C(n,{code:x.invalid_type,expected:"integer",received:"float",message:o.message}),r.dirty()):o.kind==="min"?(o.inclusive?t.data<o.value:t.data<=o.value)&&(n=this._getOrReturnCtx(t,n),C(n,{code:x.too_small,minimum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),r.dirty()):o.kind==="max"?(o.inclusive?t.data>o.value:t.data>=o.value)&&(n=this._getOrReturnCtx(t,n),C(n,{code:x.too_big,maximum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),r.dirty()):o.kind==="multipleOf"?Hp(t.data,o.value)!==0&&(n=this._getOrReturnCtx(t,n),C(n,{code:x.not_multiple_of,multipleOf:o.value,message:o.message}),r.dirty()):o.kind==="finite"?Number.isFinite(t.data)||(n=this._getOrReturnCtx(t,n),C(n,{code:x.not_finite,message:o.message}),r.dirty()):ee.assertNever(o);return{status:r.value,value:t.data}}gte(t,n){return this.setLimit("min",t,!0,$.toString(n))}gt(t,n){return this.setLimit("min",t,!1,$.toString(n))}lte(t,n){return this.setLimit("max",t,!0,$.toString(n))}lt(t,n){return this.setLimit("max",t,!1,$.toString(n))}setLimit(t,n,r,o){return new Mt({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:$.toString(o)}]})}_addCheck(t){return new Mt({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:$.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:$.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:$.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:$.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:$.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:$.toString(n)})}finite(t){return this._addCheck({kind:"finite",message:$.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:$.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:$.toString(t)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&ee.isInteger(t.value))}get isFinite(){let t=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(t===null||r.value<t)&&(t=r.value)}return Number.isFinite(n)&&Number.isFinite(t)}}Mt.create=e=>new Mt({checks:[],typeName:j.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...W(e)});class It extends G{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==E.bigint)return this._getInvalidInput(t);let n;const r=new $e;for(const o of this._def.checks)o.kind==="min"?(o.inclusive?t.data<o.value:t.data<=o.value)&&(n=this._getOrReturnCtx(t,n),C(n,{code:x.too_small,type:"bigint",minimum:o.value,inclusive:o.inclusive,message:o.message}),r.dirty()):o.kind==="max"?(o.inclusive?t.data>o.value:t.data>=o.value)&&(n=this._getOrReturnCtx(t,n),C(n,{code:x.too_big,type:"bigint",maximum:o.value,inclusive:o.inclusive,message:o.message}),r.dirty()):o.kind==="multipleOf"?t.data%o.value!==BigInt(0)&&(n=this._getOrReturnCtx(t,n),C(n,{code:x.not_multiple_of,multipleOf:o.value,message:o.message}),r.dirty()):ee.assertNever(o);return{status:r.value,value:t.data}}_getInvalidInput(t){const n=this._getOrReturnCtx(t);return C(n,{code:x.invalid_type,expected:E.bigint,received:n.parsedType}),F}gte(t,n){return this.setLimit("min",t,!0,$.toString(n))}gt(t,n){return this.setLimit("min",t,!1,$.toString(n))}lte(t,n){return this.setLimit("max",t,!0,$.toString(n))}lt(t,n){return this.setLimit("max",t,!1,$.toString(n))}setLimit(t,n,r,o){return new It({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:$.toString(o)}]})}_addCheck(t){return new It({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:$.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:$.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:$.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:$.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:$.toString(n)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}It.create=e=>{var t;return new It({checks:[],typeName:j.ZodBigInt,coerce:(t=e==null?void 0:e.coerce)!==null&&t!==void 0?t:!1,...W(e)})};class Un extends G{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==E.boolean){const n=this._getOrReturnCtx(t);return C(n,{code:x.invalid_type,expected:E.boolean,received:n.parsedType}),F}return Re(t.data)}}Un.create=e=>new Un({typeName:j.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...W(e)});class Gt extends G{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==E.date){const o=this._getOrReturnCtx(t);return C(o,{code:x.invalid_type,expected:E.date,received:o.parsedType}),F}if(isNaN(t.data.getTime())){const o=this._getOrReturnCtx(t);return C(o,{code:x.invalid_date}),F}const n=new $e;let r;for(const o of this._def.checks)o.kind==="min"?t.data.getTime()<o.value&&(r=this._getOrReturnCtx(t,r),C(r,{code:x.too_small,message:o.message,inclusive:!0,exact:!1,minimum:o.value,type:"date"}),n.dirty()):o.kind==="max"?t.data.getTime()>o.value&&(r=this._getOrReturnCtx(t,r),C(r,{code:x.too_big,message:o.message,inclusive:!0,exact:!1,maximum:o.value,type:"date"}),n.dirty()):ee.assertNever(o);return{status:n.value,value:new Date(t.data.getTime())}}_addCheck(t){return new Gt({...this._def,checks:[...this._def.checks,t]})}min(t,n){return this._addCheck({kind:"min",value:t.getTime(),message:$.toString(n)})}max(t,n){return this._addCheck({kind:"max",value:t.getTime(),message:$.toString(n)})}get minDate(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t!=null?new Date(t):null}}Gt.create=e=>new Gt({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:j.ZodDate,...W(e)});class Vr extends G{_parse(t){if(this._getType(t)!==E.symbol){const n=this._getOrReturnCtx(t);return C(n,{code:x.invalid_type,expected:E.symbol,received:n.parsedType}),F}return Re(t.data)}}Vr.create=e=>new Vr({typeName:j.ZodSymbol,...W(e)});class qn extends G{_parse(t){if(this._getType(t)!==E.undefined){const n=this._getOrReturnCtx(t);return C(n,{code:x.invalid_type,expected:E.undefined,received:n.parsedType}),F}return Re(t.data)}}qn.create=e=>new qn({typeName:j.ZodUndefined,...W(e)});class Gn extends G{_parse(t){if(this._getType(t)!==E.null){const n=this._getOrReturnCtx(t);return C(n,{code:x.invalid_type,expected:E.null,received:n.parsedType}),F}return Re(t.data)}}Gn.create=e=>new Gn({typeName:j.ZodNull,...W(e)});class un extends G{constructor(){super(...arguments),this._any=!0}_parse(t){return Re(t.data)}}un.create=e=>new un({typeName:j.ZodAny,...W(e)});class Wt extends G{constructor(){super(...arguments),this._unknown=!0}_parse(t){return Re(t.data)}}Wt.create=e=>new Wt({typeName:j.ZodUnknown,...W(e)});class vt extends G{_parse(t){const n=this._getOrReturnCtx(t);return C(n,{code:x.invalid_type,expected:E.never,received:n.parsedType}),F}}vt.create=e=>new vt({typeName:j.ZodNever,...W(e)});class Hr extends G{_parse(t){if(this._getType(t)!==E.undefined){const n=this._getOrReturnCtx(t);return C(n,{code:x.invalid_type,expected:E.void,received:n.parsedType}),F}return Re(t.data)}}Hr.create=e=>new Hr({typeName:j.ZodVoid,...W(e)});class Xe extends G{_parse(t){const{ctx:n,status:r}=this._processInputParams(t),o=this._def;if(n.parsedType!==E.array)return C(n,{code:x.invalid_type,expected:E.array,received:n.parsedType}),F;if(o.exactLength!==null){const i=n.data.length>o.exactLength.value,s=n.data.length<o.exactLength.value;(i||s)&&(C(n,{code:i?x.too_big:x.too_small,minimum:s?o.exactLength.value:void 0,maximum:i?o.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:o.exactLength.message}),r.dirty())}if(o.minLength!==null&&n.data.length<o.minLength.value&&(C(n,{code:x.too_small,minimum:o.minLength.value,type:"array",inclusive:!0,exact:!1,message:o.minLength.message}),r.dirty()),o.maxLength!==null&&n.data.length>o.maxLength.value&&(C(n,{code:x.too_big,maximum:o.maxLength.value,type:"array",inclusive:!0,exact:!1,message:o.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((i,s)=>o.type._parseAsync(new ct(n,i,n.path,s)))).then(i=>$e.mergeArray(r,i));const a=[...n.data].map((i,s)=>o.type._parseSync(new ct(n,i,n.path,s)));return $e.mergeArray(r,a)}get element(){return this._def.type}min(t,n){return new Xe({...this._def,minLength:{value:t,message:$.toString(n)}})}max(t,n){return new Xe({...this._def,maxLength:{value:t,message:$.toString(n)}})}length(t,n){return new Xe({...this._def,exactLength:{value:t,message:$.toString(n)}})}nonempty(t){return this.min(1,t)}}Xe.create=(e,t)=>new Xe({type:e,minLength:null,maxLength:null,exactLength:null,typeName:j.ZodArray,...W(t)});function en(e){if(e instanceof we){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=it.create(en(r))}return new we({...e._def,shape:()=>t})}else return e instanceof Xe?new Xe({...e._def,type:en(e.element)}):e instanceof it?it.create(en(e.unwrap())):e instanceof Pt?Pt.create(en(e.unwrap())):e instanceof lt?lt.create(e.items.map(t=>en(t))):e}class we extends G{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),n=ee.objectKeys(t);return this._cached={shape:t,keys:n}}_parse(t){if(this._getType(t)!==E.object){const c=this._getOrReturnCtx(t);return C(c,{code:x.invalid_type,expected:E.object,received:c.parsedType}),F}const{status:n,ctx:r}=this._processInputParams(t),{shape:o,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof vt&&this._def.unknownKeys==="strip"))for(const c in r.data)a.includes(c)||i.push(c);const s=[];for(const c of a){const l=o[c],u=r.data[c];s.push({key:{status:"valid",value:c},value:l._parse(new ct(r,u,r.path,c)),alwaysSet:c in r.data})}if(this._def.catchall instanceof vt){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)s.push({key:{status:"valid",value:l},value:{status:"valid",value:r.data[l]}});else if(c==="strict")i.length>0&&(C(r,{code:x.unrecognized_keys,keys:i}),n.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const u=r.data[l];s.push({key:{status:"valid",value:l},value:c._parse(new ct(r,u,r.path,l)),alwaysSet:l in r.data})}}return r.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of s){const u=await l.key,d=await l.value;c.push({key:u,value:d,alwaysSet:l.alwaysSet})}return c}).then(c=>$e.mergeObjectSync(n,c)):$e.mergeObjectSync(n,s)}get shape(){return this._def.shape()}strict(t){return $.errToObj,new we({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(n,r)=>{var o,a,i,s;const c=(i=(a=(o=this._def).errorMap)===null||a===void 0?void 0:a.call(o,n,r).message)!==null&&i!==void 0?i:r.defaultError;return n.code==="unrecognized_keys"?{message:(s=$.errToObj(t).message)!==null&&s!==void 0?s:c}:{message:c}}}:{}})}strip(){return new we({...this._def,unknownKeys:"strip"})}passthrough(){return new we({...this._def,unknownKeys:"passthrough"})}extend(t){return new we({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new we({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:j.ZodObject})}setKey(t,n){return this.augment({[t]:n})}catchall(t){return new we({...this._def,catchall:t})}pick(t){const n={};return ee.objectKeys(t).forEach(r=>{t[r]&&this.shape[r]&&(n[r]=this.shape[r])}),new we({...this._def,shape:()=>n})}omit(t){const n={};return ee.objectKeys(this.shape).forEach(r=>{t[r]||(n[r]=this.shape[r])}),new we({...this._def,shape:()=>n})}deepPartial(){return en(this)}partial(t){const n={};return ee.objectKeys(this.shape).forEach(r=>{const o=this.shape[r];t&&!t[r]?n[r]=o:n[r]=o.optional()}),new we({...this._def,shape:()=>n})}required(t){const n={};return ee.objectKeys(this.shape).forEach(r=>{if(t&&!t[r])n[r]=this.shape[r];else{let o=this.shape[r];for(;o instanceof it;)o=o._def.innerType;n[r]=o}}),new we({...this._def,shape:()=>n})}keyof(){return dc(ee.objectKeys(this.shape))}}we.create=(e,t)=>new we({shape:()=>e,unknownKeys:"strip",catchall:vt.create(),typeName:j.ZodObject,...W(t)});we.strictCreate=(e,t)=>new we({shape:()=>e,unknownKeys:"strict",catchall:vt.create(),typeName:j.ZodObject,...W(t)});we.lazycreate=(e,t)=>new we({shape:e,unknownKeys:"strip",catchall:vt.create(),typeName:j.ZodObject,...W(t)});class Zn extends G{_parse(t){const{ctx:n}=this._processInputParams(t),r=this._def.options;function o(a){for(const s of a)if(s.result.status==="valid")return s.result;for(const s of a)if(s.result.status==="dirty")return n.common.issues.push(...s.ctx.common.issues),s.result;const i=a.map(s=>new He(s.ctx.common.issues));return C(n,{code:x.invalid_union,unionErrors:i}),F}if(n.common.async)return Promise.all(r.map(async a=>{const i={...n,common:{...n.common,issues:[]},parent:null};return{result:await a._parseAsync({data:n.data,path:n.path,parent:i}),ctx:i}})).then(o);{let a;const i=[];for(const c of r){const l={...n,common:{...n.common,issues:[]},parent:null},u=c._parseSync({data:n.data,path:n.path,parent:l});if(u.status==="valid")return u;u.status==="dirty"&&!a&&(a={result:u,ctx:l}),l.common.issues.length&&i.push(l.common.issues)}if(a)return n.common.issues.push(...a.ctx.common.issues),a.result;const s=i.map(c=>new He(c));return C(n,{code:x.invalid_union,unionErrors:s}),F}}get options(){return this._def.options}}Zn.create=(e,t)=>new Zn({options:e,typeName:j.ZodUnion,...W(t)});const kt=e=>e instanceof Qn?kt(e.schema):e instanceof et?kt(e.innerType()):e instanceof Xn?[e.value]:e instanceof $t?e.options:e instanceof Jn?ee.objectValues(e.enum):e instanceof er?kt(e._def.innerType):e instanceof qn?[void 0]:e instanceof Gn?[null]:e instanceof it?[void 0,...kt(e.unwrap())]:e instanceof Pt?[null,...kt(e.unwrap())]:e instanceof Na||e instanceof nr?kt(e.unwrap()):e instanceof tr?kt(e._def.innerType):[];class ao extends G{_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==E.object)return C(n,{code:x.invalid_type,expected:E.object,received:n.parsedType}),F;const r=this.discriminator,o=n.data[r],a=this.optionsMap.get(o);return a?n.common.async?a._parseAsync({data:n.data,path:n.path,parent:n}):a._parseSync({data:n.data,path:n.path,parent:n}):(C(n,{code:x.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),F)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,n,r){const o=new Map;for(const a of n){const i=kt(a.shape[t]);if(!i.length)throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(const s of i){if(o.has(s))throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(s)}`);o.set(s,a)}}return new ao({typeName:j.ZodDiscriminatedUnion,discriminator:t,options:n,optionsMap:o,...W(r)})}}function Zo(e,t){const n=ft(e),r=ft(t);if(e===t)return{valid:!0,data:e};if(n===E.object&&r===E.object){const o=ee.objectKeys(t),a=ee.objectKeys(e).filter(s=>o.indexOf(s)!==-1),i={...e,...t};for(const s of a){const c=Zo(e[s],t[s]);if(!c.valid)return{valid:!1};i[s]=c.data}return{valid:!0,data:i}}else if(n===E.array&&r===E.array){if(e.length!==t.length)return{valid:!1};const o=[];for(let a=0;a<e.length;a++){const i=e[a],s=t[a],c=Zo(i,s);if(!c.valid)return{valid:!1};o.push(c.data)}return{valid:!0,data:o}}else return n===E.date&&r===E.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class Yn extends G{_parse(t){const{status:n,ctx:r}=this._processInputParams(t),o=(a,i)=>{if(qo(a)||qo(i))return F;const s=Zo(a.value,i.value);return s.valid?((Go(a)||Go(i))&&n.dirty(),{status:n.value,value:s.data}):(C(r,{code:x.invalid_intersection_types}),F)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([a,i])=>o(a,i)):o(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}Yn.create=(e,t,n)=>new Yn({left:e,right:t,typeName:j.ZodIntersection,...W(n)});class lt extends G{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==E.array)return C(r,{code:x.invalid_type,expected:E.array,received:r.parsedType}),F;if(r.data.length<this._def.items.length)return C(r,{code:x.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),F;!this._def.rest&&r.data.length>this._def.items.length&&(C(r,{code:x.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const o=[...r.data].map((a,i)=>{const s=this._def.items[i]||this._def.rest;return s?s._parse(new ct(r,a,r.path,i)):null}).filter(a=>!!a);return r.common.async?Promise.all(o).then(a=>$e.mergeArray(n,a)):$e.mergeArray(n,o)}get items(){return this._def.items}rest(t){return new lt({...this._def,rest:t})}}lt.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new lt({items:e,typeName:j.ZodTuple,rest:null,...W(t)})};class Kn extends G{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==E.object)return C(r,{code:x.invalid_type,expected:E.object,received:r.parsedType}),F;const o=[],a=this._def.keyType,i=this._def.valueType;for(const s in r.data)o.push({key:a._parse(new ct(r,s,r.path,s)),value:i._parse(new ct(r,r.data[s],r.path,s)),alwaysSet:s in r.data});return r.common.async?$e.mergeObjectAsync(n,o):$e.mergeObjectSync(n,o)}get element(){return this._def.valueType}static create(t,n,r){return n instanceof G?new Kn({keyType:t,valueType:n,typeName:j.ZodRecord,...W(r)}):new Kn({keyType:Ke.create(),valueType:t,typeName:j.ZodRecord,...W(n)})}}class Wr extends G{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==E.map)return C(r,{code:x.invalid_type,expected:E.map,received:r.parsedType}),F;const o=this._def.keyType,a=this._def.valueType,i=[...r.data.entries()].map(([s,c],l)=>({key:o._parse(new ct(r,s,r.path,[l,"key"])),value:a._parse(new ct(r,c,r.path,[l,"value"]))}));if(r.common.async){const s=new Map;return Promise.resolve().then(async()=>{for(const c of i){const l=await c.key,u=await c.value;if(l.status==="aborted"||u.status==="aborted")return F;(l.status==="dirty"||u.status==="dirty")&&n.dirty(),s.set(l.value,u.value)}return{status:n.value,value:s}})}else{const s=new Map;for(const c of i){const l=c.key,u=c.value;if(l.status==="aborted"||u.status==="aborted")return F;(l.status==="dirty"||u.status==="dirty")&&n.dirty(),s.set(l.value,u.value)}return{status:n.value,value:s}}}}Wr.create=(e,t,n)=>new Wr({valueType:t,keyType:e,typeName:j.ZodMap,...W(n)});class Zt extends G{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==E.set)return C(r,{code:x.invalid_type,expected:E.set,received:r.parsedType}),F;const o=this._def;o.minSize!==null&&r.data.size<o.minSize.value&&(C(r,{code:x.too_small,minimum:o.minSize.value,type:"set",inclusive:!0,exact:!1,message:o.minSize.message}),n.dirty()),o.maxSize!==null&&r.data.size>o.maxSize.value&&(C(r,{code:x.too_big,maximum:o.maxSize.value,type:"set",inclusive:!0,exact:!1,message:o.maxSize.message}),n.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const u of c){if(u.status==="aborted")return F;u.status==="dirty"&&n.dirty(),l.add(u.value)}return{status:n.value,value:l}}const s=[...r.data.values()].map((c,l)=>a._parse(new ct(r,c,r.path,l)));return r.common.async?Promise.all(s).then(c=>i(c)):i(s)}min(t,n){return new Zt({...this._def,minSize:{value:t,message:$.toString(n)}})}max(t,n){return new Zt({...this._def,maxSize:{value:t,message:$.toString(n)}})}size(t,n){return this.min(t,n).max(t,n)}nonempty(t){return this.min(1,t)}}Zt.create=(e,t)=>new Zt({valueType:e,minSize:null,maxSize:null,typeName:j.ZodSet,...W(t)});class rn extends G{constructor(){super(...arguments),this.validate=this.implement}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==E.function)return C(n,{code:x.invalid_type,expected:E.function,received:n.parsedType}),F;function r(s,c){return Fr({data:s,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,Lr(),dn].filter(l=>!!l),issueData:{code:x.invalid_arguments,argumentsError:c}})}function o(s,c){return Fr({data:s,path:n.path,errorMaps:[n.common.contextualErrorMap,n.schemaErrorMap,Lr(),dn].filter(l=>!!l),issueData:{code:x.invalid_return_type,returnTypeError:c}})}const a={errorMap:n.common.contextualErrorMap},i=n.data;if(this._def.returns instanceof pn){const s=this;return Re(async function(...c){const l=new He([]),u=await s._def.args.parseAsync(c,a).catch(p=>{throw l.addIssue(r(c,p)),l}),d=await Reflect.apply(i,this,u);return await s._def.returns._def.type.parseAsync(d,a).catch(p=>{throw l.addIssue(o(d,p)),l})})}else{const s=this;return Re(function(...c){const l=s._def.args.safeParse(c,a);if(!l.success)throw new He([r(c,l.error)]);const u=Reflect.apply(i,this,l.data),d=s._def.returns.safeParse(u,a);if(!d.success)throw new He([o(u,d.error)]);return d.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...t){return new rn({...this._def,args:lt.create(t).rest(Wt.create())})}returns(t){return new rn({...this._def,returns:t})}implement(t){return this.parse(t)}strictImplement(t){return this.parse(t)}static create(t,n,r){return new rn({args:t||lt.create([]).rest(Wt.create()),returns:n||Wt.create(),typeName:j.ZodFunction,...W(r)})}}class Qn extends G{get schema(){return this._def.getter()}_parse(t){const{ctx:n}=this._processInputParams(t);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}Qn.create=(e,t)=>new Qn({getter:e,typeName:j.ZodLazy,...W(t)});class Xn extends G{_parse(t){if(t.data!==this._def.value){const n=this._getOrReturnCtx(t);return C(n,{received:n.data,code:x.invalid_literal,expected:this._def.value}),F}return{status:"valid",value:t.data}}get value(){return this._def.value}}Xn.create=(e,t)=>new Xn({value:e,typeName:j.ZodLiteral,...W(t)});function dc(e,t){return new $t({values:e,typeName:j.ZodEnum,...W(t)})}class $t extends G{constructor(){super(...arguments),En.set(this,void 0)}_parse(t){if(typeof t.data!="string"){const n=this._getOrReturnCtx(t),r=this._def.values;return C(n,{expected:ee.joinValues(r),received:n.parsedType,code:x.invalid_type}),F}if(Br(this,En)||ic(this,En,new Set(this._def.values)),!Br(this,En).has(t.data)){const n=this._getOrReturnCtx(t),r=this._def.values;return C(n,{received:n.data,code:x.invalid_enum_value,options:r}),F}return Re(t.data)}get options(){return this._def.values}get enum(){const t={};for(const n of this._def.values)t[n]=n;return t}get Values(){const t={};for(const n of this._def.values)t[n]=n;return t}get Enum(){const t={};for(const n of this._def.values)t[n]=n;return t}extract(t,n=this._def){return $t.create(t,{...this._def,...n})}exclude(t,n=this._def){return $t.create(this.options.filter(r=>!t.includes(r)),{...this._def,...n})}}En=new WeakMap;$t.create=dc;class Jn extends G{constructor(){super(...arguments),Nn.set(this,void 0)}_parse(t){const n=ee.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==E.string&&r.parsedType!==E.number){const o=ee.objectValues(n);return C(r,{expected:ee.joinValues(o),received:r.parsedType,code:x.invalid_type}),F}if(Br(this,Nn)||ic(this,Nn,new Set(ee.getValidEnumValues(this._def.values))),!Br(this,Nn).has(t.data)){const o=ee.objectValues(n);return C(r,{received:r.data,code:x.invalid_enum_value,options:o}),F}return Re(t.data)}get enum(){return this._def.values}}Nn=new WeakMap;Jn.create=(e,t)=>new Jn({values:e,typeName:j.ZodNativeEnum,...W(t)});class pn extends G{unwrap(){return this._def.type}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==E.promise&&n.common.async===!1)return C(n,{code:x.invalid_type,expected:E.promise,received:n.parsedType}),F;const r=n.parsedType===E.promise?n.data:Promise.resolve(n.data);return Re(r.then(o=>this._def.type.parseAsync(o,{path:n.path,errorMap:n.common.contextualErrorMap})))}}pn.create=(e,t)=>new pn({type:e,typeName:j.ZodPromise,...W(t)});class et extends G{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===j.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:n,ctx:r}=this._processInputParams(t),o=this._def.effect||null,a={addIssue:i=>{C(r,i),i.fatal?n.abort():n.dirty()},get path(){return r.path}};if(a.addIssue=a.addIssue.bind(a),o.type==="preprocess"){const i=o.transform(r.data,a);if(r.common.async)return Promise.resolve(i).then(async s=>{if(n.value==="aborted")return F;const c=await this._def.schema._parseAsync({data:s,path:r.path,parent:r});return c.status==="aborted"?F:c.status==="dirty"||n.value==="dirty"?Dr(c.value):c});{if(n.value==="aborted")return F;const s=this._def.schema._parseSync({data:i,path:r.path,parent:r});return s.status==="aborted"?F:s.status==="dirty"||n.value==="dirty"?Dr(s.value):s}}if(o.type==="refinement"){const i=s=>{const c=o.refinement(s,a);if(r.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return s};if(r.common.async===!1){const s=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?F:(s.status==="dirty"&&n.dirty(),i(s.value),{status:n.value,value:s.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(s=>s.status==="aborted"?F:(s.status==="dirty"&&n.dirty(),i(s.value).then(()=>({status:n.value,value:s.value}))))}if(o.type==="transform")if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!qt(i))return i;const s=o.transform(i.value,a);if(s instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:s}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>qt(i)?Promise.resolve(o.transform(i.value,a)).then(s=>({status:n.value,value:s})):i);ee.assertNever(o)}}et.create=(e,t,n)=>new et({schema:e,typeName:j.ZodEffects,effect:t,...W(n)});et.createWithPreprocess=(e,t,n)=>new et({schema:t,effect:{type:"preprocess",transform:e},typeName:j.ZodEffects,...W(n)});class it extends G{_parse(t){return this._getType(t)===E.undefined?Re(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}it.create=(e,t)=>new it({innerType:e,typeName:j.ZodOptional,...W(t)});class Pt extends G{_parse(t){return this._getType(t)===E.null?Re(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Pt.create=(e,t)=>new Pt({innerType:e,typeName:j.ZodNullable,...W(t)});class er extends G{_parse(t){const{ctx:n}=this._processInputParams(t);let r=n.data;return n.parsedType===E.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}er.create=(e,t)=>new er({innerType:e,typeName:j.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...W(t)});class tr extends G{_parse(t){const{ctx:n}=this._processInputParams(t),r={...n,common:{...n.common,issues:[]}},o=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return Wn(o)?o.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new He(r.common.issues)},input:r.data})})):{status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new He(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}tr.create=(e,t)=>new tr({innerType:e,typeName:j.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...W(t)});class Ur extends G{_parse(t){if(this._getType(t)!==E.nan){const n=this._getOrReturnCtx(t);return C(n,{code:x.invalid_type,expected:E.nan,received:n.parsedType}),F}return{status:"valid",value:t.data}}}Ur.create=e=>new Ur({typeName:j.ZodNaN,...W(e)});const Wp=Symbol("zod_brand");class Na extends G{_parse(t){const{ctx:n}=this._processInputParams(t),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class pr extends G{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.common.async)return(async()=>{const o=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return o.status==="aborted"?F:o.status==="dirty"?(n.dirty(),Dr(o.value)):this._def.out._parseAsync({data:o.value,path:r.path,parent:r})})();{const o=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return o.status==="aborted"?F:o.status==="dirty"?(n.dirty(),{status:"dirty",value:o.value}):this._def.out._parseSync({data:o.value,path:r.path,parent:r})}}static create(t,n){return new pr({in:t,out:n,typeName:j.ZodPipeline})}}class nr extends G{_parse(t){const n=this._def.innerType._parse(t),r=o=>(qt(o)&&(o.value=Object.freeze(o.value)),o);return Wn(n)?n.then(o=>r(o)):r(n)}unwrap(){return this._def.innerType}}nr.create=(e,t)=>new nr({innerType:e,typeName:j.ZodReadonly,...W(t)});function Pi(e,t){const n=typeof e=="function"?e(t):typeof e=="string"?{message:e}:e;return typeof n=="string"?{message:n}:n}function uc(e,t={},n){return e?un.create().superRefine((r,o)=>{var a,i;const s=e(r);if(s instanceof Promise)return s.then(c=>{var l,u;if(!c){const d=Pi(t,r),p=(u=(l=d.fatal)!==null&&l!==void 0?l:n)!==null&&u!==void 0?u:!0;o.addIssue({code:"custom",...d,fatal:p})}});if(!s){const c=Pi(t,r),l=(i=(a=c.fatal)!==null&&a!==void 0?a:n)!==null&&i!==void 0?i:!0;o.addIssue({code:"custom",...c,fatal:l})}}):un.create()}const Up={object:we.lazycreate};var j;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(j||(j={}));const qp=(e,t={message:`Input not instance of ${e.name}`})=>uc(n=>n instanceof e,t),pc=Ke.create,hc=Mt.create,Gp=Ur.create,Zp=It.create,mc=Un.create,Yp=Gt.create,Kp=Vr.create,Qp=qn.create,Xp=Gn.create,Jp=un.create,eh=Wt.create,th=vt.create,nh=Hr.create,rh=Xe.create,oh=we.create,ah=we.strictCreate,ih=Zn.create,sh=ao.create,ch=Yn.create,lh=lt.create,dh=Kn.create,uh=Wr.create,ph=Zt.create,hh=rn.create,mh=Qn.create,fh=Xn.create,gh=$t.create,vh=Jn.create,wh=pn.create,Ti=et.create,bh=it.create,yh=Pt.create,_h=et.createWithPreprocess,xh=pr.create,kh=()=>pc().optional(),Sh=()=>hc().optional(),Ch=()=>mc().optional(),Eh={string:e=>Ke.create({...e,coerce:!0}),number:e=>Mt.create({...e,coerce:!0}),boolean:e=>Un.create({...e,coerce:!0}),bigint:e=>It.create({...e,coerce:!0}),date:e=>Gt.create({...e,coerce:!0})},Nh=F;var ue=Object.freeze({__proto__:null,defaultErrorMap:dn,setErrorMap:_p,getErrorMap:Lr,makeIssue:Fr,EMPTY_PATH:xp,addIssueToContext:C,ParseStatus:$e,INVALID:F,DIRTY:Dr,OK:Re,isAborted:qo,isDirty:Go,isValid:qt,isAsync:Wn,get util(){return ee},get objectUtil(){return Uo},ZodParsedType:E,getParsedType:ft,ZodType:G,datetimeRegex:lc,ZodString:Ke,ZodNumber:Mt,ZodBigInt:It,ZodBoolean:Un,ZodDate:Gt,ZodSymbol:Vr,ZodUndefined:qn,ZodNull:Gn,ZodAny:un,ZodUnknown:Wt,ZodNever:vt,ZodVoid:Hr,ZodArray:Xe,ZodObject:we,ZodUnion:Zn,ZodDiscriminatedUnion:ao,ZodIntersection:Yn,ZodTuple:lt,ZodRecord:Kn,ZodMap:Wr,ZodSet:Zt,ZodFunction:rn,ZodLazy:Qn,ZodLiteral:Xn,ZodEnum:$t,ZodNativeEnum:Jn,ZodPromise:pn,ZodEffects:et,ZodTransformer:et,ZodOptional:it,ZodNullable:Pt,ZodDefault:er,ZodCatch:tr,ZodNaN:Ur,BRAND:Wp,ZodBranded:Na,ZodPipeline:pr,ZodReadonly:nr,custom:uc,Schema:G,ZodSchema:G,late:Up,get ZodFirstPartyTypeKind(){return j},coerce:Eh,any:Jp,array:rh,bigint:Zp,boolean:mc,date:Yp,discriminatedUnion:sh,effect:Ti,enum:gh,function:hh,instanceof:qp,intersection:ch,lazy:mh,literal:fh,map:uh,nan:Gp,nativeEnum:vh,never:th,null:Xp,nullable:yh,number:hc,object:oh,oboolean:Ch,onumber:Sh,optional:bh,ostring:kh,pipeline:xh,preprocess:_h,promise:wh,record:dh,set:ph,strictObject:ah,string:pc,symbol:Kp,transformer:Ti,tuple:lh,undefined:Qp,union:ih,unknown:eh,void:nh,NEVER:Nh,ZodIssueCode:x,quotelessJson:yp,ZodError:He}),Ah=5746,Mh="/ping/stagewise",Ih="stagewise",fc={server:{getSessionInfo:{request:ue.object({}),response:ue.object({sessionId:ue.string().optional(),appName:ue.string().describe('The name of the application, e.g. "VS Code" or "Cursor"'),displayName:ue.string().describe("Human-readable window identifier for UI display"),port:ue.number().describe("Port number this VS Code instance is running on")}),update:ue.object({})},triggerAgentPrompt:{request:ue.object({sessionId:ue.string().optional(),prompt:ue.string(),model:ue.string().optional().describe("The model to use for the agent prompt"),files:ue.array(ue.string()).optional().describe("Link project files to the agent prompt"),mode:ue.enum(["agent","ask","manual"]).optional().describe("The mode to use for the agent prompt"),images:ue.array(ue.string()).optional().describe("Upload files like images, videos, etc.")}),response:ue.object({sessionId:ue.string().optional(),result:ue.object({success:ue.boolean(),error:ue.string().optional(),errorCode:ue.enum(["session_mismatch"]).optional(),output:ue.string().optional()})}),update:ue.object({sessionId:ue.string().optional(),updateText:ue.string()})}}};const $h=2;async function Ph(e=10,t=300){const n=[];let r=0;for(let o=0;o<e;o++){const a=Ah+o;try{const i=new AbortController,s=setTimeout(()=>i.abort(),t),c=await fetch(`http://localhost:${a}${Mh}`,{signal:i.signal});if(clearTimeout(s),r=0,c.ok&&await c.text()===Ih)try{const l=oc(`ws://localhost:${a}`,fc);await l.connect();const u=await l.call.getSessionInfo({},{onUpdate:()=>{}});n.push(u),await l.close()}catch(l){console.warn(`Failed to get session info from port ${a}:`,l)}else continue}catch{if(r++,r>=$h){console.warn("⬆️⬆️⬆️ Those two errors are expected! (Everything is fine, they are part of stagewise's discovery mechanism!) ✅");break}continue}}return n.length===0&&console.warn("No IDE windows found, please start an IDE with the stagewise extension installed! ❌"),n}const Th=()=>typeof window<"u"&&window.location&&window.location.port||"80",Yo=()=>`ide-selected-session-id-on-browser-port-${Th()}`,Ri=()=>{try{return localStorage.getItem(Yo())||void 0}catch{return}},xr=e=>{try{e?localStorage.setItem(Yo(),e):localStorage.removeItem(Yo())}catch{}},gc=Ge({windows:[],isDiscovering:!1,discoveryError:null,selectedSession:void 0,shouldPromptWindowSelection:!1,setShouldPromptWindowSelection:()=>{},discover:async()=>{},selectSession:()=>{},refreshSession:async()=>{},appName:void 0});function Rh({children:e}){const[t,n]=ie([]),[r,o]=ie(!1),[a,i]=ie(null),[s,c]=ie(Ri()),[l,u]=ie(!1),d=async()=>{o(!0),i(null);try{const w=await Ph();n(w);const b=Ri();if(w.length===1){const y=w[0];(!b||b!==y.sessionId)&&(c(y.sessionId),xr(y.sessionId)),u(!1)}else{const y=w.length>1&&!b||b&&!w.some(S=>S.sessionId===b);u(y),y&&(c(void 0),xr(void 0))}}catch(w){i(w instanceof Error?w.message:"Failed to discover windows")}finally{o(!1)}},p=w=>{if(!w||w===""){xr(void 0),c(void 0);return}c(w),xr(w),w&&u(!1)},m=async()=>{s&&await d()};fe(()=>{d()},[]);const v=t.find(w=>w.sessionId===s),g={windows:t,isDiscovering:r,discoveryError:a,selectedSession:v,shouldPromptWindowSelection:l,setShouldPromptWindowSelection:u,discover:d,selectSession:p,refreshSession:m,appName:v==null?void 0:v.appName};return h(gc.Provider,{value:g,children:e})}function Tt(){return Pe(gc)}const vc=Ge({bridge:null,isConnecting:!1,error:null});function zh({children:e}){const[t,n]=ie({bridge:null,isConnecting:!0,error:null}),{selectedSession:r}=Tt(),o=X(null),a=P(async i=>{o.current&&await o.current.close();try{const s=oc(`ws://localhost:${i}`,fc);await s.connect(),o.current=s,n({bridge:s,isConnecting:!1,error:null})}catch(s){o.current=null,n({bridge:null,isConnecting:!1,error:s instanceof Error?s:new Error(String(s))})}},[]);return fe(()=>{r&&a(r.port)},[r,a]),h(vc.Provider,{value:t,children:e})}function wc(){const e=Pe(vc);if(!e)throw new Error("useSRPCBridge must be used within an SRPCBridgeProvider");return e}const bc=Ge({config:void 0});function Oh({children:e,config:t}){const n=be(()=>({config:t}),[t]);return h(bc.Provider,{value:n,children:e})}function jh(){return Pe(bc)}const yc=Ge({plugins:[],toolbarContext:{sendPrompt:()=>{}}});function Lh({children:e}){const{bridge:t}=wc(),{selectedSession:n}=Tt(),{config:r}=jh(),o=(r==null?void 0:r.plugins)||[],a=be(()=>({sendPrompt:async c=>{if(!t)throw new Error("No connection to the agent");return await t.call.triggerAgentPrompt(typeof c=="string"?{prompt:c,...n&&{sessionId:n.sessionId}}:{prompt:c.prompt,model:c.model,files:c.files,images:c.images,mode:c.mode,...n&&{sessionId:n.sessionId}},{onUpdate:l=>{}})}}),[t,n]),i=X(!1);fe(()=>{i.current||(i.current=!0,o.forEach(c=>{var l;(l=c.onLoad)==null||l.call(c,a)}))},[o,a]);const s=be(()=>({plugins:o,toolbarContext:a}),[o,a]);return h(yc.Provider,{value:s,children:e})}function io(){return Pe(yc)}function _c(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=_c(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Fh(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=_c(e))&&(r&&(r+=" "),r+=t);return r}const Aa="-",Dh=e=>{const t=Vh(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:o=>{const a=o.split(Aa);return a[0]===""&&a.length!==1&&a.shift(),xc(a,t)||Bh(o)},getConflictingClassGroupIds:(o,a)=>{const i=n[o]||[];return a&&r[o]?[...i,...r[o]]:i}}},xc=(e,t)=>{var n;if(e.length===0)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),a=o?xc(e.slice(1),o):void 0;if(a)return a;if(t.validators.length===0)return;const i=e.join(Aa);return(n=t.validators.find(({validator:s})=>s(i)))==null?void 0:n.classGroupId},zi=/^\[(.+)\]$/,Bh=e=>{if(zi.test(e)){const t=zi.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Vh=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const o in n)Ko(n[o],r,o,t);return r},Ko=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const a=o===""?t:Oi(t,o);a.classGroupId=n;return}if(typeof o=="function"){if(Hh(o)){Ko(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([a,i])=>{Ko(i,Oi(t,a),n,r)})})},Oi=(e,t)=>{let n=e;return t.split(Aa).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Hh=e=>e.isThemeGetter,Wh=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(a,i)=>{n.set(a,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(a){let i=n.get(a);if(i!==void 0)return i;if((i=r.get(a))!==void 0)return o(a,i),i},set(a,i){n.has(a)?n.set(a,i):o(a,i)}}},Qo="!",Xo=":",Uh=Xo.length,qh=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=o=>{const a=[];let i=0,s=0,c=0,l;for(let v=0;v<o.length;v++){let g=o[v];if(i===0&&s===0){if(g===Xo){a.push(o.slice(c,v)),c=v+Uh;continue}if(g==="/"){l=v;continue}}g==="["?i++:g==="]"?i--:g==="("?s++:g===")"&&s--}const u=a.length===0?o:o.substring(c),d=Gh(u),p=d!==u,m=l&&l>c?l-c:void 0;return{modifiers:a,hasImportantModifier:p,baseClassName:d,maybePostfixModifierPosition:m}};if(t){const o=t+Xo,a=r;r=i=>i.startsWith(o)?a(i.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(n){const o=r;r=a=>n({className:a,parseClassName:o})}return r},Gh=e=>e.endsWith(Qo)?e.substring(0,e.length-1):e.startsWith(Qo)?e.substring(1):e,Zh=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const r=[];let o=[];return n.forEach(a=>{a[0]==="["||t[a]?(r.push(...o.sort(),a),o=[]):o.push(a)}),r.push(...o.sort()),r}},Yh=e=>({cache:Wh(e.cacheSize),parseClassName:qh(e),sortModifiers:Zh(e),...Dh(e)}),Kh=/\s+/,Qh=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o,sortModifiers:a}=t,i=[],s=e.trim().split(Kh);let c="";for(let l=s.length-1;l>=0;l-=1){const u=s[l],{isExternal:d,modifiers:p,hasImportantModifier:m,baseClassName:v,maybePostfixModifierPosition:g}=n(u);if(d){c=u+(c.length>0?" "+c:c);continue}let w=!!g,b=r(w?v.substring(0,g):v);if(!b){if(!w){c=u+(c.length>0?" "+c:c);continue}if(b=r(v),!b){c=u+(c.length>0?" "+c:c);continue}w=!1}const y=a(p).join(":"),S=m?y+Qo:y,M=S+b;if(i.includes(M))continue;i.push(M);const O=o(b,w);for(let re=0;re<O.length;++re){const Z=O[re];i.push(S+Z)}c=u+(c.length>0?" "+c:c)}return c};function Xh(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=kc(t))&&(r&&(r+=" "),r+=n);return r}const kc=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=kc(e[r]))&&(n&&(n+=" "),n+=t);return n};function ji(e,...t){let n,r,o,a=i;function i(c){const l=t.reduce((u,d)=>d(u),e());return n=Yh(l),r=n.cache.get,o=n.cache.set,a=s,s(c)}function s(c){const l=r(c);if(l)return l;const u=Qh(c,n);return o(c,u),u}return function(){return a(Xh.apply(null,arguments))}}const Ne=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Sc=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Cc=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Jh=/^\d+\/\d+$/,em=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,tm=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,nm=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,rm=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,om=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Xt=e=>Jh.test(e),K=e=>!!e&&!Number.isNaN(Number(e)),xt=e=>!!e&&Number.isInteger(Number(e)),yo=e=>e.endsWith("%")&&K(e.slice(0,-1)),ht=e=>em.test(e),am=()=>!0,im=e=>tm.test(e)&&!nm.test(e),Ec=()=>!1,sm=e=>rm.test(e),cm=e=>om.test(e),lm=e=>!N(e)&&!A(e),dm=e=>vn(e,Mc,Ec),N=e=>Sc.test(e),Ft=e=>vn(e,Ic,im),_o=e=>vn(e,fm,K),Li=e=>vn(e,Nc,Ec),um=e=>vn(e,Ac,cm),kr=e=>vn(e,$c,sm),A=e=>Cc.test(e),kn=e=>wn(e,Ic),pm=e=>wn(e,gm),Fi=e=>wn(e,Nc),hm=e=>wn(e,Mc),mm=e=>wn(e,Ac),Sr=e=>wn(e,$c,!0),vn=(e,t,n)=>{const r=Sc.exec(e);return r?r[1]?t(r[1]):n(r[2]):!1},wn=(e,t,n=!1)=>{const r=Cc.exec(e);return r?r[1]?t(r[1]):n:!1},Nc=e=>e==="position"||e==="percentage",Ac=e=>e==="image"||e==="url",Mc=e=>e==="length"||e==="size"||e==="bg-size",Ic=e=>e==="length",fm=e=>e==="number",gm=e=>e==="family-name",$c=e=>e==="shadow",Di=()=>{const e=Ne("color"),t=Ne("font"),n=Ne("text"),r=Ne("font-weight"),o=Ne("tracking"),a=Ne("leading"),i=Ne("breakpoint"),s=Ne("container"),c=Ne("spacing"),l=Ne("radius"),u=Ne("shadow"),d=Ne("inset-shadow"),p=Ne("text-shadow"),m=Ne("drop-shadow"),v=Ne("blur"),g=Ne("perspective"),w=Ne("aspect"),b=Ne("ease"),y=Ne("animate"),S=()=>["auto","avoid","all","avoid-page","page","left","right","column"],M=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],O=()=>[...M(),A,N],re=()=>["auto","hidden","clip","visible","scroll"],Z=()=>["auto","contain","none"],I=()=>[A,N,c],Q=()=>[Xt,"full","auto",...I()],Ce=()=>[xt,"none","subgrid",A,N],te=()=>["auto",{span:["full",xt,A,N]},xt,A,N],ye=()=>[xt,"auto",A,N],B=()=>["auto","min","max","fr",A,N],U=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],pe=()=>["start","end","center","stretch","center-safe","end-safe"],ne=()=>["auto",...I()],Ee=()=>[Xt,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...I()],R=()=>[e,A,N],V=()=>[...M(),Fi,Li,{position:[A,N]}],de=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ce=()=>["auto","cover","contain",hm,dm,{size:[A,N]}],z=()=>[yo,kn,Ft],T=()=>["","none","full",l,A,N],Y=()=>["",K,kn,Ft],ae=()=>["solid","dashed","dotted","double"],_e=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],D=()=>[K,yo,Fi,Li],oe=()=>["","none",v,A,N],ge=()=>["none",K,A,N],he=()=>["none",K,A,N],Ae=()=>[K,A,N],Fe=()=>[Xt,"full",...I()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ht],breakpoint:[ht],color:[am],container:[ht],"drop-shadow":[ht],ease:["in","out","in-out"],font:[lm],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ht],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ht],shadow:[ht],spacing:["px",K],text:[ht],"text-shadow":[ht],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Xt,N,A,w]}],container:["container"],columns:[{columns:[K,N,A,s]}],"break-after":[{"break-after":S()}],"break-before":[{"break-before":S()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:O()}],overflow:[{overflow:re()}],"overflow-x":[{"overflow-x":re()}],"overflow-y":[{"overflow-y":re()}],overscroll:[{overscroll:Z()}],"overscroll-x":[{"overscroll-x":Z()}],"overscroll-y":[{"overscroll-y":Z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Q()}],"inset-x":[{"inset-x":Q()}],"inset-y":[{"inset-y":Q()}],start:[{start:Q()}],end:[{end:Q()}],top:[{top:Q()}],right:[{right:Q()}],bottom:[{bottom:Q()}],left:[{left:Q()}],visibility:["visible","invisible","collapse"],z:[{z:[xt,"auto",A,N]}],basis:[{basis:[Xt,"full","auto",s,...I()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[K,Xt,"auto","initial","none",N]}],grow:[{grow:["",K,A,N]}],shrink:[{shrink:["",K,A,N]}],order:[{order:[xt,"first","last","none",A,N]}],"grid-cols":[{"grid-cols":Ce()}],"col-start-end":[{col:te()}],"col-start":[{"col-start":ye()}],"col-end":[{"col-end":ye()}],"grid-rows":[{"grid-rows":Ce()}],"row-start-end":[{row:te()}],"row-start":[{"row-start":ye()}],"row-end":[{"row-end":ye()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":B()}],"auto-rows":[{"auto-rows":B()}],gap:[{gap:I()}],"gap-x":[{"gap-x":I()}],"gap-y":[{"gap-y":I()}],"justify-content":[{justify:[...U(),"normal"]}],"justify-items":[{"justify-items":[...pe(),"normal"]}],"justify-self":[{"justify-self":["auto",...pe()]}],"align-content":[{content:["normal",...U()]}],"align-items":[{items:[...pe(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...pe(),{baseline:["","last"]}]}],"place-content":[{"place-content":U()}],"place-items":[{"place-items":[...pe(),"baseline"]}],"place-self":[{"place-self":["auto",...pe()]}],p:[{p:I()}],px:[{px:I()}],py:[{py:I()}],ps:[{ps:I()}],pe:[{pe:I()}],pt:[{pt:I()}],pr:[{pr:I()}],pb:[{pb:I()}],pl:[{pl:I()}],m:[{m:ne()}],mx:[{mx:ne()}],my:[{my:ne()}],ms:[{ms:ne()}],me:[{me:ne()}],mt:[{mt:ne()}],mr:[{mr:ne()}],mb:[{mb:ne()}],ml:[{ml:ne()}],"space-x":[{"space-x":I()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":I()}],"space-y-reverse":["space-y-reverse"],size:[{size:Ee()}],w:[{w:[s,"screen",...Ee()]}],"min-w":[{"min-w":[s,"screen","none",...Ee()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[i]},...Ee()]}],h:[{h:["screen",...Ee()]}],"min-h":[{"min-h":["screen","none",...Ee()]}],"max-h":[{"max-h":["screen",...Ee()]}],"font-size":[{text:["base",n,kn,Ft]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,A,_o]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",yo,N]}],"font-family":[{font:[pm,N,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,A,N]}],"line-clamp":[{"line-clamp":[K,"none",A,_o]}],leading:[{leading:[a,...I()]}],"list-image":[{"list-image":["none",A,N]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",A,N]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:R()}],"text-color":[{text:R()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ae(),"wavy"]}],"text-decoration-thickness":[{decoration:[K,"from-font","auto",A,Ft]}],"text-decoration-color":[{decoration:R()}],"underline-offset":[{"underline-offset":[K,"auto",A,N]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",A,N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",A,N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:V()}],"bg-repeat":[{bg:de()}],"bg-size":[{bg:ce()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},xt,A,N],radial:["",A,N],conic:[xt,A,N]},mm,um]}],"bg-color":[{bg:R()}],"gradient-from-pos":[{from:z()}],"gradient-via-pos":[{via:z()}],"gradient-to-pos":[{to:z()}],"gradient-from":[{from:R()}],"gradient-via":[{via:R()}],"gradient-to":[{to:R()}],rounded:[{rounded:T()}],"rounded-s":[{"rounded-s":T()}],"rounded-e":[{"rounded-e":T()}],"rounded-t":[{"rounded-t":T()}],"rounded-r":[{"rounded-r":T()}],"rounded-b":[{"rounded-b":T()}],"rounded-l":[{"rounded-l":T()}],"rounded-ss":[{"rounded-ss":T()}],"rounded-se":[{"rounded-se":T()}],"rounded-ee":[{"rounded-ee":T()}],"rounded-es":[{"rounded-es":T()}],"rounded-tl":[{"rounded-tl":T()}],"rounded-tr":[{"rounded-tr":T()}],"rounded-br":[{"rounded-br":T()}],"rounded-bl":[{"rounded-bl":T()}],"border-w":[{border:Y()}],"border-w-x":[{"border-x":Y()}],"border-w-y":[{"border-y":Y()}],"border-w-s":[{"border-s":Y()}],"border-w-e":[{"border-e":Y()}],"border-w-t":[{"border-t":Y()}],"border-w-r":[{"border-r":Y()}],"border-w-b":[{"border-b":Y()}],"border-w-l":[{"border-l":Y()}],"divide-x":[{"divide-x":Y()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":Y()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ae(),"hidden","none"]}],"divide-style":[{divide:[...ae(),"hidden","none"]}],"border-color":[{border:R()}],"border-color-x":[{"border-x":R()}],"border-color-y":[{"border-y":R()}],"border-color-s":[{"border-s":R()}],"border-color-e":[{"border-e":R()}],"border-color-t":[{"border-t":R()}],"border-color-r":[{"border-r":R()}],"border-color-b":[{"border-b":R()}],"border-color-l":[{"border-l":R()}],"divide-color":[{divide:R()}],"outline-style":[{outline:[...ae(),"none","hidden"]}],"outline-offset":[{"outline-offset":[K,A,N]}],"outline-w":[{outline:["",K,kn,Ft]}],"outline-color":[{outline:R()}],shadow:[{shadow:["","none",u,Sr,kr]}],"shadow-color":[{shadow:R()}],"inset-shadow":[{"inset-shadow":["none",d,Sr,kr]}],"inset-shadow-color":[{"inset-shadow":R()}],"ring-w":[{ring:Y()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:R()}],"ring-offset-w":[{"ring-offset":[K,Ft]}],"ring-offset-color":[{"ring-offset":R()}],"inset-ring-w":[{"inset-ring":Y()}],"inset-ring-color":[{"inset-ring":R()}],"text-shadow":[{"text-shadow":["none",p,Sr,kr]}],"text-shadow-color":[{"text-shadow":R()}],opacity:[{opacity:[K,A,N]}],"mix-blend":[{"mix-blend":[..._e(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":_e()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[K]}],"mask-image-linear-from-pos":[{"mask-linear-from":D()}],"mask-image-linear-to-pos":[{"mask-linear-to":D()}],"mask-image-linear-from-color":[{"mask-linear-from":R()}],"mask-image-linear-to-color":[{"mask-linear-to":R()}],"mask-image-t-from-pos":[{"mask-t-from":D()}],"mask-image-t-to-pos":[{"mask-t-to":D()}],"mask-image-t-from-color":[{"mask-t-from":R()}],"mask-image-t-to-color":[{"mask-t-to":R()}],"mask-image-r-from-pos":[{"mask-r-from":D()}],"mask-image-r-to-pos":[{"mask-r-to":D()}],"mask-image-r-from-color":[{"mask-r-from":R()}],"mask-image-r-to-color":[{"mask-r-to":R()}],"mask-image-b-from-pos":[{"mask-b-from":D()}],"mask-image-b-to-pos":[{"mask-b-to":D()}],"mask-image-b-from-color":[{"mask-b-from":R()}],"mask-image-b-to-color":[{"mask-b-to":R()}],"mask-image-l-from-pos":[{"mask-l-from":D()}],"mask-image-l-to-pos":[{"mask-l-to":D()}],"mask-image-l-from-color":[{"mask-l-from":R()}],"mask-image-l-to-color":[{"mask-l-to":R()}],"mask-image-x-from-pos":[{"mask-x-from":D()}],"mask-image-x-to-pos":[{"mask-x-to":D()}],"mask-image-x-from-color":[{"mask-x-from":R()}],"mask-image-x-to-color":[{"mask-x-to":R()}],"mask-image-y-from-pos":[{"mask-y-from":D()}],"mask-image-y-to-pos":[{"mask-y-to":D()}],"mask-image-y-from-color":[{"mask-y-from":R()}],"mask-image-y-to-color":[{"mask-y-to":R()}],"mask-image-radial":[{"mask-radial":[A,N]}],"mask-image-radial-from-pos":[{"mask-radial-from":D()}],"mask-image-radial-to-pos":[{"mask-radial-to":D()}],"mask-image-radial-from-color":[{"mask-radial-from":R()}],"mask-image-radial-to-color":[{"mask-radial-to":R()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":M()}],"mask-image-conic-pos":[{"mask-conic":[K]}],"mask-image-conic-from-pos":[{"mask-conic-from":D()}],"mask-image-conic-to-pos":[{"mask-conic-to":D()}],"mask-image-conic-from-color":[{"mask-conic-from":R()}],"mask-image-conic-to-color":[{"mask-conic-to":R()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:V()}],"mask-repeat":[{mask:de()}],"mask-size":[{mask:ce()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",A,N]}],filter:[{filter:["","none",A,N]}],blur:[{blur:oe()}],brightness:[{brightness:[K,A,N]}],contrast:[{contrast:[K,A,N]}],"drop-shadow":[{"drop-shadow":["","none",m,Sr,kr]}],"drop-shadow-color":[{"drop-shadow":R()}],grayscale:[{grayscale:["",K,A,N]}],"hue-rotate":[{"hue-rotate":[K,A,N]}],invert:[{invert:["",K,A,N]}],saturate:[{saturate:[K,A,N]}],sepia:[{sepia:["",K,A,N]}],"backdrop-filter":[{"backdrop-filter":["","none",A,N]}],"backdrop-blur":[{"backdrop-blur":oe()}],"backdrop-brightness":[{"backdrop-brightness":[K,A,N]}],"backdrop-contrast":[{"backdrop-contrast":[K,A,N]}],"backdrop-grayscale":[{"backdrop-grayscale":["",K,A,N]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[K,A,N]}],"backdrop-invert":[{"backdrop-invert":["",K,A,N]}],"backdrop-opacity":[{"backdrop-opacity":[K,A,N]}],"backdrop-saturate":[{"backdrop-saturate":[K,A,N]}],"backdrop-sepia":[{"backdrop-sepia":["",K,A,N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":I()}],"border-spacing-x":[{"border-spacing-x":I()}],"border-spacing-y":[{"border-spacing-y":I()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",A,N]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[K,"initial",A,N]}],ease:[{ease:["linear","initial",b,A,N]}],delay:[{delay:[K,A,N]}],animate:[{animate:["none",y,A,N]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,A,N]}],"perspective-origin":[{"perspective-origin":O()}],rotate:[{rotate:ge()}],"rotate-x":[{"rotate-x":ge()}],"rotate-y":[{"rotate-y":ge()}],"rotate-z":[{"rotate-z":ge()}],scale:[{scale:he()}],"scale-x":[{"scale-x":he()}],"scale-y":[{"scale-y":he()}],"scale-z":[{"scale-z":he()}],"scale-3d":["scale-3d"],skew:[{skew:Ae()}],"skew-x":[{"skew-x":Ae()}],"skew-y":[{"skew-y":Ae()}],transform:[{transform:[A,N,"","none","gpu","cpu"]}],"transform-origin":[{origin:O()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Fe()}],"translate-x":[{"translate-x":Fe()}],"translate-y":[{"translate-y":Fe()}],"translate-z":[{"translate-z":Fe()}],"translate-none":["translate-none"],accent:[{accent:R()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:R()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",A,N]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",A,N]}],fill:[{fill:["none",...R()]}],"stroke-w":[{stroke:[K,kn,Ft,_o]}],stroke:[{stroke:["none",...R()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},vm=(e,{cacheSize:t,prefix:n,experimentalParseClassName:r,extend:o={},override:a={}})=>(An(e,"cacheSize",t),An(e,"prefix",n),An(e,"experimentalParseClassName",r),Cr(e.theme,a.theme),Cr(e.classGroups,a.classGroups),Cr(e.conflictingClassGroups,a.conflictingClassGroups),Cr(e.conflictingClassGroupModifiers,a.conflictingClassGroupModifiers),An(e,"orderSensitiveModifiers",a.orderSensitiveModifiers),Er(e.theme,o.theme),Er(e.classGroups,o.classGroups),Er(e.conflictingClassGroups,o.conflictingClassGroups),Er(e.conflictingClassGroupModifiers,o.conflictingClassGroupModifiers),Pc(e,o,"orderSensitiveModifiers"),e),An=(e,t,n)=>{n!==void 0&&(e[t]=n)},Cr=(e,t)=>{if(t)for(const n in t)An(e,n,t[n])},Er=(e,t)=>{if(t)for(const n in t)Pc(e,t,n)},Pc=(e,t,n)=>{const r=t[n];r!==void 0&&(e[n]=e[n]?e[n].concat(r):r)},wm=(e,...t)=>typeof e=="function"?ji(Di,e,...t):ji(()=>vm(Di(),e),...t),rr="stagewise-companion-anchor";function bm(e,t){return document.elementsFromPoint(e,t).find(n=>n.nodeName!=="STAGEWISE-COMPANION-ANCHOR"&&!n.closest(rr)&&!n.closest("svg")&&ym(n,e,t))||document.body}const ym=(e,t,n)=>{const r=e.getBoundingClientRect(),o=t>r.left&&t<r.left+r.width,a=n>r.top&&n<r.top+r.height;return o&&a};var qr=(e=>(e[e.ESC=0]="ESC",e[e.CTRL_ALT_C=1]="CTRL_ALT_C",e))(qr||{});const Jo={0:{keyComboDefault:"Esc",keyComboMac:"esc",isEventMatching:e=>e.code==="Escape"},1:{keyComboDefault:"Ctrl+Alt+C",keyComboMac:"⌘+⌥+C",isEventMatching:e=>e.code==="KeyC"&&(e.ctrlKey||e.metaKey)&&e.altKey}},_m=wm({extend:{classGroups:{"bg-image":["bg-gradient","bg-gradient-light-1","bg-gradient-light-2","bg-gradient-light-3"]}}});function Se(...e){return _m(Fh(e))}const xo=(e=16)=>Math.random().toString(36).substring(2,e+2);function Vt({children:e,alwaysFullHeight:t=!1}){return h("section",{className:Se("flex max-h-full min-h-48 flex-col items-stretch justify-start rounded-2xl border border-border/30 bg-zinc-50/80 p-4 shadow-md backdrop-blur-md",t&&"h-full"),children:e})}Vt.Header=function({title:e,description:t}){return h("header",{className:"mb-3 flex flex-col gap-1 text-zinc-950",children:[e&&h("h3",{className:"font-semibold text-lg ",children:e}),t&&h("p",{className:"font-medium text-zinc-600",children:t})]})};Vt.Content=function({children:e}){return h("div",{className:"-mx-4 flex flex-col gap-2 overflow-y-auto border-border/30 border-t px-4 pt-4 text-zinc-950",children:e})};Vt.Footer=function({children:e}){return h("footer",{className:"flex flex-row items-end justify-end gap-2 text-sm text-zinc-600",children:e})};const xm='/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){stagewise-companion-anchor *,stagewise-companion-anchor :before,stagewise-companion-anchor :after,stagewise-companion-anchor ::backdrop{--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial}}}@layer theme{stagewise-companion-anchor{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-100:oklch(93.6% .032 17.717);--color-red-200:oklch(88.5% .062 18.334);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-orange-50:oklch(98% .016 73.684);--color-orange-100:oklch(95.4% .038 75.164);--color-orange-200:oklch(90.1% .076 70.697);--color-orange-300:oklch(83.7% .128 66.29);--color-orange-500:oklch(70.5% .213 47.604);--color-orange-600:oklch(64.6% .222 41.116);--color-orange-700:oklch(55.3% .195 38.402);--color-orange-800:oklch(47% .157 37.304);--color-amber-50:oklch(98.7% .022 95.277);--color-amber-800:oklch(47.3% .137 46.201);--color-yellow-500:oklch(79.5% .184 86.047);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-teal-500:oklch(70.4% .14 182.503);--color-sky-600:oklch(58.8% .158 241.966);--color-sky-700:oklch(50% .134 242.749);--color-blue-50:oklch(97% .014 254.604);--color-blue-100:oklch(93.2% .032 255.585);--color-blue-200:oklch(88.2% .059 254.128);--color-blue-300:oklch(80.9% .105 251.813);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-blue-800:oklch(42.4% .199 265.638);--color-indigo-700:oklch(45.7% .24 277.023);--color-indigo-950:oklch(25.7% .09 281.288);--color-violet-700:oklch(49.1% .27 292.581);--color-purple-500:oklch(62.7% .265 303.9);--color-fuchsia-700:oklch(51.8% .253 323.949);--color-pink-500:oklch(65.6% .241 354.308);--color-rose-600:oklch(58.6% .253 17.585);--color-zinc-50:oklch(98.5% 0 0);--color-zinc-100:oklch(96.7% .001 286.375);--color-zinc-300:oklch(87.1% .006 286.286);--color-zinc-400:oklch(70.5% .015 286.067);--color-zinc-500:oklch(55.2% .016 285.938);--color-zinc-600:oklch(44.2% .017 285.786);--color-zinc-700:oklch(37% .013 285.805);--color-zinc-900:oklch(21% .006 285.885);--color-zinc-950:oklch(14.1% .005 285.823);--color-black:#000;--color-white:#fff;--spacing:.25rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height: 1.5 ;--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--radius-md:.375rem;--radius-lg:.5rem;--radius-2xl:1rem;--radius-3xl:1.5rem;--drop-shadow-xs:0 1px 1px #0000000d;--drop-shadow-md:0 3px 3px #0000001f;--drop-shadow-xl:0 9px 7px #0000001a;--ease-out:cubic-bezier(0,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--blur-md:12px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-background:var(--color-white);--color-foreground:var(--color-zinc-950);--color-border:var(--color-zinc-500)}}@layer base{stagewise-companion-anchor *,stagewise-companion-anchor :after,stagewise-companion-anchor :before,stagewise-companion-anchor ::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}stagewise-companion-anchor ::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}:where(stagewise-companion-anchor),stagewise-companion-anchor{-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}stagewise-companion-anchor hr{height:0;color:inherit;border-top-width:1px}stagewise-companion-anchor abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}stagewise-companion-anchor h1,stagewise-companion-anchor h2,stagewise-companion-anchor h3,stagewise-companion-anchor h4,stagewise-companion-anchor h5,stagewise-companion-anchor h6{font-size:inherit;font-weight:inherit}stagewise-companion-anchor a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}stagewise-companion-anchor b,stagewise-companion-anchor strong{font-weight:bolder}stagewise-companion-anchor code,stagewise-companion-anchor kbd,stagewise-companion-anchor samp,stagewise-companion-anchor pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}stagewise-companion-anchor small{font-size:80%}stagewise-companion-anchor sub,stagewise-companion-anchor sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}stagewise-companion-anchor sub{bottom:-.25em}stagewise-companion-anchor sup{top:-.5em}stagewise-companion-anchor table{text-indent:0;border-color:inherit;border-collapse:collapse}stagewise-companion-anchor :-moz-focusring{outline:auto}stagewise-companion-anchor progress{vertical-align:baseline}stagewise-companion-anchor summary{display:list-item}stagewise-companion-anchor ol,stagewise-companion-anchor ul,stagewise-companion-anchor menu{list-style:none}stagewise-companion-anchor img,stagewise-companion-anchor svg,stagewise-companion-anchor video,stagewise-companion-anchor canvas,stagewise-companion-anchor audio,stagewise-companion-anchor iframe,stagewise-companion-anchor embed,stagewise-companion-anchor object{vertical-align:middle;display:block}stagewise-companion-anchor img,stagewise-companion-anchor video{max-width:100%;height:auto}stagewise-companion-anchor button,stagewise-companion-anchor input,stagewise-companion-anchor select,stagewise-companion-anchor optgroup,stagewise-companion-anchor textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor ::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup{font-weight:bolder}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}stagewise-companion-anchor ::file-selector-button{margin-inline-end:4px}stagewise-companion-anchor ::-moz-placeholder{opacity:1}stagewise-companion-anchor ::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){stagewise-companion-anchor ::-moz-placeholder{color:currentColor}stagewise-companion-anchor ::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor ::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}stagewise-companion-anchor ::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}stagewise-companion-anchor textarea{resize:vertical}stagewise-companion-anchor ::-webkit-search-decoration{-webkit-appearance:none}stagewise-companion-anchor ::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}stagewise-companion-anchor ::-webkit-datetime-edit{display:inline-flex}stagewise-companion-anchor ::-webkit-datetime-edit-fields-wrapper{padding:0}stagewise-companion-anchor ::-webkit-datetime-edit{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-year-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-month-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-day-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-hour-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-minute-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-second-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-millisecond-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-meridiem-field{padding-block:0}stagewise-companion-anchor :-moz-ui-invalid{box-shadow:none}stagewise-companion-anchor button,stagewise-companion-anchor input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::-webkit-inner-spin-button{height:auto}stagewise-companion-anchor ::-webkit-outer-spin-button{height:auto}stagewise-companion-anchor [hidden]:where(:not([hidden=until-found])){display:none!important}stagewise-companion-anchor stagewise-companion-anchor *{min-width:0;min-height:0;position:relative}}@layer components{stagewise-companion-anchor .chat-loading-gradient{background:linear-gradient(#f8fafccc,#f8fafccc) padding-box padding-box,linear-gradient(45deg,#8b5cf6,#06b6d4,#8b5cf6) 0 0/400% 400% border-box;border:2px solid #0000;animation:2s infinite gradient-animation}stagewise-companion-anchor .chat-success-border{animation:2s ease-out blink-green-fade}stagewise-companion-anchor .chat-error-border{animation:1s ease-out blink-red-fade}@keyframes blink-green-fade{0%,50%{box-shadow:0 0 0 2px #22c55eb3}to{box-shadow:0 0 0 2px #22c55e00}}@keyframes blink-red-fade{0%,50%{box-shadow:0 0 0 2px #ef4444}to{box-shadow:0 0 0 2px #ef444400}}}@layer utilities{stagewise-companion-anchor .pointer-events-auto{pointer-events:auto!important}stagewise-companion-anchor .pointer-events-none{pointer-events:none!important}stagewise-companion-anchor .visible{visibility:visible!important}stagewise-companion-anchor .absolute{position:absolute!important}stagewise-companion-anchor .fixed{position:fixed!important}stagewise-companion-anchor .relative{position:relative!important}stagewise-companion-anchor .inset-0{inset:calc(var(--spacing)*0)!important}stagewise-companion-anchor .inset-4{inset:calc(var(--spacing)*4)!important}stagewise-companion-anchor .top-0{top:calc(var(--spacing)*0)!important}stagewise-companion-anchor .top-0\\.5{top:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .top-1\\/2{top:50%!important}stagewise-companion-anchor .top-\\[-20\\%\\]{top:-20%!important}stagewise-companion-anchor .top-\\[25\\%\\]{top:25%!important}stagewise-companion-anchor .right-0{right:calc(var(--spacing)*0)!important}stagewise-companion-anchor .right-1\\/2{right:50%!important}stagewise-companion-anchor .right-\\[100\\%\\]{right:100%!important}stagewise-companion-anchor .bottom-0{bottom:calc(var(--spacing)*0)!important}stagewise-companion-anchor .bottom-1\\/2{bottom:50%!important}stagewise-companion-anchor .bottom-3{bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-0{left:calc(var(--spacing)*0)!important}stagewise-companion-anchor .left-0\\.5{left:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .left-1\\/2{left:50%!important}stagewise-companion-anchor .left-3{left:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-\\[-10\\%\\]{left:-10%!important}stagewise-companion-anchor .left-\\[25\\%\\]{left:25%!important}stagewise-companion-anchor .left-\\[100\\%\\]{left:100%!important}stagewise-companion-anchor .z-20{z-index:20!important}stagewise-companion-anchor .z-50{z-index:50!important}stagewise-companion-anchor .container{width:100%!important}@media (min-width:40rem){stagewise-companion-anchor .container{max-width:40rem!important}}@media (min-width:48rem){stagewise-companion-anchor .container{max-width:48rem!important}}@media (min-width:64rem){stagewise-companion-anchor .container{max-width:64rem!important}}@media (min-width:80rem){stagewise-companion-anchor .container{max-width:80rem!important}}@media (min-width:96rem){stagewise-companion-anchor .container{max-width:96rem!important}}stagewise-companion-anchor .-mx-4{margin-inline:calc(var(--spacing)*-4)!important}stagewise-companion-anchor .my-2{margin-block:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mt-1{margin-top:calc(var(--spacing)*1)!important}stagewise-companion-anchor .mt-2{margin-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mb-2{margin-bottom:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mb-3{margin-bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .block{display:block!important}stagewise-companion-anchor .contents{display:contents!important}stagewise-companion-anchor .flex{display:flex!important}stagewise-companion-anchor .hidden{display:none!important}stagewise-companion-anchor .inline{display:inline!important}stagewise-companion-anchor .aspect-square{aspect-ratio:1!important}stagewise-companion-anchor .size-0{width:calc(var(--spacing)*0)!important;height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .size-1\\.5{width:calc(var(--spacing)*1.5)!important;height:calc(var(--spacing)*1.5)!important}stagewise-companion-anchor .size-2\\/3{width:66.6667%!important;height:66.6667%!important}stagewise-companion-anchor .size-3{width:calc(var(--spacing)*3)!important;height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .size-4{width:calc(var(--spacing)*4)!important;height:calc(var(--spacing)*4)!important}stagewise-companion-anchor .size-4\\.5{width:calc(var(--spacing)*4.5)!important;height:calc(var(--spacing)*4.5)!important}stagewise-companion-anchor .size-5{width:calc(var(--spacing)*5)!important;height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .size-6{width:calc(var(--spacing)*6)!important;height:calc(var(--spacing)*6)!important}stagewise-companion-anchor .size-8{width:calc(var(--spacing)*8)!important;height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .size-9{width:calc(var(--spacing)*9)!important;height:calc(var(--spacing)*9)!important}stagewise-companion-anchor .size-9\\/12{width:75%!important;height:75%!important}stagewise-companion-anchor .size-12{width:calc(var(--spacing)*12)!important;height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .size-\\[120\\%\\]{width:120%!important;height:120%!important}stagewise-companion-anchor .size-full{width:100%!important;height:100%!important}stagewise-companion-anchor .h-0{height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .h-3{height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .h-5{height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .h-8{height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .h-9\\.5{height:calc(var(--spacing)*9.5)!important}stagewise-companion-anchor .h-12{height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .h-16{height:calc(var(--spacing)*16)!important}stagewise-companion-anchor .h-24{height:calc(var(--spacing)*24)!important}stagewise-companion-anchor .h-\\[50\\%\\]{height:50%!important}stagewise-companion-anchor .h-\\[120\\%\\]{height:120%!important}stagewise-companion-anchor .h-\\[calc\\(100vh-32px\\)\\]{height:calc(100vh - 32px)!important}stagewise-companion-anchor .h-\\[calc-size\\(auto\\)\\]{height:calc-size(auto)!important}stagewise-companion-anchor .h-\\[calc-size\\(auto\\,size\\)\\]{height:calc-size(auto,size)!important}stagewise-companion-anchor .h-auto{height:auto!important}stagewise-companion-anchor .h-full{height:100%!important}stagewise-companion-anchor .h-screen{height:100vh!important}stagewise-companion-anchor .max-h-full{max-height:100%!important}stagewise-companion-anchor .min-h-0{min-height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .min-h-48{min-height:calc(var(--spacing)*48)!important}stagewise-companion-anchor .w-8{width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .w-9\\.5{width:calc(var(--spacing)*9.5)!important}stagewise-companion-anchor .w-96{width:calc(var(--spacing)*96)!important}stagewise-companion-anchor .w-\\[50\\%\\]{width:50%!important}stagewise-companion-anchor .w-auto{width:auto!important}stagewise-companion-anchor .w-fit{width:-moz-fit-content!important;width:fit-content!important}stagewise-companion-anchor .w-full{width:100%!important}stagewise-companion-anchor .w-max{width:-moz-max-content!important;width:max-content!important}stagewise-companion-anchor .w-screen{width:100vw!important}stagewise-companion-anchor .max-w-8{max-width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .max-w-90{max-width:calc(var(--spacing)*90)!important}stagewise-companion-anchor .max-w-\\[40vw\\]{max-width:40vw!important}stagewise-companion-anchor .max-w-full{max-width:100%!important}stagewise-companion-anchor .min-w-0{min-width:calc(var(--spacing)*0)!important}stagewise-companion-anchor .min-w-3{min-width:calc(var(--spacing)*3)!important}stagewise-companion-anchor .min-w-24{min-width:calc(var(--spacing)*24)!important}stagewise-companion-anchor .flex-1{flex:1!important}stagewise-companion-anchor .flex-shrink-0,stagewise-companion-anchor .shrink-0{flex-shrink:0!important}stagewise-companion-anchor .origin-bottom{transform-origin:bottom!important}stagewise-companion-anchor .origin-bottom-left{transform-origin:0 100%!important}stagewise-companion-anchor .origin-bottom-right{transform-origin:100% 100%!important}stagewise-companion-anchor .origin-center{transform-origin:50%!important}stagewise-companion-anchor .origin-top{transform-origin:top!important}stagewise-companion-anchor .origin-top-left{transform-origin:0 0!important}stagewise-companion-anchor .origin-top-right{transform-origin:100% 0!important}stagewise-companion-anchor .scale-25{--tw-scale-x:25%!important;--tw-scale-y:25%!important;--tw-scale-z:25%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .scale-50{--tw-scale-x:50%!important;--tw-scale-y:50%!important;--tw-scale-z:50%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .scale-100{--tw-scale-x:100%!important;--tw-scale-y:100%!important;--tw-scale-z:100%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)!important}stagewise-companion-anchor .animate-pulse{animation:var(--animate-pulse)!important}stagewise-companion-anchor .animate-spin{animation:var(--animate-spin)!important}stagewise-companion-anchor .cursor-copy{cursor:copy!important}stagewise-companion-anchor .cursor-not-allowed{cursor:not-allowed!important}stagewise-companion-anchor .cursor-pointer{cursor:pointer!important}stagewise-companion-anchor .resize{resize:both!important}stagewise-companion-anchor .resize-none{resize:none!important}stagewise-companion-anchor .snap-start{scroll-snap-align:start!important}stagewise-companion-anchor .list-inside{list-style-position:inside!important}stagewise-companion-anchor .list-decimal{list-style-type:decimal!important}stagewise-companion-anchor .flex-col{flex-direction:column!important}stagewise-companion-anchor .flex-col-reverse{flex-direction:column-reverse!important}stagewise-companion-anchor .flex-row{flex-direction:row!important}stagewise-companion-anchor .flex-wrap{flex-wrap:wrap!important}stagewise-companion-anchor .items-center{align-items:center!important}stagewise-companion-anchor .items-end{align-items:flex-end!important}stagewise-companion-anchor .items-start{align-items:flex-start!important}stagewise-companion-anchor .items-stretch{align-items:stretch!important}stagewise-companion-anchor .justify-between{justify-content:space-between!important}stagewise-companion-anchor .justify-center{justify-content:center!important}stagewise-companion-anchor .justify-end{justify-content:flex-end!important}stagewise-companion-anchor .justify-start{justify-content:flex-start!important}stagewise-companion-anchor .gap-0\\.5{gap:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .gap-1{gap:calc(var(--spacing)*1)!important}stagewise-companion-anchor .gap-2{gap:calc(var(--spacing)*2)!important}stagewise-companion-anchor .gap-3{gap:calc(var(--spacing)*3)!important}stagewise-companion-anchor :where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0!important;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse))!important;margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))!important}stagewise-companion-anchor :where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0!important;border-bottom-style:var(--tw-border-style)!important;border-top-style:var(--tw-border-style)!important;border-top-width:calc(1px*var(--tw-divide-y-reverse))!important;border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))!important}stagewise-companion-anchor :where(.divide-y-reverse>:not(:last-child)){--tw-divide-y-reverse:1!important}stagewise-companion-anchor :where(.divide-blue-200>:not(:last-child)){border-color:var(--color-blue-200)!important}stagewise-companion-anchor :where(.divide-border\\/20>:not(:last-child)){border-color:#71717b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor :where(.divide-border\\/20>:not(:last-child)){border-color:color-mix(in oklab,var(--color-border)20%,transparent)!important}}stagewise-companion-anchor :where(.divide-orange-200>:not(:last-child)){border-color:var(--color-orange-200)!important}stagewise-companion-anchor .truncate{text-overflow:ellipsis!important;white-space:nowrap!important;overflow:hidden!important}stagewise-companion-anchor .overflow-hidden{overflow:hidden!important}stagewise-companion-anchor .overflow-visible{overflow:visible!important}stagewise-companion-anchor .overflow-y-auto{overflow-y:auto!important}stagewise-companion-anchor .rounded{border-radius:.25rem!important}stagewise-companion-anchor .rounded-2xl{border-radius:var(--radius-2xl)!important}stagewise-companion-anchor .rounded-full{border-radius:3.40282e38px!important}stagewise-companion-anchor .rounded-lg{border-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-md{border-radius:var(--radius-md)!important}stagewise-companion-anchor .rounded-t-3xl{border-top-left-radius:var(--radius-3xl)!important;border-top-right-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-t-lg{border-top-left-radius:var(--radius-lg)!important;border-top-right-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-b-3xl{border-bottom-right-radius:var(--radius-3xl)!important;border-bottom-left-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-b-lg{border-bottom-right-radius:var(--radius-lg)!important;border-bottom-left-radius:var(--radius-lg)!important}stagewise-companion-anchor .border{border-style:var(--tw-border-style)!important;border-width:1px!important}stagewise-companion-anchor .border-2{border-style:var(--tw-border-style)!important;border-width:2px!important}stagewise-companion-anchor .border-t{border-top-style:var(--tw-border-style)!important;border-top-width:1px!important}stagewise-companion-anchor .border-solid{--tw-border-style:solid!important;border-style:solid!important}stagewise-companion-anchor .border-blue-200{border-color:var(--color-blue-200)!important}stagewise-companion-anchor .border-blue-300{border-color:var(--color-blue-300)!important}stagewise-companion-anchor .border-blue-500{border-color:var(--color-blue-500)!important}stagewise-companion-anchor .border-blue-600\\/80{border-color:#155dfccc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-blue-600\\/80{border-color:color-mix(in oklab,var(--color-blue-600)80%,transparent)!important}}stagewise-companion-anchor .border-border\\/30{border-color:#71717b4d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-border\\/30{border-color:color-mix(in oklab,var(--color-border)30%,transparent)!important}}stagewise-companion-anchor .border-green-500{border-color:var(--color-green-500)!important}stagewise-companion-anchor .border-green-600\\/80{border-color:#00a544cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-green-600\\/80{border-color:color-mix(in oklab,var(--color-green-600)80%,transparent)!important}}stagewise-companion-anchor .border-orange-200{border-color:var(--color-orange-200)!important}stagewise-companion-anchor .border-orange-300{border-color:var(--color-orange-300)!important}stagewise-companion-anchor .border-orange-500{border-color:var(--color-orange-500)!important}stagewise-companion-anchor .border-pink-500{border-color:var(--color-pink-500)!important}stagewise-companion-anchor .border-purple-500{border-color:var(--color-purple-500)!important}stagewise-companion-anchor .border-red-200{border-color:var(--color-red-200)!important}stagewise-companion-anchor .border-red-500{border-color:var(--color-red-500)!important}stagewise-companion-anchor .border-transparent{border-color:#0000!important}stagewise-companion-anchor .border-yellow-500{border-color:var(--color-yellow-500)!important}stagewise-companion-anchor .border-zinc-300{border-color:var(--color-zinc-300)!important}stagewise-companion-anchor .border-zinc-500{border-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-amber-50{background-color:var(--color-amber-50)!important}stagewise-companion-anchor .bg-background\\/60{background-color:#fff9!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-background\\/60{background-color:color-mix(in oklab,var(--color-background)60%,transparent)!important}}stagewise-companion-anchor .bg-blue-50{background-color:var(--color-blue-50)!important}stagewise-companion-anchor .bg-blue-50\\/90{background-color:#eff6ffe6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-50\\/90{background-color:color-mix(in oklab,var(--color-blue-50)90%,transparent)!important}}stagewise-companion-anchor .bg-blue-100\\/80{background-color:#dbeafecc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-100\\/80{background-color:color-mix(in oklab,var(--color-blue-100)80%,transparent)!important}}stagewise-companion-anchor .bg-blue-500{background-color:var(--color-blue-500)!important}stagewise-companion-anchor .bg-blue-600{background-color:var(--color-blue-600)!important}stagewise-companion-anchor .bg-blue-600\\/20{background-color:#155dfc33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-600\\/20{background-color:color-mix(in oklab,var(--color-blue-600)20%,transparent)!important}}stagewise-companion-anchor .bg-green-500{background-color:var(--color-green-500)!important}stagewise-companion-anchor .bg-green-600\\/5{background-color:#00a5440d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-green-600\\/5{background-color:color-mix(in oklab,var(--color-green-600)5%,transparent)!important}}stagewise-companion-anchor .bg-orange-50\\/90{background-color:#fff7ede6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-orange-50\\/90{background-color:color-mix(in oklab,var(--color-orange-50)90%,transparent)!important}}stagewise-companion-anchor .bg-orange-100\\/80{background-color:#ffedd5cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-orange-100\\/80{background-color:color-mix(in oklab,var(--color-orange-100)80%,transparent)!important}}stagewise-companion-anchor .bg-orange-500{background-color:var(--color-orange-500)!important}stagewise-companion-anchor .bg-orange-600{background-color:var(--color-orange-600)!important}stagewise-companion-anchor .bg-pink-500{background-color:var(--color-pink-500)!important}stagewise-companion-anchor .bg-purple-500{background-color:var(--color-purple-500)!important}stagewise-companion-anchor .bg-red-100{background-color:var(--color-red-100)!important}stagewise-companion-anchor .bg-red-500{background-color:var(--color-red-500)!important}stagewise-companion-anchor .bg-rose-600{background-color:var(--color-rose-600)!important}stagewise-companion-anchor .bg-transparent{background-color:#0000!important}stagewise-companion-anchor .bg-white{background-color:var(--color-white)!important}stagewise-companion-anchor .bg-white\\/40{background-color:#fff6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/40{background-color:color-mix(in oklab,var(--color-white)40%,transparent)!important}}stagewise-companion-anchor .bg-white\\/80{background-color:#fffc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/80{background-color:color-mix(in oklab,var(--color-white)80%,transparent)!important}}stagewise-companion-anchor .bg-white\\/90{background-color:#ffffffe6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/90{background-color:color-mix(in oklab,var(--color-white)90%,transparent)!important}}stagewise-companion-anchor .bg-yellow-500{background-color:var(--color-yellow-500)!important}stagewise-companion-anchor .bg-zinc-50\\/80{background-color:#fafafacc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-50\\/80{background-color:color-mix(in oklab,var(--color-zinc-50)80%,transparent)!important}}stagewise-companion-anchor .bg-zinc-300{background-color:var(--color-zinc-300)!important}stagewise-companion-anchor .bg-zinc-500{background-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-zinc-500\\/10{background-color:#71717b1a!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-500\\/10{background-color:color-mix(in oklab,var(--color-zinc-500)10%,transparent)!important}}stagewise-companion-anchor .bg-zinc-500\\/40{background-color:#71717b66!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-500\\/40{background-color:color-mix(in oklab,var(--color-zinc-500)40%,transparent)!important}}stagewise-companion-anchor .bg-zinc-700\\/80{background-color:#3f3f46cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-700\\/80{background-color:color-mix(in oklab,var(--color-zinc-700)80%,transparent)!important}}stagewise-companion-anchor .bg-gradient-to-tr{--tw-gradient-position:to top right in oklab!important;background-image:linear-gradient(var(--tw-gradient-stops))!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(55\\,48\\,163\\,0\\)_55\\%\\,rgba\\(55\\,48\\,163\\,0\\.35\\)_73\\%\\)\\]{background-image:radial-gradient(circle,#3730a300 55%,#3730a359 73%)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(219\\,39\\,119\\,0\\.2\\)_0\\%\\,rgba\\(219\\,39\\,119\\,0\\)_100\\%\\)\\]{background-image:radial-gradient(circle,#db277733,#db277700)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(255\\,255\\,255\\,0\\)_60\\%\\,rgba\\(255\\,255\\,255\\,0\\.2\\)_70\\%\\)\\]{background-image:radial-gradient(circle,#fff0 60%,#fff3 70%)!important}stagewise-companion-anchor .from-blue-600{--tw-gradient-from:var(--color-blue-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-indigo-700{--tw-gradient-from:var(--color-indigo-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-orange-600{--tw-gradient-from:var(--color-orange-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-sky-700{--tw-gradient-from:var(--color-sky-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .via-blue-500{--tw-gradient-via:var(--color-blue-500)!important;--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position)!important;--tw-gradient-stops:var(--tw-gradient-via-stops)!important}stagewise-companion-anchor .to-fuchsia-700{--tw-gradient-to:var(--color-fuchsia-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-red-600{--tw-gradient-to:var(--color-red-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-sky-600{--tw-gradient-to:var(--color-sky-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-teal-500{--tw-gradient-to:var(--color-teal-500)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .fill-current{fill:currentColor!important}stagewise-companion-anchor .fill-white{fill:var(--color-white)!important}stagewise-companion-anchor .fill-zinc-500\\/50{fill:#71717b80!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .fill-zinc-500\\/50{fill:color-mix(in oklab,var(--color-zinc-500)50%,transparent)!important}}stagewise-companion-anchor .fill-zinc-950{fill:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-black\\/30{stroke:#0000004d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .stroke-black\\/30{stroke:color-mix(in oklab,var(--color-black)30%,transparent)!important}}stagewise-companion-anchor .stroke-none{stroke:none!important}stagewise-companion-anchor .stroke-white{stroke:var(--color-white)!important}stagewise-companion-anchor .stroke-zinc-950{stroke:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-1{stroke-width:1px!important}stagewise-companion-anchor .p-0\\.5{padding:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .p-1{padding:calc(var(--spacing)*1)!important}stagewise-companion-anchor .p-2{padding:calc(var(--spacing)*2)!important}stagewise-companion-anchor .p-3{padding:calc(var(--spacing)*3)!important}stagewise-companion-anchor .p-4{padding:calc(var(--spacing)*4)!important}stagewise-companion-anchor .px-0\\.5{padding-inline:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .px-1{padding-inline:calc(var(--spacing)*1)!important}stagewise-companion-anchor .px-2{padding-inline:calc(var(--spacing)*2)!important}stagewise-companion-anchor .px-3{padding-inline:calc(var(--spacing)*3)!important}stagewise-companion-anchor .px-4{padding-inline:calc(var(--spacing)*4)!important}stagewise-companion-anchor .py-0{padding-block:calc(var(--spacing)*0)!important}stagewise-companion-anchor .py-0\\.5{padding-block:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .py-2{padding-block:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pt-2{padding-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pt-4{padding-top:calc(var(--spacing)*4)!important}stagewise-companion-anchor .pr-6{padding-right:calc(var(--spacing)*6)!important}stagewise-companion-anchor .pb-4{padding-bottom:calc(var(--spacing)*4)!important}stagewise-companion-anchor .pl-2{padding-left:calc(var(--spacing)*2)!important}stagewise-companion-anchor .text-base{font-size:var(--text-base)!important;line-height:var(--tw-leading,var(--text-base--line-height))!important}stagewise-companion-anchor .text-lg{font-size:var(--text-lg)!important;line-height:var(--tw-leading,var(--text-lg--line-height))!important}stagewise-companion-anchor .text-sm{font-size:var(--text-sm)!important;line-height:var(--tw-leading,var(--text-sm--line-height))!important}stagewise-companion-anchor .text-xs{font-size:var(--text-xs)!important;line-height:var(--tw-leading,var(--text-xs--line-height))!important}stagewise-companion-anchor .text-\\[0\\.5em\\]{font-size:.5em!important}stagewise-companion-anchor .font-bold{--tw-font-weight:var(--font-weight-bold)!important;font-weight:var(--font-weight-bold)!important}stagewise-companion-anchor .font-medium{--tw-font-weight:var(--font-weight-medium)!important;font-weight:var(--font-weight-medium)!important}stagewise-companion-anchor .font-normal{--tw-font-weight:var(--font-weight-normal)!important;font-weight:var(--font-weight-normal)!important}stagewise-companion-anchor .font-semibold{--tw-font-weight:var(--font-weight-semibold)!important;font-weight:var(--font-weight-semibold)!important}stagewise-companion-anchor .text-amber-800{color:var(--color-amber-800)!important}stagewise-companion-anchor .text-blue-500{color:var(--color-blue-500)!important}stagewise-companion-anchor .text-blue-600{color:var(--color-blue-600)!important}stagewise-companion-anchor .text-blue-700{color:var(--color-blue-700)!important}stagewise-companion-anchor .text-blue-800{color:var(--color-blue-800)!important}stagewise-companion-anchor .text-foreground{color:var(--color-foreground)!important}stagewise-companion-anchor .text-indigo-700{color:var(--color-indigo-700)!important}stagewise-companion-anchor .text-orange-600{color:var(--color-orange-600)!important}stagewise-companion-anchor .text-orange-700{color:var(--color-orange-700)!important}stagewise-companion-anchor .text-orange-800{color:var(--color-orange-800)!important}stagewise-companion-anchor .text-red-600{color:var(--color-red-600)!important}stagewise-companion-anchor .text-red-700{color:var(--color-red-700)!important}stagewise-companion-anchor .text-transparent{color:#0000!important}stagewise-companion-anchor .text-violet-700{color:var(--color-violet-700)!important}stagewise-companion-anchor .text-white{color:var(--color-white)!important}stagewise-companion-anchor .text-zinc-500{color:var(--color-zinc-500)!important}stagewise-companion-anchor .text-zinc-600{color:var(--color-zinc-600)!important}stagewise-companion-anchor .text-zinc-700{color:var(--color-zinc-700)!important}stagewise-companion-anchor .text-zinc-950{color:var(--color-zinc-950)!important}stagewise-companion-anchor .opacity-0{opacity:0!important}stagewise-companion-anchor .opacity-20{opacity:.2!important}stagewise-companion-anchor .opacity-30{opacity:.3!important}stagewise-companion-anchor .opacity-80{opacity:.8!important}stagewise-companion-anchor .opacity-100{opacity:1!important}stagewise-companion-anchor .shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .ring{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:#00000080!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-black)50%,transparent)var(--tw-shadow-alpha),transparent)!important}}stagewise-companion-anchor .ring-transparent{--tw-ring-color:transparent!important}stagewise-companion-anchor .ring-zinc-950\\/20{--tw-ring-color:#09090b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .ring-zinc-950\\/20{--tw-ring-color:color-mix(in oklab,var(--color-zinc-950)20%,transparent)!important}}stagewise-companion-anchor .outline{outline-style:var(--tw-outline-style)!important;outline-width:1px!important}stagewise-companion-anchor .blur{--tw-blur:blur(8px)!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .blur-md{--tw-blur:blur(var(--blur-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .blur-none{--tw-blur: !important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-md{--tw-drop-shadow-size:drop-shadow(0 3px 3px var(--tw-drop-shadow-color,#0000001f))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xl{--tw-drop-shadow-size:drop-shadow(0 9px 7px var(--tw-drop-shadow-color,#0000001a))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xl))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xs{--tw-drop-shadow-size:drop-shadow(0 1px 1px var(--tw-drop-shadow-color,#0000000d))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xs))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:#000!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:color-mix(in oklab,var(--color-black)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:oklch(25.7% .09 281.288)!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:color-mix(in oklab,var(--color-indigo-950)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .backdrop-blur{--tw-backdrop-blur:blur(8px)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-blur-md{--tw-backdrop-blur:blur(var(--blur-md))!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-saturate-150{--tw-backdrop-saturate:saturate(150%)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-all{transition-property:all!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .duration-0{--tw-duration:0s!important;transition-duration:0s!important}stagewise-companion-anchor .duration-100{--tw-duration:.1s!important;transition-duration:.1s!important}stagewise-companion-anchor .duration-150{--tw-duration:.15s!important;transition-duration:.15s!important}stagewise-companion-anchor .duration-300{--tw-duration:.3s!important;transition-duration:.3s!important}stagewise-companion-anchor .duration-500{--tw-duration:.5s!important;transition-duration:.5s!important}stagewise-companion-anchor .ease-out{--tw-ease:var(--ease-out)!important;transition-timing-function:var(--ease-out)!important}stagewise-companion-anchor .outline-none{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .select-none{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}stagewise-companion-anchor :is(.\\*\\:size-full>*){width:100%!important;height:100%!important}stagewise-companion-anchor .placeholder\\:text-zinc-400::-moz-placeholder{color:var(--color-zinc-400)!important}stagewise-companion-anchor .placeholder\\:text-zinc-400::placeholder{color:var(--color-zinc-400)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:#09090b80!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:#09090b80!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:#09090bb3!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:#09090bb3!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}}@media (hover:hover){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:#e40014cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:color-mix(in oklab,var(--color-red-600)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-blue-200\\/80:hover{background-color:#bedbffcc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-blue-200\\/80:hover{background-color:color-mix(in oklab,var(--color-blue-200)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-orange-200:hover{background-color:var(--color-orange-200)!important}stagewise-companion-anchor .hover\\:bg-orange-700:hover{background-color:var(--color-orange-700)!important}stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:#e4001433!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:color-mix(in oklab,var(--color-red-600)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-500\\/20:hover{background-color:#71717b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-500\\/20:hover{background-color:color-mix(in oklab,var(--color-zinc-500)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:#09090b0d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:color-mix(in oklab,var(--color-zinc-950)5%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:#09090b1a!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:color-mix(in oklab,var(--color-zinc-950)10%,transparent)!important}}stagewise-companion-anchor .hover\\:text-orange-800:hover{color:var(--color-orange-800)!important}stagewise-companion-anchor .hover\\:text-white:hover{color:var(--color-white)!important}stagewise-companion-anchor .hover\\:underline:hover{text-decoration-line:underline!important}stagewise-companion-anchor .hover\\:opacity-100:hover{opacity:1!important}stagewise-companion-anchor .hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}}stagewise-companion-anchor .focus\\:border-blue-500:focus{border-color:var(--color-blue-500)!important}stagewise-companion-anchor .focus\\:border-zinc-500:focus{border-color:var(--color-zinc-500)!important}stagewise-companion-anchor .focus\\:text-zinc-900:focus{color:var(--color-zinc-900)!important}stagewise-companion-anchor .focus\\:outline-none:focus{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .disabled\\:opacity-50:disabled{opacity:.5!important}stagewise-companion-anchor .data-focus\\:outline-none[data-focus]{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .animate-shake{animation:.5s ease-in-out 2 shake}}@keyframes shake{0%,to{transform:translate(0)}10%,30%,50%,70%,90%{transform:translate(-2px)}20%,40%,60%,80%{transform:translate(2px)}}@keyframes gradient-animation{0%{background-position:0%}50%{background-position:100%}to{background-position:0%}}stagewise-companion-anchor stagewise-companion-anchor{all:initial;interpolate-size:allow-keywords;transform:translate(0);color:var(--color-zinc-950)!important;letter-spacing:normal!important;text-rendering:auto!important;font-family:Inter,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important;font-weight:400!important;line-height:normal!important}@supports (font-variation-settings:normal){stagewise-companion-anchor stagewise-companion-anchor{font-optical-sizing:auto!important;font-family:InterVariable,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important}}stagewise-companion-anchor #headlessui-portal-root{z-index:50!important;width:100vw!important;height:100vh!important;position:fixed!important}stagewise-companion-anchor #headlessui-portal-root>*{pointer-events:auto!important}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}@keyframes pulse{50%{opacity:.5}}';function km(e){const t={},n=["id","class","name","type","href","src","alt","for","placeholder"],r=[];for(let o=0;o<e.attributes.length;o++){const a=e.attributes[o];a.name.startsWith("data-")?r.push({name:a.name,value:a.value}):(n.includes(a.name.toLowerCase())||a.name.toLowerCase()!=="style")&&(t[a.name]=a.value)}return r.forEach(o=>{t[o.name]=o.value}),t}function Sm(e,t){var n;let r=`<element index="${t+1}">
`;r+=`  <tag>${e.tagName.toLowerCase()}</tag>
`;const o=e.id;o&&(r+=`  <id>${o}</id>
`);const a=Array.from(e.classList).join(", ");a&&(r+=`  <classes>${a}</classes>
`);const i=km(e);if(Object.keys(i).length>0){r+=`  <attributes>
`;for(const[c,l]of Object.entries(i))(c.toLowerCase()!=="class"||!a)&&(r+=`    <${c}>${l}</${c}>
`);r+=`  </attributes>
`}const s=(n=e.innerText)==null?void 0:n.trim();if(s&&(r+=`  <text>${s.length>100?`${s.substring(0,100)}...`:s}</text>
`),r+=`  <structural_context>
`,e.parentElement){const c=e.parentElement;r+=`    <parent>
`,r+=`      <tag>${c.tagName.toLowerCase()}</tag>
`,c.id&&(r+=`      <id>${c.id}</id>
`);const l=Array.from(c.classList).join(", ");l&&(r+=`      <classes>${l}</classes>
`),r+=`    </parent>
`}else r+=`    <parent>No parent element found (likely root or disconnected)</parent>
`;r+=`  </structural_context>
`;try{const c=window.getComputedStyle(e),l={color:c.color,backgroundColor:c.backgroundColor,fontSize:c.fontSize,fontWeight:c.fontWeight,display:c.display};r+=`  <styles>
`;for(const[u,d]of Object.entries(l))r+=`    <${u}>${d}</${u}>
`;r+=`  </styles>
`}catch{r+=`  <styles>Could not retrieve computed styles</styles>
`}return r+=`</element>
`,r}function Cm(e,t,n,r){const o=r.map(i=>`
      <plugin_contexts>
<${i.pluginName}>
${i.contextSnippets.map(s=>`    <${s.promptContextName}>${s.content}</${s.promptContextName}>`).join(`
`)}
</${i.pluginName}>
</plugin_contexts>
`.trim()).join(`
`);if(!e||e.length===0)return`
    <request>
      <user_goal>${t}</user_goal>
      <url>${n}</url>
  <context>No specific element was selected on the page. Please analyze the page code in general or ask for clarification.</context>
  ${o}
</request>`.trim();let a="";return e.forEach((i,s)=>{a+=Sm(i,s)}),`
<request>
  <user_goal>${t}</user_goal>
  <url>${n}</url>
  <selected_elements>
    ${a.trim()}
  </selected_elements>
  ${o}
</request>`.trim()}const Tc=Ge(null),Rc="stgws:companion";function Em(){try{const e=sessionStorage.getItem(Rc);return e?JSON.parse(e):{}}catch(e){return console.error("Failed to load state from storage:",e),{}}}function Nm(e){try{sessionStorage.setItem(Rc,JSON.stringify(e))}catch(t){console.error("Failed to save state to storage:",t)}}function Am({children:e}){const[t,n]=ie(()=>{const p=Em();return{appBlockRequestList:[],appUnblockRequestList:[],lastBlockRequestNumber:0,lastUnblockRequestNumber:0,isMainAppBlocked:!1,toolbarBoxRef:Do(),minimized:p.minimized??!1,requestMainAppBlock:()=>0,requestMainAppUnblock:()=>0,discardMainAppBlock:()=>{},discardMainAppUnblock:()=>{},setToolbarBoxRef:()=>{},unsetToolbarBoxRef:()=>{},minimize:()=>{},expand:()=>{}}});fe(()=>{Nm({minimized:t.minimized})},[t.minimized]);const r=P(()=>{let p=0;return n(m=>(p=m.lastBlockRequestNumber+1,{...m,appBlockRequestList:[...m.appBlockRequestList,p],lastBlockRequestNumber:p,isMainAppBlocked:m.appUnblockRequestList.length===0})),p},[]),o=P(()=>{let p=0;return n(m=>(p=m.lastUnblockRequestNumber+1,{...m,appUnblockRequestList:[...m.appUnblockRequestList,p],lastUnblockRequestNumber:p,isMainAppBlocked:!1})),p},[]),a=P(p=>{n(m=>{const v=m.appBlockRequestList.filter(g=>g!==p);return{...m,appBlockRequestList:v,isMainAppBlocked:v.length>0&&m.appUnblockRequestList.length===0}})},[]),i=P(p=>{n(m=>{const v=m.appUnblockRequestList.filter(g=>g!==p);return{...m,appUnblockRequestList:v,isMainAppBlocked:m.appBlockRequestList.length>0&&v.length===0}})},[]),s=P(p=>{n(m=>({...m,toolbarBoxRef:p}))},[]),c=P(()=>{n(p=>({...p,toolbarBoxRef:Do()}))},[]),l=P(()=>{n(p=>({...p,minimized:!0}))},[]),u=P(()=>{n(p=>({...p,minimized:!1}))},[]),d={requestMainAppBlock:r,requestMainAppUnblock:o,discardMainAppBlock:a,discardMainAppUnblock:i,isMainAppBlocked:t.isMainAppBlocked,toolbarBoxRef:t.toolbarBoxRef,setToolbarBoxRef:s,unsetToolbarBoxRef:c,minimized:t.minimized,minimize:l,expand:u};return h(Tc.Provider,{value:d,children:e})}function Ma(){const e=Pe(Tc);if(!e)throw new Error("useAppState must be used within an AppStateProvider");return e}const zc=Ge({chats:[],currentChatId:null,createChat:()=>"",deleteChat:()=>{},setCurrentChat:()=>{},setChatInput:()=>{},addChatDomContext:()=>{},removeChatDomContext:()=>{},addMessage:()=>{},chatAreaState:"hidden",setChatAreaState:()=>{},isPromptCreationActive:!1,startPromptCreation:()=>{},stopPromptCreation:()=>{},promptState:"idle",resetPromptState:()=>{}}),Mm=({children:e})=>{const[t,n]=ie([{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]),[r,o]=ie("new_chat"),[a,i]=ie("hidden"),[s,c]=ie(!1),[l,u]=ie("idle"),d=P(()=>{u("idle")},[]),{minimized:p}=Ma(),{selectedSession:m,setShouldPromptWindowSelection:v,windows:g}=Tt();fe(()=>{p&&(c(!1),i("hidden"))},[p]);const{bridge:w}=wc(),b=P(()=>{const B=xo(),U={id:B,title:null,messages:[],inputValue:"",domContextElements:[]};return n(pe=>[...pe,U]),o(B),B},[]),y=P(B=>{n(U=>{const pe=U.filter(ne=>ne.id!==B);return pe.length===0?[{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]:pe}),r===B&&n(U=>(o(U[0].id),U))},[r]),S=P(B=>{o(B)},[]),M=P((B,U)=>{n(pe=>pe.map(ne=>ne.id===B?{...ne,inputValue:U}:ne))},[]),{plugins:O}=io(),re=P(()=>{c(!0),a==="hidden"&&i("compact"),O.forEach(B=>{var U;(U=B.onPromptingStart)==null||U.call(B)})},[a]),Z=P(()=>{c(!1),u("idle"),n(B=>B.map(U=>U.id===r?{...U,domContextElements:[]}:U)),a==="compact"&&i("hidden"),O.forEach(B=>{var U;(U=B.onPromptingAbort)==null||U.call(B)})},[r,a]),I=P(B=>{i(B),B==="hidden"&&Z()},[i,Z]),Q=P((B,U)=>{const pe=O.filter(ne=>ne.onContextElementSelect);n(ne=>ne.map(Ee=>Ee.id===B?{...Ee,domContextElements:[...Ee.domContextElements,{element:U,pluginContext:pe.map(R=>{var V;return{pluginName:R.pluginName,context:(V=R.onContextElementSelect)==null?void 0:V.call(R,U)}})}]}:Ee))},[O]),Ce=P((B,U)=>{n(pe=>pe.map(ne=>ne.id===B?{...ne,domContextElements:ne.domContextElements.filter(Ee=>Ee.element!==U)}:ne))},[]),te=P(async(B,U,pe=!1)=>{if(!U.trim()||l==="loading")return;const ne=t.find(z=>z.id===B);u("loading");const Ee=[],R=O.map(async z=>{var T;const Y={id:xo(),text:U,contextElements:(ne==null?void 0:ne.domContextElements.map(oe=>oe.element))||[],sentByPlugin:pe},ae=await((T=z.onPromptSend)==null?void 0:T.call(z,Y));if(!ae||!ae.contextSnippets||ae.contextSnippets.length===0)return null;const _e=ae.contextSnippets.map(async oe=>{const ge=typeof oe.content=="string"?oe.content:await oe.content();return{promptContextName:oe.promptContextName,content:ge}}),D=await Promise.all(_e);return D.length>0?{pluginName:z.pluginName,contextSnippets:D}:null});(await Promise.all(R)).forEach(z=>{z&&Ee.push(z)});const V=Cm(ne==null?void 0:ne.domContextElements.map(z=>z.element),U,window.location.href,Ee),de={id:xo(),content:U.trim(),sender:"user",type:"regular",timestamp:new Date};async function ce(){if(w)try{const z=await w.call.triggerAgentPrompt({prompt:V,sessionId:m==null?void 0:m.sessionId},{onUpdate:T=>{}});z.result.success?(setTimeout(()=>{u("success")},1e3),n(T=>T.map(Y=>Y.id===B?{...Y,inputValue:""}:Y))):(z.result.errorCode&&z.result.errorCode==="session_mismatch"&&v(!0),u("error"),setTimeout(()=>{u("idle"),c(!1),n(T=>T.map(Y=>Y.id===B?{...Y,inputValue:""}:Y))},300))}catch{u("error"),setTimeout(()=>{u("idle"),c(!1),n(z=>z.map(T=>T.id===B?{...T,inputValue:""}:T))},300)}else v(!0),u("error"),setTimeout(()=>{u("idle"),c(!1),n(z=>z.map(T=>T.id===B?{...T,inputValue:""}:T))},300)}ce(),a==="hidden"&&i("compact"),n(z=>z.map(T=>T.id===B?{...T,messages:[...T.messages,de],inputValue:U.trim(),domContextElements:[]}:T))},[a,w,t,c,i,m,l,u,O]),ye={chats:t,currentChatId:r,createChat:b,deleteChat:y,setCurrentChat:S,setChatInput:M,addMessage:te,chatAreaState:a,setChatAreaState:I,isPromptCreationActive:s,startPromptCreation:re,stopPromptCreation:Z,addChatDomContext:Q,removeChatDomContext:Ce,promptState:l,resetPromptState:d};return h(zc.Provider,{value:ye,children:e})};function hr(){const e=Pe(zc);if(!e)throw new Error("useChatState must be used within a ChatStateProvider");return e}function Im({children:e,config:t}){return h(Oh,{config:t,children:h(Rh,{children:h(zh,{children:h(Lh,{children:h(Mm,{children:e})})})})})}function Gr(e,t,n,r=window){fe(()=>{if(!(typeof window>"u")&&r)return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)},[e,t,r,n])}function $m(){const{startPromptCreation:e,stopPromptCreation:t,isPromptCreationActive:n}=hr(),r=be(()=>({[qr.CTRL_ALT_C]:()=>n?!1:(e(),!0),[qr.ESC]:()=>n?(t(),!0):!1}),[e,t,n]),o=P(a=>{for(const[i,s]of Object.entries(Jo))if(s.isEventMatching(a)){r[i]()&&(a.preventDefault(),a.stopPropagation());break}},[r]);return Gr("keydown",o,{capture:!0}),null}const Oc=typeof document<"u"?Ea.useLayoutEffect:()=>{};function Pm(e){const t=X(null);return Oc(()=>{t.current=e},[e]),P((...n)=>{const r=t.current;return r==null?void 0:r(...n)},[])}const Rt=e=>{var t;return(t=e==null?void 0:e.ownerDocument)!==null&&t!==void 0?t:document},Ht=e=>e&&"window"in e&&e.window===e?e:Rt(e).defaultView||window;function jc(e,t){return t&&e?e.contains(t):!1}const ea=(e=document)=>e.activeElement;function Lc(e){return e.target}function Tm(e){var t;return typeof window>"u"||window.navigator==null?!1:((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.brands.some(n=>e.test(n.brand)))||e.test(window.navigator.userAgent)}function Rm(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function Fc(e){let t=null;return()=>(t==null&&(t=e()),t)}const zm=Fc(function(){return Rm(/^Mac/i)}),Om=Fc(function(){return Tm(/Android/i)});function Dc(){let e=X(new Map),t=P((o,a,i,s)=>{let c=s!=null&&s.once?(...l)=>{e.current.delete(i),i(...l)}:i;e.current.set(i,{type:a,eventTarget:o,fn:c,options:s}),o.addEventListener(a,c,s)},[]),n=P((o,a,i,s)=>{var c;let l=((c=e.current.get(i))===null||c===void 0?void 0:c.fn)||i;o.removeEventListener(a,l,s),e.current.delete(i)},[]),r=P(()=>{e.current.forEach((o,a)=>{n(o.eventTarget,o.type,a,o.options)})},[n]);return fe(()=>r,[r]),{addGlobalListener:t,removeGlobalListener:n,removeAllGlobalListeners:r}}function jm(e){return e.mozInputSource===0&&e.isTrusted?!0:Om()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function Bc(e){let t=e;return t.nativeEvent=e,t.isDefaultPrevented=()=>t.defaultPrevented,t.isPropagationStopped=()=>t.cancelBubble,t.persist=()=>{},t}function Lm(e,t){Object.defineProperty(e,"target",{value:t}),Object.defineProperty(e,"currentTarget",{value:t})}function Vc(e){let t=X({isFocused:!1,observer:null});Oc(()=>{const r=t.current;return()=>{r.observer&&(r.observer.disconnect(),r.observer=null)}},[]);let n=Pm(r=>{e==null||e(r)});return P(r=>{if(r.target instanceof HTMLButtonElement||r.target instanceof HTMLInputElement||r.target instanceof HTMLTextAreaElement||r.target instanceof HTMLSelectElement){t.current.isFocused=!0;let o=r.target,a=i=>{if(t.current.isFocused=!1,o.disabled){let s=Bc(i);n(s)}t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};o.addEventListener("focusout",a,{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&o.disabled){var i;(i=t.current.observer)===null||i===void 0||i.disconnect();let s=o===document.activeElement?null:document.activeElement;o.dispatchEvent(new FocusEvent("blur",{relatedTarget:s})),o.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:s}))}}),t.current.observer.observe(o,{attributes:!0,attributeFilter:["disabled"]})}},[n])}let Fm=!1,mr=null,ta=new Set,Rn=new Map,Yt=!1,na=!1;const Dm={Tab:!0,Escape:!0};function Ia(e,t){for(let n of ta)n(e,t)}function Bm(e){return!(e.metaKey||!zm()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Zr(e){Yt=!0,Bm(e)&&(mr="keyboard",Ia("keyboard",e))}function on(e){mr="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(Yt=!0,Ia("pointer",e))}function Hc(e){jm(e)&&(Yt=!0,mr="virtual")}function Wc(e){e.target===window||e.target===document||Fm||!e.isTrusted||(!Yt&&!na&&(mr="virtual",Ia("virtual",e)),Yt=!1,na=!1)}function Uc(){Yt=!1,na=!0}function ra(e){if(typeof window>"u"||Rn.get(Ht(e)))return;const t=Ht(e),n=Rt(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){Yt=!0,r.apply(this,arguments)},n.addEventListener("keydown",Zr,!0),n.addEventListener("keyup",Zr,!0),n.addEventListener("click",Hc,!0),t.addEventListener("focus",Wc,!0),t.addEventListener("blur",Uc,!1),typeof PointerEvent<"u"&&(n.addEventListener("pointerdown",on,!0),n.addEventListener("pointermove",on,!0),n.addEventListener("pointerup",on,!0)),t.addEventListener("beforeunload",()=>{qc(e)},{once:!0}),Rn.set(t,{focus:r})}const qc=(e,t)=>{const n=Ht(e),r=Rt(e);t&&r.removeEventListener("DOMContentLoaded",t),Rn.has(n)&&(n.HTMLElement.prototype.focus=Rn.get(n).focus,r.removeEventListener("keydown",Zr,!0),r.removeEventListener("keyup",Zr,!0),r.removeEventListener("click",Hc,!0),n.removeEventListener("focus",Wc,!0),n.removeEventListener("blur",Uc,!1),typeof PointerEvent<"u"&&(r.removeEventListener("pointerdown",on,!0),r.removeEventListener("pointermove",on,!0),r.removeEventListener("pointerup",on,!0)),Rn.delete(n))};function Vm(e){const t=Rt(e);let n;return t.readyState!=="loading"?ra(e):(n=()=>{ra(e)},t.addEventListener("DOMContentLoaded",n)),()=>qc(e,n)}typeof document<"u"&&Vm();function Gc(){return mr!=="pointer"}const Hm=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Wm(e,t,n){let r=Rt(n==null?void 0:n.target);const o=typeof window<"u"?Ht(n==null?void 0:n.target).HTMLInputElement:HTMLInputElement,a=typeof window<"u"?Ht(n==null?void 0:n.target).HTMLTextAreaElement:HTMLTextAreaElement,i=typeof window<"u"?Ht(n==null?void 0:n.target).HTMLElement:HTMLElement,s=typeof window<"u"?Ht(n==null?void 0:n.target).KeyboardEvent:KeyboardEvent;return e=e||r.activeElement instanceof o&&!Hm.has(r.activeElement.type)||r.activeElement instanceof a||r.activeElement instanceof i&&r.activeElement.isContentEditable,!(e&&t==="keyboard"&&n instanceof s&&!Dm[n.key])}function Um(e,t,n){ra(),fe(()=>{let r=(o,a)=>{Wm(!!(n!=null&&n.isTextInput),o,a)&&e(Gc())};return ta.add(r),()=>{ta.delete(r)}},t)}function qm(e){let{isDisabled:t,onFocus:n,onBlur:r,onFocusChange:o}=e;const a=P(c=>{if(c.target===c.currentTarget)return r&&r(c),o&&o(!1),!0},[r,o]),i=Vc(a),s=P(c=>{const l=Rt(c.target),u=l?ea(l):ea();c.target===c.currentTarget&&u===Lc(c.nativeEvent)&&(n&&n(c),o&&o(!0),i(c))},[o,n,i]);return{focusProps:{onFocus:!t&&(n||o||r)?s:void 0,onBlur:!t&&(r||o)?a:void 0}}}function Gm(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:r,onFocusWithinChange:o}=e,a=X({isFocusWithin:!1}),{addGlobalListener:i,removeAllGlobalListeners:s}=Dc(),c=P(d=>{d.currentTarget.contains(d.target)&&a.current.isFocusWithin&&!d.currentTarget.contains(d.relatedTarget)&&(a.current.isFocusWithin=!1,s(),n&&n(d),o&&o(!1))},[n,o,a,s]),l=Vc(c),u=P(d=>{if(!d.currentTarget.contains(d.target))return;const p=Rt(d.target),m=ea(p);if(!a.current.isFocusWithin&&m===Lc(d.nativeEvent)){r&&r(d),o&&o(!0),a.current.isFocusWithin=!0,l(d);let v=d.currentTarget;i(p,"focus",g=>{if(a.current.isFocusWithin&&!jc(v,g.target)){let w=new p.defaultView.FocusEvent("blur",{relatedTarget:g.target});Lm(w,v);let b=Bc(w);c(b)}},{capture:!0})}},[r,o,l,i,c]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:u,onBlur:c}}}let oa=!1,ko=0;function Zm(){oa=!0,setTimeout(()=>{oa=!1},50)}function Bi(e){e.pointerType==="touch"&&Zm()}function Ym(){if(!(typeof document>"u"))return typeof PointerEvent<"u"&&document.addEventListener("pointerup",Bi),ko++,()=>{ko--,!(ko>0)&&(typeof PointerEvent<"u"&&document.removeEventListener("pointerup",Bi))}}function Zc(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:r,isDisabled:o}=e,[a,i]=ie(!1),s=X({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;fe(Ym,[]);let{addGlobalListener:c,removeAllGlobalListeners:l}=Dc(),{hoverProps:u,triggerHoverEnd:d}=be(()=>{let p=(g,w)=>{if(s.pointerType=w,o||w==="touch"||s.isHovered||!g.currentTarget.contains(g.target))return;s.isHovered=!0;let b=g.currentTarget;s.target=b,c(Rt(g.target),"pointerover",y=>{s.isHovered&&s.target&&!jc(s.target,y.target)&&m(y,y.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:b,pointerType:w}),n&&n(!0),i(!0)},m=(g,w)=>{let b=s.target;s.pointerType="",s.target=null,!(w==="touch"||!s.isHovered||!b)&&(s.isHovered=!1,l(),r&&r({type:"hoverend",target:b,pointerType:w}),n&&n(!1),i(!1))},v={};return typeof PointerEvent<"u"&&(v.onPointerEnter=g=>{oa&&g.pointerType==="mouse"||p(g,g.pointerType)},v.onPointerLeave=g=>{!o&&g.currentTarget.contains(g.target)&&m(g,g.pointerType)}),{hoverProps:v,triggerHoverEnd:m}},[t,n,r,o,s,c,l]);return fe(()=>{o&&d({currentTarget:s.target},s.pointerType)},[o]),{hoverProps:u,isHovered:a}}function Yc(e={}){let{autoFocus:t=!1,isTextInput:n,within:r}=e,o=X({isFocused:!1,isFocusVisible:t||Gc()}),[a,i]=ie(!1),[s,c]=ie(()=>o.current.isFocused&&o.current.isFocusVisible),l=P(()=>c(o.current.isFocused&&o.current.isFocusVisible),[]),u=P(m=>{o.current.isFocused=m,i(m),l()},[l]);Um(m=>{o.current.isFocusVisible=m,l()},[],{isTextInput:n});let{focusProps:d}=qm({isDisabled:r,onFocusChange:u}),{focusWithinProps:p}=Gm({isDisabled:!r,onFocusWithinChange:u});return{isFocused:a,isFocusVisible:s,focusProps:r?p:d}}var Km=Object.defineProperty,Qm=(e,t,n)=>t in e?Km(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,So=(e,t,n)=>(Qm(e,typeof t!="symbol"?t+"":t,n),n);let Xm=class{constructor(){So(this,"current",this.detect()),So(this,"handoffState","pending"),So(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},Kc=new Xm;function Jm(e){var t,n;return Kc.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?(n=(t=e.current)==null?void 0:t.ownerDocument)!=null?n:document:null:document}function ef(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function Qc(){let e=[],t={addEventListener(n,r,o,a){return n.addEventListener(r,o,a),t.add(()=>n.removeEventListener(r,o,a))},requestAnimationFrame(...n){let r=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(r))},nextFrame(...n){return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(...n){let r=setTimeout(...n);return t.add(()=>clearTimeout(r))},microTask(...n){let r={current:!0};return ef(()=>{r.current&&n[0]()}),t.add(()=>{r.current=!1})},style(n,r,o){let a=n.style.getPropertyValue(r);return Object.assign(n.style,{[r]:o}),this.add(()=>{Object.assign(n.style,{[r]:a})})},group(n){let r=Qc();return n(r),this.add(()=>r.dispose())},add(n){return e.includes(n)||e.push(n),()=>{let r=e.indexOf(n);if(r>=0)for(let o of e.splice(r,1))o()}},dispose(){for(let n of e.splice(0))n()}};return t}function tf(){let[e]=ie(Qc);return fe(()=>()=>e.dispose(),[e]),e}let $a=(e,t)=>{Kc.isServer?fe(e,t):gn(e,t)};function nf(e){let t=X(e);return $a(()=>{t.current=e},[e]),t}let Yr=function(e){let t=nf(e);return Ea.useCallback((...n)=>t.current(...n),[t])};function rf(e){let t=e.width/2,n=e.height/2;return{top:e.clientY-n,right:e.clientX+t,bottom:e.clientY+n,left:e.clientX-t}}function of(e,t){return!(!e||!t||e.right<t.left||e.left>t.right||e.bottom<t.top||e.top>t.bottom)}function af({disabled:e=!1}={}){let t=X(null),[n,r]=ie(!1),o=tf(),a=Yr(()=>{t.current=null,r(!1),o.dispose()}),i=Yr(s=>{if(o.dispose(),t.current===null){t.current=s.currentTarget,r(!0);{let c=Jm(s.currentTarget);o.addEventListener(c,"pointerup",a,!1),o.addEventListener(c,"pointermove",l=>{if(t.current){let u=rf(l);r(of(u,t.current.getBoundingClientRect()))}},!1),o.addEventListener(c,"pointercancel",a,!1)}}});return{pressed:n,pressProps:e?{}:{onPointerDown:i,onPointerUp:a,onClick:a}}}let sf=Ge(void 0);function so(){return Pe(sf)}function Vi(...e){return Array.from(new Set(e.flatMap(t=>typeof t=="string"?t.split(" "):[]))).filter(Boolean).join(" ")}function Xc(e,t,...n){if(e in t){let o=t[e];return typeof o=="function"?o(...n):o}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(o=>`"${o}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,Xc),r}var cf=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(cf||{}),lf=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(lf||{});function co(){let e=uf();return P(t=>df({mergeRefs:e,...t}),[e])}function df({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:a=!0,name:i,mergeRefs:s}){s=s??pf;let c=Jc(t,e);if(a)return Nr(c,n,r,i,s);let l=o??0;if(l&2){let{static:u=!1,...d}=c;if(u)return Nr(d,n,r,i,s)}if(l&1){let{unmount:u=!0,...d}=c;return Xc(u?0:1,{0(){return null},1(){return Nr({...d,hidden:!0,style:{display:"none"}},n,r,i,s)}})}return Nr(c,n,r,i,s)}function Nr(e,t={},n,r,o){let{as:a=n,children:i,refName:s="ref",...c}=Co(e,["unmount","static"]),l=e.ref!==void 0?{[s]:e.ref}:{},u=typeof i=="function"?i(t):i;"className"in c&&c.className&&typeof c.className=="function"&&(c.className=c.className(t)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let d={};if(t){let p=!1,m=[];for(let[v,g]of Object.entries(t))typeof g=="boolean"&&(p=!0),g===!0&&m.push(v.replace(/([A-Z])/g,w=>`-${w.toLowerCase()}`));if(p){d["data-headlessui-state"]=m.join(" ");for(let v of m)d[`data-${v}`]=""}}if(a===je&&(Object.keys(Jt(c)).length>0||Object.keys(Jt(d)).length>0))if(!ur(u)||Array.isArray(u)&&u.length>1){if(Object.keys(Jt(c)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(Jt(c)).concat(Object.keys(Jt(d))).map(p=>`  - ${p}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(p=>`  - ${p}`).join(`
`)].join(`
`))}else{let p=u.props,m=p==null?void 0:p.className,v=typeof m=="function"?(...b)=>Vi(m(...b),c.className):Vi(m,c.className),g=v?{className:v}:{},w=Jc(u.props,Jt(Co(c,["ref"])));for(let b in d)b in w&&delete d[b];return rc(u,Object.assign({},w,d,l,{ref:o(hf(u),l.ref)},g))}return Le(a,Object.assign({},Co(c,["ref"]),a!==je&&l,a!==je&&d),u)}function uf(){let e=X([]),t=P(n=>{for(let r of e.current)r!=null&&(typeof r=="function"?r(n):r.current=n)},[]);return(...n)=>{if(!n.every(r=>r==null))return e.current=n,t}}function pf(...e){return e.every(t=>t==null)?void 0:t=>{for(let n of e)n!=null&&(typeof n=="function"?n(t):n.current=t)}}function Jc(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let o in r)o.startsWith("on")&&typeof r[o]=="function"?(n[o]!=null||(n[o]=[]),n[o].push(r[o])):t[o]=r[o];if(t.disabled||t["aria-disabled"])for(let r in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(n[r]=[o=>{var a;return(a=o==null?void 0:o.preventDefault)==null?void 0:a.call(o)}]);for(let r in n)Object.assign(t,{[r](o,...a){let i=n[r];for(let s of i){if((o instanceof Event||(o==null?void 0:o.nativeEvent)instanceof Event)&&o.defaultPrevented)return;s(o,...a)}}});return t}function el(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let o in r)o.startsWith("on")&&typeof r[o]=="function"?(n[o]!=null||(n[o]=[]),n[o].push(r[o])):t[o]=r[o];for(let r in n)Object.assign(t,{[r](...o){let a=n[r];for(let i of a)i==null||i(...o)}});return t}function lo(e){var t;return Object.assign(dr(e),{displayName:(t=e.displayName)!=null?t:e.name})}function Jt(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function Co(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function hf(e){return Ea.version.split(".")[0]>="19"?e.props.ref:e.ref}let mf="button";function ff(e,t){var n;let r=so(),{disabled:o=r||!1,autoFocus:a=!1,...i}=e,{isFocusVisible:s,focusProps:c}=Yc({autoFocus:a}),{isHovered:l,hoverProps:u}=Zc({isDisabled:o}),{pressed:d,pressProps:p}=af({disabled:o}),m=el({ref:t,type:(n=i.type)!=null?n:"button",disabled:o||void 0,autoFocus:a},c,u,p),v=be(()=>({disabled:o,hover:l,focus:s,active:d,autofocus:a}),[o,l,s,d,a]);return co()({ourProps:m,theirProps:i,slot:v,defaultTag:mf,name:"Button"})}let Pa=lo(ff),gf=Ge(void 0);function tl(){return Pe(gf)}let vf=Symbol();function nl(...e){let t=X(e);fe(()=>{t.current=e},[e]);let n=Yr(r=>{for(let o of t.current)o!=null&&(typeof o=="function"?o(r):o.current=r)});return e.every(r=>r==null||(r==null?void 0:r[vf]))?void 0:n}let Ta=Ge(null);Ta.displayName="DescriptionContext";function rl(){let e=Pe(Ta);if(e===null){let t=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,rl),t}return e}function wf(){var e,t;return(t=(e=Pe(Ta))==null?void 0:e.value)!=null?t:void 0}let bf="p";function yf(e,t){let n=lr(),r=so(),{id:o=`headlessui-description-${n}`,...a}=e,i=rl(),s=nl(t);$a(()=>i.register(o),[o,i.register]);let c=r||!1,l=be(()=>({...i.slot,disabled:c}),[i.slot,c]),u={ref:s,...i.props,id:o};return co()({ourProps:u,theirProps:a,slot:l,defaultTag:bf,name:i.name||"Description"})}let _f=lo(yf);Object.assign(_f,{});let Ra=Ge(null);Ra.displayName="LabelContext";function ol(){let e=Pe(Ra);if(e===null){let t=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,ol),t}return e}function xf(e){var t,n,r;let o=(n=(t=Pe(Ra))==null?void 0:t.value)!=null?n:void 0;return((r=void 0)!=null?r:0)>0?[o,...e].filter(Boolean).join(" "):o}let kf="label";function Sf(e,t){var n;let r=lr(),o=ol(),a=tl(),i=so(),{id:s=`headlessui-label-${r}`,htmlFor:c=a??((n=o.props)==null?void 0:n.htmlFor),passive:l=!1,...u}=e,d=nl(t);$a(()=>o.register(s),[s,o.register]);let p=Yr(w=>{let b=w.currentTarget;if(b instanceof HTMLLabelElement&&w.preventDefault(),o.props&&"onClick"in o.props&&typeof o.props.onClick=="function"&&o.props.onClick(w),b instanceof HTMLLabelElement){let y=document.getElementById(b.htmlFor);if(y){let S=y.getAttribute("disabled");if(S==="true"||S==="")return;let M=y.getAttribute("aria-disabled");if(M==="true"||M==="")return;(y instanceof HTMLInputElement&&(y.type==="radio"||y.type==="checkbox")||y.role==="radio"||y.role==="checkbox"||y.role==="switch")&&y.click(),y.focus({preventScroll:!0})}}}),m=i||!1,v=be(()=>({...o.slot,disabled:m}),[o.slot,m]),g={ref:d,...o.props,id:s,htmlFor:c,onClick:p};return l&&("onClick"in g&&(delete g.htmlFor,delete g.onClick),"onClick"in u&&delete u.onClick),co()({ourProps:g,theirProps:u,slot:v,defaultTag:c?kf:"div",name:o.name||"Label"})}let Cf=lo(Sf);Object.assign(Cf,{});let Ef="textarea";function Nf(e,t){let n=lr(),r=tl(),o=so(),{id:a=r||`headlessui-textarea-${n}`,disabled:i=o||!1,autoFocus:s=!1,invalid:c=!1,...l}=e,u=xf(),d=wf(),{isFocused:p,focusProps:m}=Yc({autoFocus:s}),{isHovered:v,hoverProps:g}=Zc({isDisabled:i}),w=el({ref:t,id:a,"aria-labelledby":u,"aria-describedby":d,"aria-invalid":c?"true":void 0,disabled:i||void 0,autoFocus:s},m,g),b=be(()=>({disabled:i,invalid:c,hover:v,focus:p,autofocus:s}),[i,c,v,p,s]);return co()({ourProps:w,theirProps:l,slot:b,defaultTag:Ef,name:"Textarea"})}let Af=lo(Nf);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mf=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),If=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),Hi=e=>{const t=If(e);return t.charAt(0).toUpperCase()+t.slice(1)},al=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim(),$f=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Pf={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tf=dr(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:a,iconNode:i,...s},c)=>Le("svg",{ref:c,...Pf,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:al("lucide",o),...!a&&!$f(s)&&{"aria-hidden":"true"},...s},[...i.map(([l,u])=>Le(l,u)),...Array.isArray(a)?a:[a]]));/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dt=(e,t)=>{const n=dr(({className:r,...o},a)=>Le(Tf,{ref:a,iconNode:t,className:al(`lucide-${Mf(Hi(e))}`,`lucide-${e}`,r),...o}));return n.displayName=Hi(e),n};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rf=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],zf=dt("chevron-down",Rf);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Of=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],jf=dt("chevron-up",Of);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lf=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],Ff=dt("message-circle",Lf);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Df=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Bf=dt("plus",Df);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=[["path",{d:"M15.39 4.39a1 1 0 0 0 1.68-.474 2.5 2.5 0 1 1 3.014 3.015 1 1 0 0 0-.474 1.68l1.683 1.682a2.414 2.414 0 0 1 0 3.414L19.61 15.39a1 1 0 0 1-1.68-.474 2.5 2.5 0 1 0-3.014 3.015 1 1 0 0 1 .474 1.68l-1.683 1.682a2.414 2.414 0 0 1-3.414 0L8.61 19.61a1 1 0 0 0-1.68.474 2.5 2.5 0 1 1-3.014-3.015 1 1 0 0 0 .474-1.68l-1.683-1.682a2.414 2.414 0 0 1 0-3.414L4.39 8.61a1 1 0 0 1 1.68.474 2.5 2.5 0 1 0 3.014-3.015 1 1 0 0 1-.474-1.68l1.683-1.682a2.414 2.414 0 0 1 3.414 0z",key:"w46dr5"}]],Hf=dt("puzzle",Vf);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wf=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],bn=dt("refresh-cw",Wf);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],qf=dt("send",Uf);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gf=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Zf=dt("settings",Gf);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yf=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Kf=dt("trash-2",Yf);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qf=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],il=dt("wifi-off",Qf),Xf={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},sl={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},ke={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},Te={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},St={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class _{static getFirstMatch(t,n){const r=n.match(t);return r&&r.length>0&&r[1]||""}static getSecondMatch(t,n){const r=n.match(t);return r&&r.length>1&&r[2]||""}static matchAndReturnConst(t,n,r){if(t.test(n))return r}static getWindowsVersionName(t){switch(t){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(t){const n=t.split(".").splice(0,2).map(r=>parseInt(r,10)||0);if(n.push(0),n[0]===10)switch(n[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(t){const n=t.split(".").splice(0,2).map(r=>parseInt(r,10)||0);if(n.push(0),!(n[0]===1&&n[1]<5)){if(n[0]===1&&n[1]<6)return"Cupcake";if(n[0]===1&&n[1]>=6)return"Donut";if(n[0]===2&&n[1]<2)return"Eclair";if(n[0]===2&&n[1]===2)return"Froyo";if(n[0]===2&&n[1]>2)return"Gingerbread";if(n[0]===3)return"Honeycomb";if(n[0]===4&&n[1]<1)return"Ice Cream Sandwich";if(n[0]===4&&n[1]<4)return"Jelly Bean";if(n[0]===4&&n[1]>=4)return"KitKat";if(n[0]===5)return"Lollipop";if(n[0]===6)return"Marshmallow";if(n[0]===7)return"Nougat";if(n[0]===8)return"Oreo";if(n[0]===9)return"Pie"}}static getVersionPrecision(t){return t.split(".").length}static compareVersions(t,n,r=!1){const o=_.getVersionPrecision(t),a=_.getVersionPrecision(n);let i=Math.max(o,a),s=0;const c=_.map([t,n],l=>{const u=i-_.getVersionPrecision(l),d=l+new Array(u+1).join(".0");return _.map(d.split("."),p=>new Array(20-p.length).join("0")+p).reverse()});for(r&&(s=i-Math.min(o,a)),i-=1;i>=s;){if(c[0][i]>c[1][i])return 1;if(c[0][i]===c[1][i]){if(i===s)return 0;i-=1}else if(c[0][i]<c[1][i])return-1}}static map(t,n){const r=[];let o;if(Array.prototype.map)return Array.prototype.map.call(t,n);for(o=0;o<t.length;o+=1)r.push(n(t[o]));return r}static find(t,n){let r,o;if(Array.prototype.find)return Array.prototype.find.call(t,n);for(r=0,o=t.length;r<o;r+=1){const a=t[r];if(n(a,r))return a}}static assign(t,...n){const r=t;let o,a;if(Object.assign)return Object.assign(t,...n);for(o=0,a=n.length;o<a;o+=1){const i=n[o];typeof i=="object"&&i!==null&&Object.keys(i).forEach(s=>{r[s]=i[s]})}return t}static getBrowserAlias(t){return Xf[t]}static getBrowserTypeByAlias(t){return sl[t]||""}}const le=/version\/(\d+(\.?_?\d+)+)/i,Jf=[{test:[/googlebot/i],describe(e){const t={name:"Googlebot"},n=_.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/opera/i],describe(e){const t={name:"Opera"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opr\/|opios/i],describe(e){const t={name:"Opera"},n=_.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/SamsungBrowser/i],describe(e){const t={name:"Samsung Internet for Android"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Whale/i],describe(e){const t={name:"NAVER Whale Browser"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MZBrowser/i],describe(e){const t={name:"MZ Browser"},n=_.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/focus/i],describe(e){const t={name:"Focus"},n=_.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/swing/i],describe(e){const t={name:"Swing"},n=_.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/coast/i],describe(e){const t={name:"Opera Coast"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(e){const t={name:"Opera Touch"},n=_.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/yabrowser/i],describe(e){const t={name:"Yandex Browser"},n=_.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/ucbrowser/i],describe(e){const t={name:"UC Browser"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Maxthon|mxios/i],describe(e){const t={name:"Maxthon"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/epiphany/i],describe(e){const t={name:"Epiphany"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/puffin/i],describe(e){const t={name:"Puffin"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sleipnir/i],describe(e){const t={name:"Sleipnir"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/k-meleon/i],describe(e){const t={name:"K-Meleon"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/micromessenger/i],describe(e){const t={name:"WeChat"},n=_.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/qqbrowser/i],describe(e){const t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},n=_.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/msie|trident/i],describe(e){const t={name:"Internet Explorer"},n=_.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/\sedg\//i],describe(e){const t={name:"Microsoft Edge"},n=_.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/edg([ea]|ios)/i],describe(e){const t={name:"Microsoft Edge"},n=_.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/vivaldi/i],describe(e){const t={name:"Vivaldi"},n=_.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/seamonkey/i],describe(e){const t={name:"SeaMonkey"},n=_.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sailfish/i],describe(e){const t={name:"Sailfish"},n=_.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return n&&(t.version=n),t}},{test:[/silk/i],describe(e){const t={name:"Amazon Silk"},n=_.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/phantom/i],describe(e){const t={name:"PhantomJS"},n=_.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/slimerjs/i],describe(e){const t={name:"SlimerJS"},n=_.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){const t={name:"BlackBerry"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(web|hpw)[o0]s/i],describe(e){const t={name:"WebOS Browser"},n=_.getFirstMatch(le,e)||_.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/bada/i],describe(e){const t={name:"Bada"},n=_.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/tizen/i],describe(e){const t={name:"Tizen"},n=_.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/qupzilla/i],describe(e){const t={name:"QupZilla"},n=_.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/firefox|iceweasel|fxios/i],describe(e){const t={name:"Firefox"},n=_.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/electron/i],describe(e){const t={name:"Electron"},n=_.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MiuiBrowser/i],describe(e){const t={name:"Miui"},n=_.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/chromium/i],describe(e){const t={name:"Chromium"},n=_.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/chrome|crios|crmo/i],describe(e){const t={name:"Chrome"},n=_.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/GSA/i],describe(e){const t={name:"Google Search"},n=_.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test(e){const t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe(e){const t={name:"Android Browser"},n=_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/playstation 4/i],describe(e){const t={name:"PlayStation 4"},n=_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/safari|applewebkit/i],describe(e){const t={name:"Safari"},n=_.getFirstMatch(le,e);return n&&(t.version=n),t}},{test:[/.*/i],describe(e){const t=/^(.*)\/(.*) /,n=/^(.*)\/(.*)[ \t]\((.*)/,r=e.search("\\(")!==-1?n:t;return{name:_.getFirstMatch(r,e),version:_.getSecondMatch(r,e)}}}],eg=[{test:[/Roku\/DVP/],describe(e){const t=_.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:Te.Roku,version:t}}},{test:[/windows phone/i],describe(e){const t=_.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:Te.WindowsPhone,version:t}}},{test:[/windows /i],describe(e){const t=_.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),n=_.getWindowsVersionName(t);return{name:Te.Windows,version:t,versionName:n}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(e){const t={name:Te.iOS},n=_.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return n&&(t.version=n),t}},{test:[/macintosh/i],describe(e){const t=_.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),n=_.getMacOSVersionName(t),r={name:Te.MacOS,version:t};return n&&(r.versionName=n),r}},{test:[/(ipod|iphone|ipad)/i],describe(e){const t=_.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:Te.iOS,version:t}}},{test(e){const t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe(e){const t=_.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),n=_.getAndroidVersionName(t),r={name:Te.Android,version:t};return n&&(r.versionName=n),r}},{test:[/(web|hpw)[o0]s/i],describe(e){const t=_.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),n={name:Te.WebOS};return t&&t.length&&(n.version=t),n}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){const t=_.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||_.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||_.getFirstMatch(/\bbb(\d+)/i,e);return{name:Te.BlackBerry,version:t}}},{test:[/bada/i],describe(e){const t=_.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:Te.Bada,version:t}}},{test:[/tizen/i],describe(e){const t=_.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:Te.Tizen,version:t}}},{test:[/linux/i],describe(){return{name:Te.Linux}}},{test:[/CrOS/],describe(){return{name:Te.ChromeOS}}},{test:[/PlayStation 4/],describe(e){const t=_.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:Te.PlayStation4,version:t}}}],tg=[{test:[/googlebot/i],describe(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe(e){const t=_.getFirstMatch(/(can-l01)/i,e)&&"Nova",n={type:ke.mobile,vendor:"Huawei"};return t&&(n.model=t),n}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe(){return{type:ke.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe(){return{type:ke.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(){return{type:ke.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe(){return{type:ke.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe(){return{type:ke.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe(){return{type:ke.tablet}}},{test(e){const t=e.test(/ipod|iphone/i),n=e.test(/like (ipod|iphone)/i);return t&&!n},describe(e){const t=_.getFirstMatch(/(ipod|iphone)/i,e);return{type:ke.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe(){return{type:ke.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe(){return{type:ke.mobile}}},{test(e){return e.getBrowserName(!0)==="blackberry"},describe(){return{type:ke.mobile,vendor:"BlackBerry"}}},{test(e){return e.getBrowserName(!0)==="bada"},describe(){return{type:ke.mobile}}},{test(e){return e.getBrowserName()==="windows phone"},describe(){return{type:ke.mobile,vendor:"Microsoft"}}},{test(e){const t=Number(String(e.getOSVersion()).split(".")[0]);return e.getOSName(!0)==="android"&&t>=3},describe(){return{type:ke.tablet}}},{test(e){return e.getOSName(!0)==="android"},describe(){return{type:ke.mobile}}},{test(e){return e.getOSName(!0)==="macos"},describe(){return{type:ke.desktop,vendor:"Apple"}}},{test(e){return e.getOSName(!0)==="windows"},describe(){return{type:ke.desktop}}},{test(e){return e.getOSName(!0)==="linux"},describe(){return{type:ke.desktop}}},{test(e){return e.getOSName(!0)==="playstation 4"},describe(){return{type:ke.tv}}},{test(e){return e.getOSName(!0)==="roku"},describe(){return{type:ke.tv}}}],ng=[{test(e){return e.getBrowserName(!0)==="microsoft edge"},describe(e){if(/\sedg\//i.test(e))return{name:St.Blink};const t=_.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:St.EdgeHTML,version:t}}},{test:[/trident/i],describe(e){const t={name:St.Trident},n=_.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test(e){return e.test(/presto/i)},describe(e){const t={name:St.Presto},n=_.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test(e){const t=e.test(/gecko/i),n=e.test(/like gecko/i);return t&&!n},describe(e){const t={name:St.Gecko},n=_.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(apple)?webkit\/537\.36/i],describe(){return{name:St.Blink}}},{test:[/(apple)?webkit/i],describe(e){const t={name:St.WebKit},n=_.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}}];class Wi{constructor(t,n=!1){if(t==null||t==="")throw new Error("UserAgent parameter can't be empty");this._ua=t,this.parsedResult={},n!==!0&&this.parse()}getUA(){return this._ua}test(t){return t.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const t=_.find(Jf,n=>{if(typeof n.test=="function")return n.test(this);if(n.test instanceof Array)return n.test.some(r=>this.test(r));throw new Error("Browser's test function is not valid")});return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(t){return t?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const t=_.find(eg,n=>{if(typeof n.test=="function")return n.test(this);if(n.test instanceof Array)return n.test.some(r=>this.test(r));throw new Error("Browser's test function is not valid")});return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os}getOSName(t){const{name:n}=this.getOS();return t?String(n).toLowerCase()||"":n||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(t=!1){const{type:n}=this.getPlatform();return t?String(n).toLowerCase()||"":n||""}parsePlatform(){this.parsedResult.platform={};const t=_.find(tg,n=>{if(typeof n.test=="function")return n.test(this);if(n.test instanceof Array)return n.test.some(r=>this.test(r));throw new Error("Browser's test function is not valid")});return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(t){return t?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const t=_.find(ng,n=>{if(typeof n.test=="function")return n.test(this);if(n.test instanceof Array)return n.test.some(r=>this.test(r));throw new Error("Browser's test function is not valid")});return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return _.assign({},this.parsedResult)}satisfies(t){const n={};let r=0;const o={};let a=0;if(Object.keys(t).forEach(i=>{const s=t[i];typeof s=="string"?(o[i]=s,a+=1):typeof s=="object"&&(n[i]=s,r+=1)}),r>0){const i=Object.keys(n),s=_.find(i,l=>this.isOS(l));if(s){const l=this.satisfies(n[s]);if(l!==void 0)return l}const c=_.find(i,l=>this.isPlatform(l));if(c){const l=this.satisfies(n[c]);if(l!==void 0)return l}}if(a>0){const i=Object.keys(o),s=_.find(i,c=>this.isBrowser(c,!0));if(s!==void 0)return this.compareVersion(o[s])}}isBrowser(t,n=!1){const r=this.getBrowserName().toLowerCase();let o=t.toLowerCase();const a=_.getBrowserTypeByAlias(o);return n&&a&&(o=a.toLowerCase()),o===r}compareVersion(t){let n=[0],r=t,o=!1;const a=this.getBrowserVersion();if(typeof a=="string")return t[0]===">"||t[0]==="<"?(r=t.substr(1),t[1]==="="?(o=!0,r=t.substr(2)):n=[],t[0]===">"?n.push(1):n.push(-1)):t[0]==="="?r=t.substr(1):t[0]==="~"&&(o=!0,r=t.substr(1)),n.indexOf(_.compareVersions(a,r,o))>-1}isOS(t){return this.getOSName(!0)===String(t).toLowerCase()}isPlatform(t){return this.getPlatformType(!0)===String(t).toLowerCase()}isEngine(t){return this.getEngineName(!0)===String(t).toLowerCase()}is(t,n=!1){return this.isBrowser(t,n)||this.isOS(t)||this.isPlatform(t)}some(t=[]){return t.some(n=>this.is(n))}}/*!
 * Bowser - a browser detector
 * https://github.com/lancedikson/bowser
 * MIT License | (c) Dustin Diaz 2012-2015
 * MIT License | (c) Denis Demchenko 2015-2019
 */class rg{static getParser(t,n=!1){if(typeof t!="string")throw new Error("UserAgent should be a string");return new Wi(t,n)}static parse(t){return new Wi(t).getResult()}static get BROWSER_MAP(){return sl}static get ENGINE_MAP(){return St}static get OS_MAP(){return Te}static get PLATFORMS_MAP(){return ke}}const og=()=>be(()=>{{const e=rg.parse(window.navigator.userAgent);return{browser:e.browser,engine:e.engine,os:e.os}}},[]);function ag(e){return og().os.name.toLowerCase().includes("mac")?Jo[e].keyComboMac:Jo[e].keyComboDefault}function ig(){const e=hr(),[t,n]=ie(!1),r=be(()=>e.chats.find(g=>g.id===e.currentChatId),[e.chats,e.currentChatId]),o=be(()=>(r==null?void 0:r.inputValue)||"",[r==null?void 0:r.inputValue]),a=P(g=>{e.setChatInput(e.currentChatId,g)},[e.setChatInput,e.currentChatId]),i=P(()=>{!r||!o.trim()||e.addMessage(r.id,o)},[r,o,e.addMessage]),s=P(g=>{g.key==="Enter"&&!g.shiftKey&&!t&&(g.preventDefault(),i())},[i,t]),c=P(()=>{n(!0)},[]),l=P(()=>{n(!1)},[]),u=X(null);fe(()=>{var g,w,b;const y=()=>{var S;return(S=u.current)==null?void 0:S.focus()};return e.isPromptCreationActive?((g=u.current)==null||g.focus(),(w=u.current)==null||w.addEventListener("blur",y)):(b=u.current)==null||b.blur(),()=>{var S;(S=u.current)==null||S.removeEventListener("blur",y)}},[e.isPromptCreationActive]);const d=be(()=>Se("flex size-8 items-center justify-center rounded-full bg-transparent p-1 text-zinc-950 opacity-20 transition-all duration-150",o.length>0&&"bg-blue-600 text-white opacity-100",e.promptState==="loading"&&"cursor-not-allowed bg-zinc-300 text-zinc-500 opacity-30"),[o.length,e.promptState]),p=be(()=>Se("h-full w-full flex-1 resize-none bg-transparent text-zinc-950 transition-all duration-150 placeholder:text-zinc-950/50 focus:outline-none",e.promptState==="loading"&&"text-zinc-500 placeholder:text-zinc-400"),[e.promptState]),m=be(()=>{const g="flex h-24 w-full flex-1 flex-row items-end gap-1 rounded-2xl p-4 text-sm text-zinc-950 shadow-md backdrop-blur transition-all duration-150 placeholder:text-zinc-950/70";switch(e.promptState){case"loading":return Se(g,"border-2 border-transparent bg-zinc-50/80","chat-loading-gradient");case"success":return Se(g,"border-2 border-transparent bg-zinc-50/80","chat-success-border");case"error":return Se(g,"border-2 border-transparent bg-zinc-50/80","chat-error-border animate-shake");default:return Se(g,"border border-border/30 bg-zinc-50/80")}},[e.promptState]),v=ag(qr.CTRL_ALT_C);return h("div",{className:m,onClick:()=>e.startPromptCreation(),role:"button",tabIndex:0,children:[h(Af,{ref:u,className:p,value:o,onChange:g=>a(g.currentTarget.value),onKeyDown:s,onCompositionStart:c,onCompositionEnd:l,placeholder:e.isPromptCreationActive?e.promptState==="loading"?"Processing...":"Enter prompt...":`What do you want to change? (${v})`,disabled:e.promptState==="loading"}),h(Pa,{className:d,disabled:o.length===0||e.promptState==="loading",onClick:i,children:h(qf,{className:"size-4"})})]})}const za=Ge(null),sg=({containerRef:e,children:t,snapAreas:n,onDragStart:r,onDragEnd:o})=>{const[a,i]=ie({top:0,left:0,right:0,bottom:0});fe(()=>{if(!e.current)return;const v=()=>{if(e.current){const w=e.current.getBoundingClientRect();i({top:w.top,left:w.left,right:w.right,bottom:w.bottom})}};v();const g=new ResizeObserver(v);return g.observe(e.current),window.addEventListener("resize",v),()=>{e.current&&g.unobserve(e.current),g.disconnect(),window.removeEventListener("resize",v)}},[e]);const s=X(new Set),c=X(new Set),l=P(v=>(s.current.add(v),()=>s.current.delete(v)),[]),u=P(v=>(c.current.add(v),()=>c.current.delete(v)),[]),d=P(()=>{r&&r(),s.current.forEach(v=>v())},[r]),p=P(()=>{o&&o(),c.current.forEach(v=>v())},[o]),m={borderLocation:a,snapAreas:n,registerDragStart:l,registerDragEnd:u,emitDragStart:d,emitDragEnd:p};return h(za.Provider,{value:m,children:t})};function cg(e){const t=Pe(za),n=X(t);fe(()=>{n.current=t},[t]);const r=X(null),o=X(null),[a,i]=ie(null),[s,c]=ie(null),l=X(null),u=X(null),d=X(null),p=X(!1),m=X(e.initialRelativeCenter),[v,g]=ie(null),{startThreshold:w=3,areaSnapThreshold:b=60,onDragStart:y,onDragEnd:S,initialSnapArea:M,springStiffness:O=.2,springDampness:re=.55}=e,Z=X(null),I=X({x:0,y:0}),Q=X(!1);fe(()=>{if(M&&t&&t.borderLocation&&t.snapAreas&&t.snapAreas[M]&&!p.current){const{top:V,left:de,right:ce,bottom:z}=t.borderLocation,T=ce-de,Y=z-V,ae={topLeft:{x:de,y:V},topRight:{x:ce,y:V},bottomLeft:{x:de,y:z},bottomRight:{x:ce,y:z}}[M];if(ae&&T>0&&Y>0){const _e=(ae.x-de)/T,D=(ae.y-V)/Y;m.current={x:_e,y:D}}else ae&&console.warn("useDraggable: Container for initialSnapArea has zero width or height. Cannot calculate relative center from snap area. Falling back to initialRelativeCenter or undefined.")}},[M,t]);function Ce(V){const{top:de,left:ce,right:z,bottom:T}=V,Y=(ce+z)/2;return{topLeft:{x:ce,y:de},topCenter:{x:Y,y:de},topRight:{x:z,y:de},bottomLeft:{x:ce,y:T},bottomCenter:{x:Y,y:T},bottomRight:{x:z,y:T}}}const te=P(()=>{var V,de;const ce=r.current;if(!ce)return;const z=ce.offsetWidth,T=ce.offsetHeight,Y=ce.offsetParent;let ae=0,_e=0,D=window.innerWidth,oe=window.innerHeight;if(Y){const se=Y.getBoundingClientRect();ae=se.left,_e=se.top,D=Y.offsetWidth||window.innerWidth,oe=Y.offsetHeight||window.innerHeight}let ge=null,he=null;const Ae=m.current;let Fe=null,wt=null;const bt=n.current;let ut=!0,pt=!0;if(p.current&&l.current&&d.current&&bt&&bt.borderLocation&&bt.snapAreas){const se={x:d.current.x-l.current.x,y:d.current.y-l.current.y},Ot=Ce(bt.borderLocation);let xe=Number.POSITIVE_INFINITY,Ie=null,jt=null;for(const Lt in bt.snapAreas)if(bt.snapAreas[Lt]){const gr=Ot[Lt];if(!gr)continue;const La=Math.hypot(gr.x-se.x,gr.y-se.y);La<xe&&(xe=La,Ie=Lt,jt=gr)}Ie&&jt&&xe<=b&&(Fe=Ie,wt=jt),pt=(se.x-ae)/D<=.5,ut=(se.y-_e)/oe<=.5}if(p.current&&wt)ge=wt.x,he=wt.y,g(Fe),pt=(wt.x-ae)/D<=.5,ut=(wt.y-_e)/oe<=.5;else if(p.current&&l.current&&d.current)ge=d.current.x-l.current.x,he=d.current.y-l.current.y,g(null),pt=(ge-ae)/D<=.5,ut=(he-_e)/oe<=.5;else{if(Ae&&D>0&&oe>0){if(ut=Ae.y<=.5,pt=Ae.x<=.5,pt){const se=D*Ae.x;ge=ae+se}else{const se=D*(1-Ae.x);ge=ae+D-se}if(ut){const se=oe*Ae.y;he=_e+se}else{const se=oe*(1-Ae.y);he=_e+oe-se}}else{!((V=r.current)!=null&&V.style.left)&&!((de=r.current)!=null&&de.style.top)&&console.warn("useDraggable: Cannot determine position. Parent has no dimensions or initialRelativeCenter was not effectively set.");return}g(null)}if(ge===null||he===null)return;const{borderLocation:De}=n.current||{borderLocation:void 0};if(De&&z>0&&T>0){const se=De.right-De.left,Ot=De.bottom-De.top;let xe=ge,Ie=he;if(z>=se)xe=De.left+se/2;else{const jt=De.left+z/2,Lt=De.right-z/2;xe=Math.max(jt,Math.min(xe,Lt))}if(T>=Ot)Ie=De.top+Ot/2;else{const jt=De.top+T/2,Lt=De.bottom-T/2;Ie=Math.max(jt,Math.min(Ie,Lt))}ge=xe,he=Ie}if(!Z.current){Z.current={x:ge,y:he},I.current={x:0,y:0};const se=ge-z/2,Ot=he-T/2,xe=ce.style;if(xe.right="",xe.bottom="",xe.left="",xe.top="",pt){const Ie=se-ae;xe.left=D>0?`${(Ie/D*100).toFixed(2)}%`:"0px",xe.right=""}else{const Ie=ae+D-(se+z);xe.right=D>0?`${(Ie/D*100).toFixed(2)}%`:"0px",xe.left=""}if(ut){const Ie=Ot-_e;xe.top=oe>0?`${(Ie/oe*100).toFixed(2)}%`:"0px",xe.bottom=""}else{const Ie=_e+oe-(Ot+T);xe.bottom=oe>0?`${(Ie/oe*100).toFixed(2)}%`:"0px",xe.top=""}Q.current=!0;return}if(!Q.current){Q.current=!0;return}const ze=Z.current,Me=I.current,zt=ge-ze.x,Be=he-ze.y,tt=O*zt-re*Me.x,fr=O*Be-re*Me.y;Me.x+=tt,Me.y+=fr,ze.x+=Me.x,ze.y+=Me.y;const yt=.5;Math.abs(zt)<yt&&Math.abs(Be)<yt&&Math.abs(Me.x)<yt&&Math.abs(Me.y)<yt&&(ze.x=ge,ze.y=he,Me.x=0,Me.y=0),Z.current={...ze},I.current={...Me};const Oa=ze.x-z/2,ja=ze.y-T/2,We=ce.style;if(We.right="",We.bottom="",We.left="",We.top="",pt){const se=Oa-ae;We.left=D>0?`${(se/D*100).toFixed(2)}%`:"0px",We.right=""}else{const se=ae+D-(Oa+z);We.right=D>0?`${(se/D*100).toFixed(2)}%`:"0px",We.left=""}if(ut){const se=ja-_e;We.top=oe>0?`${(se/oe*100).toFixed(2)}%`:"0px",We.bottom=""}else{const se=_e+oe-(ja+T);We.bottom=oe>0?`${(se/oe*100).toFixed(2)}%`:"0px",We.top=""}(Math.abs(ze.x-ge)>yt||Math.abs(ze.y-he)>yt||Math.abs(Me.x)>yt||Math.abs(Me.y)>yt||p.current)&&requestAnimationFrame(te)},[b,O,re]),[ye,B]=ie(!1),U=P(V=>{var de;if(p.current){S&&S(),(de=n.current)!=null&&de.emitDragEnd&&n.current.emitDragEnd(),B(!0),setTimeout(()=>B(!1),0);const ce=r.current,z=n.current;if(ce&&z&&z.borderLocation){const T=ce.offsetWidth,Y=ce.offsetHeight,ae=ce.offsetParent;let _e=0,D=0,oe=window.innerWidth,ge=window.innerHeight;if(ae){const Be=ae.getBoundingClientRect();_e=Be.left,D=Be.top,oe=ae.offsetWidth||window.innerWidth,ge=ae.offsetHeight||window.innerHeight}let he=0,Ae=0;d.current&&l.current?(he=d.current.x-l.current.x,Ae=d.current.y-l.current.y):Z.current&&(he=Z.current.x,Ae=Z.current.y);const Fe=z.borderLocation,wt=Fe.left+T/2,bt=Fe.right-T/2,ut=Fe.top+Y/2,pt=Fe.bottom-Y/2;he=Math.max(wt,Math.min(he,bt)),Ae=Math.max(ut,Math.min(Ae,pt));const De=Ce(Fe);let ze=Number.POSITIVE_INFINITY,Me=null,zt=null;for(const Be in z.snapAreas)if(z.snapAreas[Be]){const tt=De[Be];if(!tt)continue;const fr=Math.hypot(tt.x-he,tt.y-Ae);fr<ze&&(ze=fr,Me=Be,zt=tt)}if(Me&&zt){g(Me);const Be=(zt.x-_e)/oe,tt=(zt.y-D)/ge;m.current={x:Be,y:tt}}else{g(null);const Be=(he-_e)/oe,tt=(Ae-D)/ge;m.current={x:Be,y:tt}}}}u.current=null,p.current=!1,window.removeEventListener("mousemove",pe,{capture:!0}),window.removeEventListener("mouseup",U,{capture:!0}),r.current&&(r.current.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor=""},[S]),pe=P(V=>{var de;u.current&&(Math.hypot(V.clientX-u.current.x,V.clientY-u.current.y)>w&&!p.current&&(p.current=!0,r.current&&(r.current.style.userSelect="none"),document.body.style.userSelect="none",document.body.style.cursor="grabbing",y&&y(),(de=n.current)!=null&&de.emitDragStart&&n.current.emitDragStart(),requestAnimationFrame(te)),d.current={x:V.clientX,y:V.clientY})},[w,y,te]),ne=P(V=>{if(V.button!==0)return;const de=o.current,ce=r.current;if(de){if(!de.contains(V.target)&&V.target!==de)return}else if(ce){if(!ce.contains(V.target)&&V.target!==ce)return}else{console.error("Draggable element or handle ref not set in mouseDownHandler");return}if(u.current={x:V.clientX,y:V.clientY},!r.current){console.error("Draggable element ref not set in mouseDownHandler");return}const z=r.current.getBoundingClientRect(),T=z.left+z.width/2,Y=z.top+z.height/2;l.current={x:V.clientX-T,y:V.clientY-Y},window.addEventListener("mousemove",pe,{capture:!0}),window.addEventListener("mouseup",U,{capture:!0})},[pe,U]);fe(()=>{const V=s||a;return V&&V.addEventListener("mousedown",ne),()=>{V&&V.removeEventListener("mousedown",ne),p.current&&(S&&S(),p.current=!1,a&&(a.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor="",window.removeEventListener("mousemove",pe,{capture:!0}),window.removeEventListener("mouseup",U,{capture:!0}))}},[a,s,ne,S,pe,U]),fe(()=>{r.current&&t&&t.borderLocation&&m.current&&!p.current&&!Q.current&&requestAnimationFrame(()=>{r.current&&te()})},[a,t,e.initialRelativeCenter,M,te]);const Ee=P(V=>{i(V),r.current=V},[]),R=P(V=>{c(V),o.current=V},[]);return{draggableRef:Ee,handleRef:R,position:{snapArea:v,isTopHalf:m.current?m.current.y<=.5:!0,isLeftHalf:m.current?m.current.x<=.5:!0},wasDragged:ye}}function or({children:e}){return h("div",{className:"fade-in slide-in-from-right-2 flex max-h-sm max-w-full animate-in snap-start flex-col items-center justify-between gap-1 py-0.5",children:e})}function lg(e){return h("div",{className:"relative flex w-full shrink-0 items-center justify-center",children:[e.children,e.badgeContent&&h("div",{className:Se("bg-blue-600 text-white",e.badgeClassName,"pointer-events-none absolute right-0 bottom-0 flex h-3 w-max min-w-3 max-w-8 select-none items-center justify-center truncate rounded-full px-0.5 font-semibold text-[0.5em]"),children:e.badgeContent}),e.statusDot&&h("div",{className:Se("bg-rose-600",e.statusDotClassName,"pointer-events-none absolute top-0 right-0 size-1.5 rounded-full")})]})}const hn=dr(({badgeContent:e,badgeClassName:t,statusDot:n,statusDotClassName:r,tooltipHint:o,variant:a="default",active:i,...s},c)=>{const l=h(Pa,{ref:c,...s,className:Se("flex items-center justify-center rounded-full p-1 text-zinc-950 ring ring-transparent transition-all duration-150 hover:bg-zinc-950/5",a==="default"?"size-8":"h-8 rounded-full",i&&"bg-white/40 ring-zinc-950/20",s.className)});return h(lg,{badgeContent:e,badgeClassName:t,statusDot:n,statusDotClassName:r,children:l})});hn.displayName="ToolbarButton";const dg=({color:e="default",loading:t=!1,loadingSpeed:n="slow",...r})=>{const o={default:"fill-stagewise-700 stroke-none",black:"fill-zinc-950 stroke-none",white:"fill-white stroke-none",zinc:"fill-zinc-500/50 stroke-none",current:"fill-current stroke-none",gradient:"fill-white stroke-black/30 stroke-1"};return h("div",{className:`relative ${e==="gradient"?"overflow-hidden rounded-full":"overflow-visible"} ${r.className||""} ${t?"drop-shadow-xl":""} aspect-square`,children:[e==="gradient"&&h("div",{className:"absolute inset-0",children:[h("div",{className:"absolute inset-0 size-full bg-gradient-to-tr from-indigo-700 via-blue-500 to-teal-500"}),h("div",{className:"absolute top-1/2 left-1/2 size-9/12 bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),h("div",{className:"absolute right-1/2 bottom-1/2 size-full bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),h("div",{className:"absolute top-0 left-[-10%] size-[120%] bg-[radial-gradient(circle,rgba(255,255,255,0)_60%,rgba(255,255,255,0.2)_70%)]"}),h("div",{className:"absolute top-[-20%] left-0 h-[120%] w-full bg-[radial-gradient(circle,rgba(55,48,163,0)_55%,rgba(55,48,163,0.35)_73%)]"})]}),h("svg",{className:`absolute overflow-visible ${e==="gradient"?"top-[25%] left-[25%] h-[50%] w-[50%] drop-shadow-indigo-950 drop-shadow-xs":"top-0 left-0 h-full w-full"}`,viewBox:"0 0 2048 2048",children:[h("title",{children:"stagewise"}),h("ellipse",{className:o[e]+(t?" animate-pulse":""),id:"path3",ry:"624",rx:"624",cy:"1024",cx:"1024"})]}),h("svg",{className:`absolute overflow-visible ${e==="gradient"?"top-[25%] left-[25%] h-[50%] w-[50%]":"top-0 left-0 h-full w-full"}`,viewBox:"0 0 2048 2048",children:h("path",{id:"path4",className:`origin-center ${o[e]}${t?n==="fast"?" animate-spin-fast":" animate-spin-slow":""}`,d:"M 1024 0 A 1024 1024 0 0 0 0 1024 A 1024 1024 0 0 0 1024 2048 L 1736 2048 L 1848 2048 C 1958.7998 2048 2048 1958.7998 2048 1848 L 2048 1736 L 2048 1024 A 1024 1024 0 0 0 1024 0 z M 1024.9414 200 A 824 824 0 0 1 1848.9414 1024 A 824 824 0 0 1 1024.9414 1848 A 824 824 0 0 1 200.94141 1024 A 824 824 0 0 1 1024.9414 200 z "})})]})},ug=({onOpenPanel:e,isActive:t=!1})=>h(or,{children:h(hn,{onClick:e,active:t,children:h(Zf,{className:"size-4"})})}),pg=({onClose:e})=>h(Vt,{children:[h(Vt.Header,{title:"Settings"}),h(Vt.Content,{children:h(hg,{})}),h(Vt.Content,{children:h(mg,{})})]}),hg=()=>{const{windows:e,isDiscovering:t,discoveryError:n,discover:r,selectedSession:o,selectSession:a}=Tt(),i=l=>{const u=l.target,d=u.value===""?void 0:u.value;a(d)},{appName:s}=Tt(),c=()=>{r()};return h("div",{className:"space-y-4 pb-4",children:[h("div",{children:[h("label",{htmlFor:"session-select",className:"mb-2 block font-medium text-sm text-zinc-700",children:["IDE Window ",s&&`(${s})`]}),h("div",{className:"flex w-full items-center space-x-2",children:[h("select",{id:"session-select",value:(o==null?void 0:o.sessionId)||"",onChange:i,className:"h-8 min-w-0 flex-1 rounded-lg border border-zinc-300 bg-zinc-500/10 px-3 text-sm backdrop-saturate-150 focus:border-zinc-500 focus:outline-none",disabled:t,children:[h("option",{value:"",disabled:!0,children:e.length>0?"Select an IDE window...":"No windows available"}),e.map(l=>h("option",{value:l.sessionId,children:[l.displayName," - Port ",l.port]},l.sessionId))]}),h("button",{type:"button",onClick:c,disabled:t,className:"flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-zinc-500/10 backdrop-saturate-150 transition-colors hover:bg-zinc-500/20 disabled:opacity-50",title:"Refresh window list",children:h(bn,{className:`size-4 ${t?"animate-spin":""}`})})]}),n&&h("p",{className:"mt-1 text-red-600 text-sm",children:["Error discovering windows: ",n]}),!t&&e.length===0&&!n&&h("p",{className:"mt-1 text-sm text-zinc-500",children:"No IDE windows found. Make sure the Stagewise extension is installed and running."})]}),o&&h("div",{className:"rounded-lg bg-blue-50 p-3",children:[h("p",{className:"text-blue-800 text-sm",children:[h("strong",{children:"Selected:"})," ",o.displayName]}),h("p",{className:"mt-1 text-blue-600 text-xs",children:["Session ID: ",o.sessionId.substring(0,8),"..."]})]}),!o&&e.length>0&&h("div",{className:"rounded-lg bg-amber-50 p-3",children:h("p",{className:"text-amber-800 text-sm",children:[h("strong",{children:"No window selected:"})," Please select an IDE window above to connect."]})})]})},mg=()=>h("div",{className:"space-y-2 text-xs text-zinc-700",children:[h("div",{className:"my-2 flex flex-wrap items-center gap-3",children:[h("a",{href:"https://github.com/stagewise-io/stagewise",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-blue-700 hover:underline",title:"GitHub Repository",children:[h("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:h("path",{d:"M12 .5C5.73.5.5 5.73.5 12c0 5.08 3.29 9.39 7.86 10.91.58.11.79-.25.79-.56 0-.28-.01-1.02-.02-2-3.2.7-3.88-1.54-3.88-1.54-.53-1.34-1.3-1.7-1.3-1.7-1.06-.72.08-.71.08-.71 1.17.08 1.78 1.2 1.78 1.2 1.04 1.78 2.73 1.27 3.4.97.11-.75.41-1.27.74-1.56-2.56-.29-5.26-1.28-5.26-5.7 0-1.26.45-2.29 1.19-3.1-.12-.29-.52-1.46.11-3.05 0 0 .98-.31 3.2 1.18a11.1 11.1 0 0 1 2.92-.39c.99 0 1.99.13 2.92.39 2.22-1.49 3.2-1.18 3.2-1.18.63 1.59.23 2.76.11 3.05.74.81 1.19 1.84 1.19 3.1 0 4.43-2.7 5.41-5.27 5.7.42.36.79 1.08.79 2.18 0 1.57-.01 2.84-.01 3.23 0 .31.21.68.8.56C20.71 21.39 24 17.08 24 12c0-6.27-5.23-11.5-12-11.5z"})}),"GitHub"]}),h("a",{href:"https://discord.gg/gkdGsDYaKA",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-indigo-700 hover:underline",title:"Join our Discord",children:[h("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:h("path",{d:"M20.317 4.369A19.791 19.791 0 0 0 16.885 3.2a.117.117 0 0 0-.124.06c-.537.96-1.13 2.22-1.552 3.2a18.524 18.524 0 0 0-5.418 0c-.423-.98-1.016-2.24-1.553-3.2a.117.117 0 0 0-.124-.06A19.736 19.736 0 0 0 3.683 4.369a.105.105 0 0 0-.047.043C.533 9.043-.32 13.579.099 18.057a.12.12 0 0 0 .045.083c1.934 1.426 3.81 2.288 5.671 2.857a.116.116 0 0 0 .127-.043c.438-.602.827-1.24 1.165-1.908a.112.112 0 0 0-.062-.158c-.619-.234-1.205-.52-1.77-.853a.117.117 0 0 1-.012-.194c.119-.09.238-.183.353-.277a.112.112 0 0 1 .114-.013c3.747 1.71 7.789 1.71 11.533 0a.112.112 0 0 1 .115.012c.115.094.234.188.353.278a.117.117 0 0 1-.012.194c-.565.333-1.151.619-1.77.853a.112.112 0 0 0-.062.158c.34.668.728 1.306 1.165 1.908a.115.115 0 0 0 .127.043c1.861-.569 3.737-1.431 5.671-2.857a.12.12 0 0 0 .045-.083c.5-5.177-.838-9.673-3.636-13.645a.105.105 0 0 0-.047-.043zM8.02 15.331c-1.183 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.175 1.095 2.156 2.418 0 1.334-.955 2.419-2.156 2.419zm7.96 0c-1.183 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.175 1.095 2.156 2.418 0 1.334-.946 2.419-2.156 2.419z"})}),"Discord"]}),h("a",{href:"https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-violet-700 hover:underline",title:"VS Code Marketplace",children:[h("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:h("path",{d:"M21.805 2.29a2.25 2.25 0 0 0-2.45-.49l-7.5 3.25a2.25 2.25 0 0 0-1.31 2.06v1.13l-5.13 2.22a2.25 2.25 0 0 0-1.31 2.06v3.5a2.25 2.25 0 0 0 1.31 2.06l5.13 2.22v1.13a2.25 2.25 0 0 0 1.31 2.06l7.5 3.25a2.25 2.25 0 0 0 2.45-.49A2.25 2.25 0 0 0 23 20.25V3.75a2.25 2.25 0 0 0-1.195-1.46zM12 20.25v-16.5l7.5 3.25v10l-7.5 3.25z"})}),"VS Code Marketplace"]})]}),h("div",{className:"mt-2",children:[h("span",{className:"font-semibold",children:"Contact:"})," ",h("a",{href:"mailto:<EMAIL>",className:"text-blue-700 hover:underline",children:"<EMAIL>"})]}),h("div",{className:"mt-2 text-zinc-500",children:h("span",{children:["Licensed under AGPL v3."," ",h("a",{href:"https://github.com/stagewise-io/stagewise/blob/main/LICENSE",target:"_blank",rel:"noopener noreferrer",className:"hover:underline",children:"View license"})]})})]});function fg({discover:e,discoveryError:t}){return h("div",{className:"rounded-lg border border-orange-200 bg-orange-50/90 p-4 shadow-lg backdrop-blur",children:[h("div",{className:"mb-3 flex items-center gap-3",children:[h(il,{className:"size-5 text-orange-600"}),h("h3",{className:"font-semibold text-orange-800",children:"Not Connected"})]}),h("div",{className:"space-y-3 text-orange-700 text-sm",children:[h("p",{children:"The stagewise toolbar isn't connected to any IDE window."}),t&&h("div",{className:"rounded border border-red-200 bg-red-100 p-2 text-red-700",children:[h("strong",{children:"Error:"})," ",t]}),h("div",{className:"space-y-2",children:[h("p",{className:"font-medium",children:"To connect:"}),h("ol",{className:"list-inside list-decimal space-y-1 pl-2 text-xs",children:[h("li",{children:"Open your IDE (Cursor, Windsurf, etc.)"}),h("li",{children:"Install the stagewise extension"}),h("li",{children:"Make sure the extension is active"}),h("li",{children:"Click refresh below"})]})]}),h("button",{type:"button",onClick:e,className:"flex w-full items-center justify-center gap-2 rounded-md bg-orange-600 px-3 py-2 font-medium text-sm text-white transition-colors hover:bg-orange-700",children:[h(bn,{className:"size-4"}),"Retry Connection"]}),h("div",{className:"border-orange-200 border-t pt-2",children:h("a",{href:"https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension",target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 text-xs hover:text-orange-800 hover:underline",children:"Get VS Code Extension →"})})]})]})}function gg(){return h("div",{className:"rounded-lg border border-blue-200 bg-blue-50/90 p-4 shadow-lg backdrop-blur",children:[h("div",{className:"mb-3 flex items-center gap-3",children:[h(bn,{className:"size-5 animate-spin text-blue-600"}),h("h3",{className:"font-semibold text-blue-800",children:"Connecting..."})]}),h("div",{className:"text-blue-700 text-sm",children:h("p",{children:["Looking for active agent instances...",h("br",{}),h("span",{className:"text-blue-500 text-xs",children:"VS Code, Cursor, Windsurf ..."})]})})]})}function vg(){const{windows:e,isDiscovering:t,discoveryError:n,discover:r,selectedSession:o,selectSession:a,appName:i}=Tt(),s=l=>{const u=l.target,d=u.value===""?void 0:u.value;a(d)},c=()=>{r()};return h("div",{className:"rounded-lg border border-blue-200 bg-blue-50/90 p-4 shadow-lg backdrop-blur",children:[h("div",{className:"mb-3",children:h("h3",{className:"font-semibold text-blue-800",children:"Select IDE Window"})}),h("div",{className:"space-y-3",children:[h("div",{children:[h("label",{htmlFor:"window-selection-select",className:"mb-2 block font-medium text-blue-700 text-sm",children:["IDE Window ",i&&`(${i})`]}),h("div",{className:"flex w-full items-center space-x-2",children:[h("select",{id:"window-selection-select",value:(o==null?void 0:o.sessionId)||"",onChange:s,className:"h-8 min-w-0 flex-1 rounded-lg border border-blue-300 bg-white/80 px-3 text-sm backdrop-saturate-150 focus:border-blue-500 focus:outline-none",disabled:t,children:[h("option",{value:"",disabled:!0,children:e.length>0?"Select an IDE window...":"No windows available"}),e.map(l=>h("option",{value:l.sessionId,children:[l.displayName," - Port ",l.port]},l.sessionId))]}),h("button",{type:"button",onClick:c,disabled:t,className:"flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-blue-100/80 backdrop-saturate-150 transition-colors hover:bg-blue-200/80 disabled:opacity-50",title:"Refresh window list",children:h(bn,{className:`size-4 text-blue-600 ${t?"animate-spin":""}`})})]}),n&&h("p",{className:"mt-1 text-red-600 text-sm",children:["Error discovering windows: ",n]}),!t&&e.length===0&&!n&&h("p",{className:"mt-1 text-blue-600 text-sm",children:"No IDE windows found. Make sure the Stagewise extension is installed and running."})]}),o&&h("div",{className:"rounded-lg bg-blue-100/80 p-3",children:[h("p",{className:"text-blue-800 text-sm",children:[h("strong",{children:"Selected:"})," ",o.displayName]}),h("p",{className:"mt-1 text-blue-600 text-xs",children:["Session ID: ",o.sessionId.substring(0,8),"..."]})]}),!o&&h("div",{className:"rounded-lg border border-blue-200 bg-white/90 p-3",children:h("p",{className:"text-blue-800 text-sm",children:[h("strong",{children:"No window selected:"})," Please select an IDE window above to connect."]})})]})]})}function wg({handleButtonClick:e,pluginBox:t,setPluginBox:n,openPanel:r,setOpenPanel:o,chatState:a}){const i=io().plugins.filter(s=>s.onActionClick);return h(je,{children:[h(ug,{onOpenPanel:()=>o(r==="settings"?null:"settings"),isActive:r==="settings"}),i.length>0&&h(or,{children:i.map(s=>h(hn,{onClick:e(()=>{(t==null?void 0:t.pluginName)!==s.pluginName?s.onActionClick()&&n({component:s.onActionClick(),pluginName:s.pluginName}):n(null)}),active:(t==null?void 0:t.pluginName)===s.pluginName,children:s.iconSvg?h("span",{className:"size-4 stroke-zinc-950 text-zinc-950 *:size-full",children:s.iconSvg}):h(Hf,{className:"size-4"})},s.pluginName))}),h(or,{children:h(hn,{onClick:e(()=>a.isPromptCreationActive?a.stopPromptCreation():a.startPromptCreation()),active:a.isPromptCreationActive,children:h(Ff,{className:"size-4 stroke-zinc-950"})})})]})}function bg(){const{discover:e,isDiscovering:t}=Tt();return h(or,{children:h(hn,{onClick:t?void 0:()=>e(),className:Se(t?"text-blue-700":"text-orange-700 hover:bg-orange-200"),children:h(bn,{className:Se("size-4",t&&"animate-spin")})})})}function yg(){const e=Pe(za),t=e==null?void 0:e.borderLocation,n=!!t&&t.right-t.left>0&&t.bottom-t.top>0,r=cg({startThreshold:10,initialSnapArea:"bottomRight"}),{windows:o,isDiscovering:a,discoveryError:i,discover:s,shouldPromptWindowSelection:c}=Tt(),l=o.length>0,[u,d]=ie(null),[p,m]=ie(null),v=hr(),{minimized:g,minimize:w,expand:b}=Ma();fe(()=>{g&&(d(null),m(null))},[g]);const y=Q=>Ce=>{if(r.wasDragged){Ce.preventDefault(),Ce.stopPropagation();return}Q()};if(!n)return null;const S=a,M=!l&&!a,O=l,re=c&&O,Z=S?{border:"border-blue-300",bg:"bg-blue-100/80",divideBorder:"divide-blue-200",buttonBg:"from-blue-600 to-sky-600",buttonColor:"text-blue-700"}:M?{border:"border-orange-300",bg:"bg-orange-100/80",divideBorder:"divide-orange-200",buttonBg:"from-orange-600 to-red-600",buttonColor:"text-orange-700"}:{border:"border-border/30",bg:"bg-zinc-50/80",divideBorder:"divide-border/20",buttonBg:"from-sky-700 to-fuchsia-700",buttonColor:"stroke-zinc-950"},I=()=>S?h(bn,{className:"size-4 animate-spin text-white"}):M?h(il,{className:"size-4 text-white"}):h(dg,{className:"size-4.5",color:"white"});return h("div",{ref:r.draggableRef,className:"absolute p-0.5",children:[h("div",{className:Se("absolute flex h-[calc(100vh-32px)] w-96 max-w-[40vw] items-stretch justify-end transition-all duration-300 ease-out",r.position.isTopHalf?"top-0 flex-col-reverse":"bottom-0 flex-col",r.position.isLeftHalf?"left-[100%]":"right-[100%]"),children:[h("div",{className:Se("flex min-h-0 flex-1 origin-bottom-right flex-col items-stretch px-2 transition-all duration-300 ease-out",(u||p==="settings"||!O||re)&&!g?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none h-0 scale-50 opacity-0 blur-md",r.position.isTopHalf?"justify-start":"justify-end",r.position.isTopHalf?r.position.isLeftHalf?"origin-top-left":"origin-top-right":r.position.isLeftHalf?"origin-bottom-left":"origin-bottom-right"),children:[S&&h(gg,{}),M&&h(fg,{discover:s,discoveryError:i}),re&&h(vg,{}),O&&p==="settings"&&!re&&h(pg,{onClose:()=>m(null)}),O&&!re&&(u==null?void 0:u.component)]}),O&&h("div",{className:Se("z-20 w-full px-2 transition-all duration-300 ease-out",v.isPromptCreationActive&&!g?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none h-0 scale-50 opacity-0 blur-md",r.position.isTopHalf?"mb-2":"mt-2",r.position.isTopHalf?r.position.isLeftHalf?"origin-top-left":"origin-top-right":r.position.isLeftHalf?"origin-bottom-left":"origin-bottom-right"),children:h(ig,{})})]}),h("div",{ref:r.handleRef,className:Se("pointer-events-auto z-50 rounded-full border px-0.5 shadow-md backdrop-blur transition-all duration-300 ease-out",Z.border,Z.bg,r.position.isTopHalf?"flex-col-reverse divide-y-reverse":"flex-col",g?"h-9.5 w-9.5":"h-[calc-size(auto,size)] h-auto w-auto"),children:[h(Pa,{onClick:()=>b(),className:Se("absolute right-0 left-0 z-50 flex size-9 origin-center cursor-pointer items-center justify-center rounded-full bg-gradient-to-tr transition-all duration-300 ease-out",Z.buttonBg,g?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none scale-25 opacity-0 blur-md",r.position.isTopHalf?"top-0":"bottom-0"),children:I()}),h("div",{className:Se("flex h-[calc-size(auto)] scale-100 items-center justify-center divide-y transition-all duration-300 ease-out",Z.divideBorder,r.position.isTopHalf?"origin-top flex-col-reverse divide-y-reverse":"origin-bottom flex-col",g&&"pointer-events-none h-0 scale-50 opacity-0 blur-md"),children:[O?h(wg,{handleButtonClick:y,pluginBox:u,setPluginBox:d,openPanel:p,setOpenPanel:m,chatState:v}):h(bg,{}),h(or,{children:h(hn,{onClick:y(()=>w()),className:Se("h-5",Z.buttonColor,r.position.isTopHalf?"rounded-t-3xl rounded-b-lg":"rounded-t-lg rounded-b-3xl"),children:r.position.isTopHalf?h(jf,{className:"size-4"}):h(zf,{className:"size-4"})})})]})]})]})}function _g(){const e=X(null);return h("div",{className:"absolute size-full",children:h("div",{className:"absolute inset-4",ref:e,children:h(sg,{containerRef:e,snapAreas:{topLeft:!0,topRight:!0,bottomLeft:!0,bottomRight:!0,topCenter:!0,bottomCenter:!0},children:h(yg,{})})})})}function xg(e){const t=X(null),n=P(a=>{if(a.target.closest(".companion"))return;const i=bm(a.clientX,a.clientY);e.ignoreList.includes(i)||t.current!==i&&(t.current=i,e.onElementHovered(i))},[e]),r=P(()=>{t.current=null,e.onElementUnhovered()},[e]),o=P(a=>{a.preventDefault(),a.stopPropagation(),t.current&&(e.ignoreList.includes(t.current)||e.onElementSelected(t.current))},[e]);return h("div",{className:"pointer-events-auto fixed inset-0 h-screen w-screen cursor-copy",onMouseMove:n,onMouseLeave:r,onClick:o,role:"button",tabIndex:0})}function cl(){const[e,t]=ie({width:window.innerWidth,height:window.innerHeight}),n=P(()=>t({width:window.innerWidth,height:window.innerHeight}),[]);return Gr("resize",n),e}function ll(e,t){const n=X(void 0),r=be(()=>1e3/t,[t]),o=X(0),a=P(i=>{i-o.current>=r&&(e(),o.current=i),n.current=requestAnimationFrame(a)},[e,r]);fe(()=>(n.current=requestAnimationFrame(a),()=>{n.current&&(cancelAnimationFrame(n.current),n.current=void 0)}),[t,a])}function kg({refElement:e,...t}){const n=X(null),r=cl(),{plugins:o}=io(),a=be(()=>e?o.filter(s=>s.onContextElementSelect).map(s=>{var c;return{pluginName:s.pluginName,context:(c=s.onContextElementSelect)==null?void 0:c.call(s,e)}}):[],[e]),i=P(()=>{if(n.current)if(e){const s=e.getBoundingClientRect();n.current.style.top=`${s.top-2}px`,n.current.style.left=`${s.left-2}px`,n.current.style.width=`${s.width+4}px`,n.current.style.height=`${s.height+4}px`,n.current.style.display=void 0}else n.current.style.height="0px",n.current.style.width="0px",n.current.style.top=`${r.height/2}px`,n.current.style.left=`${r.width/2}px`,n.current.style.display="none"},[e,r.height,r.width]);return ll(i,30),h("div",{...t,className:"fixed flex items-center justify-center rounded-lg border-2 border-blue-600/80 bg-blue-600/20 text-white transition-all duration-100",style:{zIndex:1e3},ref:n,children:[h("div",{className:"absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1",children:[h("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:h("span",{className:"truncate",children:e.tagName.toLowerCase()})}),a.filter(s=>s.context.annotation).map(s=>{var c;return h("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:[h("span",{className:"size-3 shrink-0 stroke-white text-white *:size-full",children:(c=o.find(l=>l.pluginName===s.pluginName))==null?void 0:c.iconSvg}),h("span",{className:"truncate",children:s.context.annotation})]})})]}),h(Bf,{className:"size-6 drop-shadow-black drop-shadow-md"})]})}function Sg({refElement:e,...t}){const n=X(null),r=cl(),o=P(()=>{if(n.current)if(e){const c=e.getBoundingClientRect();n.current.style.top=`${c.top}px`,n.current.style.left=`${c.left}px`,n.current.style.width=`${c.width}px`,n.current.style.height=`${c.height}px`,n.current.style.display=void 0}else n.current.style.height="0px",n.current.style.width="0px",n.current.style.top=`${r.height/2}px`,n.current.style.left=`${r.width/2}px`,n.current.style.display="none"},[e,r.height,r.width]);ll(o,30);const a=hr(),i=P(()=>{a.removeChatDomContext(a.currentChatId,e)},[a,e]),{plugins:s}=io();return h("div",{...t,className:"pointer-events-auto fixed flex cursor-pointer items-center justify-center rounded-lg border-2 border-green-600/80 bg-green-600/5 text-transparent transition-all duration-0 hover:border-red-600/80 hover:bg-red-600/20 hover:text-white",ref:n,onClick:i,role:"button",tabIndex:0,children:[h("div",{className:"absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1",children:[h("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:h("span",{className:"truncate",children:e.tagName.toLowerCase()})}),t.pluginContext.filter(c=>c.context.annotation).map(c=>{var l;return h("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:[h("span",{className:"size-3 shrink-0 stroke-white text-white *:size-full",children:(l=s.find(u=>u.pluginName===c.pluginName))==null?void 0:l.iconSvg}),h("span",{className:"truncate",children:c.context.annotation})]})})]}),h(Kf,{className:"size-6 drop-shadow-black drop-shadow-md"})]})}function Cg(){const{chats:e,currentChatId:t,addChatDomContext:n,isPromptCreationActive:r,promptState:o}=hr(),a=be(()=>e.find(d=>d.id===t),[t,e]),i=r&&o!=="loading",s=be(()=>(a==null?void 0:a.domContextElements)||[],[a]),[c,l]=ie(null),u=P(d=>{n(t,d)},[n,t]);return i?h(je,{children:[c&&h(kg,{refElement:c}),h(xg,{ignoreList:s.map(d=>d.element),onElementHovered:l,onElementSelected:u,onElementUnhovered:()=>l(null)}),s.map(d=>h(Sg,{refElement:d.element,pluginContext:d.pluginContext}))]}):null}function Eg(){return h("div",{className:Se("fixed inset-0 h-screen w-screen"),children:[h(Cg,{}),h(_g,{})]})}function Ng(){const e=X(!1);return fe(()=>{const t=HTMLElement.prototype.focus;return HTMLElement.prototype.focus=function(...n){const r=this.getRootNode();!(r instanceof ShadowRoot&&r.host instanceof HTMLElement&&r.host.nodeName==="STAGEWISE-COMPANION-ANCHOR")&&e.current||t.apply(this,n)},()=>{HTMLElement.prototype.focus=t}},[]),Gr("focusin",t=>{t.target.localName===rr&&(e.current=!0)},{capture:!0}),Gr("focusout",t=>{t.target.localName===rr&&(e.current=!1)},{capture:!0}),null}function Ag({children:e}){return e}function Mg(){const{isMainAppBlocked:e}=Ma();return h("div",{className:Se("fixed inset-0 h-screen w-screen",e?"pointer-events-auto":"pointer-events-none"),role:"button",tabIndex:0})}function Ig(e){return h(Am,{children:[h(Ng,{}),h(Mg,{}),h(Im,{config:e,children:[h($m,{}),h(Ag,{children:h(Eg,{})})]})]})}function $g(e){if(!document.body)throw new Error("stagewise companion cannot find document.body");if(document.body.querySelector(rr))throw console.warn("A stagewise companion anchor already exists. Aborting this instance."),new Error("A stagewise companion anchor already exists.");const t=document.createElement(rr);t.style.position="fixed",t.style.top="0px",t.style.left="0px",t.style.right="0px",t.style.bottom="0px",t.style.pointerEvents="none",t.style.zIndex="2147483647";const n=a=>{a.stopPropagation()};t.onclick=n,t.onmousedown=n,t.onmouseup=n,t.onmousemove=n,t.ondblclick=n,t.oncontextmenu=n,t.onwheel=n,t.onfocus=n,t.onblur=n,document.body.appendChild(t);const r=document.createElement("link");r.rel="stylesheet",r.href="https://rsms.me/inter/inter.css",document.head.appendChild(r);const o=document.createElement("style");o.append(document.createTextNode(xm)),document.head.appendChild(o),cn(Le(Ig,e),t)}function Pg({config:e,enabled:t=!1}){const n=L.useRef(!1);return L.useEffect(()=>{n.current||!t||(n.current=!0,$g(e))},[e,t]),null}function Tg(){return h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"-11.5 -10.23174 23 20.46348",children:[h("title",{children:"React Logo"}),h("circle",{cx:"0",cy:"0",r:"2.05",fill:"currentColor"}),h("g",{stroke:"currentColor","stroke-width":"1",fill:"none",children:[h("ellipse",{rx:"11",ry:"4.2"}),h("ellipse",{rx:"11",ry:"4.2",transform:"rotate(60)"}),h("ellipse",{rx:"11",ry:"4.2",transform:"rotate(120)"})]})]})}const Rg=0,zg=1,Og=5;function dl(e){var t,n;if(!e)return null;const r=[],o=3,a=Object.keys(e).find(s=>s.startsWith("__reactFiber$")||s.startsWith("__reactInternalInstance$"));if(!a)return null;let i=e[a];if(!i)return null;for(;i&&r.length<o;){let s=null;if(i.tag===zg||i.tag===Rg){const c=i.type;c&&(s={name:c.displayName||c.name||((t=i._debugOwner)==null?void 0:t.name)||"AnonymousComponent",type:"regular"})}else i.tag===Og&&i._debugOwner&&((n=i._debugOwner.env)!=null&&n.toLowerCase().includes("server"))&&(s={name:i._debugOwner.name,type:"rsc"});s&&(r.some(l=>l.name===s.name&&l.type===s.type)||r.push(s)),i=i.return}return r.length>0?r:null}function Ui(e){const t=dl(e);return t!=null&&t[0]?{annotation:`${t[0].name}${t[0].type==="rsc"?" (RSC)":""}`}:{annotation:null}}function jg(e){const t=e.map(n=>dl(n));return t.some(n=>n.length>0)?`This is additional information on the elements that the user selected. Use this information to find the correct element in the codebase.

  ${t.map((r,o)=>`
<element index="${o+1}">
  ${r.length===0?"No React component as parent detected":`React component tree (from closest to farthest, 3 closest elements): ${r.map(a=>`{name: ${a.name}, type: ${a.type}}`).join(" child of ")}`}
</element>
    `)}
  `:null}const Lg={displayName:"React",description:"This toolbar adds additional information and metadata for apps using React as a UI framework",iconSvg:h(Tg,{}),pluginName:"react",onContextElementHover:Ui,onContextElementSelect:Ui,onPromptSend:e=>{const t=jg(e.contextElements);return t?{contextSnippets:[{promptContextName:"elements-react-component-info",content:t}]}:{contextSnippets:[]}}};function Fg(){return f.jsxs("div",{className:"App",children:[f.jsx(Pg,{config:{plugins:[Lg]}}),f.jsx("a",{href:"#main-content",className:"skip-link",children:"Skip to main content"}),f.jsx(Ul,{}),f.jsxs("main",{id:"main-content",role:"main","aria-label":"Main content",children:[f.jsx(Xl,{}),f.jsx(Jl,{}),f.jsx(ed,{}),f.jsx(td,{}),f.jsx(Eu,{}),f.jsx(Nu,{}),f.jsx(Au,{})]}),f.jsx(Mu,{})]})}Eo.createRoot(document.getElementById("root")).render(f.jsx(ul.StrictMode,{children:f.jsx(Fg,{})}));
