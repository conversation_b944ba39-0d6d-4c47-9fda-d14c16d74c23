import React from 'react';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import NavigationIndicator from '../../components/ui/NavigationIndicator';
import ContactHero from './components/ContactHero';
import ContactForm from './components/ContactForm';
import ContactInfo from './components/ContactInfo';
import LocationMap from './components/LocationMap';
import AlternativeContact from './components/AlternativeContact';

const ContactPage = () => {
  const sections = [
    { id: 'hero', label: 'Hero' },
    { id: 'contact-form', label: 'Contact Form' },
    { id: 'contact-info', label: 'Contact Info' },
    { id: 'location', label: 'Location' },
    { id: 'alternatives', label: 'Other Options' }
  ];

  return (
    <div className="min-h-screen bg-surface">
      <Header />
      <NavigationIndicator sections={sections} />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section id="hero">
          <ContactHero />
        </section>

        {/* Breadcrumb */}
        <div className="max-w-7xl mx-auto px-6 lg:px-12 py-8">
          <Breadcrumb />
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 lg:px-12 pb-16">
          {/* Contact Form and Info Section */}
          <section id="contact-form" className="mb-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div>
                <ContactForm />
              </div>
              
              {/* Contact Information */}
              <div id="contact-info">
                <ContactInfo />
              </div>
            </div>
          </section>

          {/* Location and Alternative Contact Section */}
          <section id="location" className="mb-16">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Location Map */}
              <div>
                <LocationMap />
              </div>
              
              {/* Alternative Contact Methods */}
              <div id="alternatives">
                <AlternativeContact />
              </div>
            </div>
          </section>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="max-w-7xl mx-auto px-6 lg:px-12">
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="w-12 h-12 bg-accent rounded-lg flex items-center justify-center">
                <svg 
                  viewBox="0 0 24 24" 
                  className="w-6 h-6 text-accent-foreground"
                  fill="currentColor"
                >
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                  <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" fill="none"/>
                </svg>
              </div>
            </div>
            
            <h2 className="text-2xl font-semibold mb-4">
              Portfolio Pro
            </h2>
            
            <p className="text-primary-foreground/80 mb-8 max-w-2xl mx-auto leading-relaxed">
              Creating exceptional digital experiences through innovative design and development. 
              Let's build something amazing together.
            </p>
            
            <div className="border-t border-primary-foreground/20 pt-8">
              <p className="text-primary-foreground/60 text-sm">
                © {new Date().getFullYear()} Portfolio Pro. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ContactPage;