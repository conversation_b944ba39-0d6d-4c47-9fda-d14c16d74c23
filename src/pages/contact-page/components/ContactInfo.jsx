import React from 'react';
import Icon from '../../../components/AppIcon';

const ContactInfo = () => {
  const contactMethods = [
    {
      icon: 'Mail',
      label: 'Email',
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>',
      description: 'Best for detailed project discussions'
    },
    {
      icon: 'Phone',
      label: 'Phone',
      value: '+****************',
      href: 'tel:+15551234567',
      description: 'Available Mon-Fri, 9AM-6PM EST'
    },
    {
      icon: 'MapPin',
      label: 'Location',
      value: 'San Francisco, CA',
      href: null,
      description: 'Open to remote collaboration worldwide'
    }
  ];

  const socialLinks = [
    {
      icon: 'Github',
      label: 'GitHub',
      href: 'https://github.com/portfoliopro',
      username: '@portfoliopro'
    },
    {
      icon: 'Linkedin',
      label: 'LinkedIn',
      href: 'https://linkedin.com/in/portfoliopro',
      username: '/in/portfoliopro'
    },
    {
      icon: 'Twitter',
      label: 'Twitter',
      href: 'https://twitter.com/portfoliopro',
      username: '@portfoliopro'
    },
    {
      icon: 'Instagram',
      label: 'Instagram',
      href: 'https://instagram.com/portfoliopro',
      username: '@portfoliopro'
    }
  ];

  const stats = [
    {
      icon: 'Clock',
      label: 'Response Time',
      value: '< 24 hours',
      description: 'Average response time'
    },
    {
      icon: 'Users',
      label: 'Client Satisfaction',
      value: '98%',
      description: 'Based on 50+ projects'
    },
    {
      icon: 'Calendar',
      label: 'Availability',
      value: 'Open',
      description: 'Currently accepting new projects'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Contact Methods */}
      <div className="bg-background rounded-xl shadow-card p-8">
        <h2 className="text-2xl font-semibold text-primary mb-6">
          Get in Touch
        </h2>
        
        <div className="space-y-6">
          {contactMethods.map((method, index) => (
            <div key={index} className="flex items-start gap-4">
              <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Icon name={method.icon} size={20} className="text-accent" />
              </div>
              
              <div className="flex-1">
                <h3 className="font-medium text-text-primary mb-1">
                  {method.label}
                </h3>
                
                {method.href ? (
                  <a
                    href={method.href}
                    className="text-accent hover:text-accent/80 transition-smooth font-medium"
                  >
                    {method.value}
                  </a>
                ) : (
                  <p className="text-text-primary font-medium">
                    {method.value}
                  </p>
                )}
                
                <p className="text-sm text-text-secondary mt-1">
                  {method.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Social Links */}
      <div className="bg-background rounded-xl shadow-card p-8">
        <h2 className="text-2xl font-semibold text-primary mb-6">
          Connect on Social
        </h2>
        
        <div className="grid grid-cols-2 gap-4">
          {socialLinks.map((social, index) => (
            <a
              key={index}
              href={social.href}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-3 p-4 rounded-lg border border-border hover:border-accent/30 hover:bg-accent/5 transition-smooth group"
            >
              <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center group-hover:bg-accent/20 transition-smooth">
                <Icon name={social.icon} size={18} className="text-accent" />
              </div>
              
              <div className="flex-1 min-w-0">
                <p className="font-medium text-text-primary text-sm">
                  {social.label}
                </p>
                <p className="text-xs text-text-secondary truncate">
                  {social.username}
                </p>
              </div>
            </a>
          ))}
        </div>
      </div>

      {/* Stats */}
      <div className="bg-background rounded-xl shadow-card p-8">
        <h2 className="text-2xl font-semibold text-primary mb-6">
          Why Work With Me
        </h2>
        
        <div className="space-y-6">
          {stats.map((stat, index) => (
            <div key={index} className="flex items-center gap-4">
              <div className="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center">
                <Icon name={stat.icon} size={20} className="text-success" />
              </div>
              
              <div className="flex-1">
                <div className="flex items-baseline gap-2">
                  <h3 className="font-semibold text-text-primary">
                    {stat.label}
                  </h3>
                  <span className="text-lg font-bold text-success">
                    {stat.value}
                  </span>
                </div>
                <p className="text-sm text-text-secondary">
                  {stat.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContactInfo;