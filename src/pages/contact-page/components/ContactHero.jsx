import React from 'react';
import Icon from '../../../components/AppIcon';

const ContactHero = () => {
  return (
    <section className="bg-gradient-to-br from-primary to-secondary text-primary-foreground py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center">
              <Icon name="MessageCircle" size={32} className="text-accent-foreground" />
            </div>
          </div>
          
          <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
            Let's Work Together
          </h1>
          
          <p className="text-xl lg:text-2xl mb-8 opacity-90 leading-relaxed">
            Ready to bring your ideas to life? I'm here to help you create something amazing.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-lg">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span className="opacity-90">Available for new projects</span>
            </div>
            
            <div className="hidden sm:block w-px h-6 bg-primary-foreground/30"></div>
            
            <div className="flex items-center gap-3">
              <Icon name="Clock" size={20} className="opacity-80" />
              <span className="opacity-90">Response within 24 hours</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactHero;