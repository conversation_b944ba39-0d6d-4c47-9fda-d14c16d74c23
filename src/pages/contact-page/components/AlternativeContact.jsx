import React, { useState } from 'react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const AlternativeContact = () => {
  const [selectedTimeSlot, setSelectedTimeSlot] = useState('');
  const [isScheduling, setIsScheduling] = useState(false);
  const [isScheduled, setIsScheduled] = useState(false);

  const timeSlots = [
    { value: 'morning', label: 'Morning (9:00 AM - 12:00 PM PST)', available: true },
    { value: 'afternoon', label: 'Afternoon (1:00 PM - 5:00 PM PST)', available: true },
    { value: 'evening', label: 'Evening (6:00 PM - 8:00 PM PST)', available: false }
  ];

  const quickActions = [
    {
      icon: 'Download',
      title: 'Download Resume',
      description: 'Get my latest CV and portfolio summary',
      action: 'download',
      buttonText: 'Download PDF'
    },
    {
      icon: 'Calendar',
      title: 'Schedule a Call',
      description: 'Book a 30-minute consultation call',
      action: 'schedule',
      buttonText: 'Schedule Now'
    },
    {
      icon: 'MessageSquare',
      title: 'WhatsApp Chat',
      description: 'Quick questions and instant responses',
      action: 'whatsapp',
      buttonText: 'Start Chat'
    }
  ];

  const handleScheduleCall = async () => {
    if (!selectedTimeSlot) return;
    
    setIsScheduling(true);
    
    // Simulate scheduling process
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setIsScheduled(true);
    } catch (error) {
      console.error('Scheduling error:', error);
    } finally {
      setIsScheduling(false);
    }
  };

  const handleQuickAction = (action) => {
    switch (action) {
      case 'download':
        // Simulate file download
        const link = document.createElement('a');
        link.href = '/assets/resume.pdf'; // Mock file path
        link.download = 'PortfolioPro_Resume.pdf';
        link.click();
        break;
      case 'whatsapp':
        window.open('https://wa.me/15551234567?text=Hi! I found your portfolio and would like to discuss a project.', '_blank');
        break;
      default:
        break;
    }
  };

  return (
    <div className="space-y-8">
      {/* Quick Actions */}
      <div className="bg-background rounded-xl shadow-card p-8">
        <h2 className="text-2xl font-semibold text-primary mb-6">
          Quick Actions
        </h2>
        
        <div className="space-y-4">
          {quickActions.map((action, index) => (
            <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg hover:border-accent/30 hover:bg-accent/5 transition-smooth">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center">
                  <Icon name={action.icon} size={20} className="text-accent" />
                </div>
                
                <div>
                  <h3 className="font-medium text-text-primary mb-1">
                    {action.title}
                  </h3>
                  <p className="text-sm text-text-secondary">
                    {action.description}
                  </p>
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction(action.action)}
                iconName={action.icon}
                iconPosition="left"
              >
                {action.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </div>

      {/* Schedule Consultation */}
      <div className="bg-background rounded-xl shadow-card p-8">
        <div className="flex items-center gap-3 mb-6">
          <Icon name="Video" size={24} className="text-accent" />
          <h2 className="text-2xl font-semibold text-primary">
            Schedule a Consultation
          </h2>
        </div>
        
        {isScheduled ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="CheckCircle" size={32} className="text-success" />
            </div>
            <h3 className="text-xl font-semibold text-primary mb-2">
              Call Scheduled!
            </h3>
            <p className="text-text-secondary mb-4">
              I'll send you a calendar invite with the meeting details shortly.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setIsScheduled(false);
                setSelectedTimeSlot('');
              }}
            >
              Schedule Another Call
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            <p className="text-text-secondary leading-relaxed">
              Book a free 30-minute consultation to discuss your project requirements, 
              timeline, and how we can work together to achieve your goals.
            </p>
            
            <div>
              <h3 className="font-medium text-text-primary mb-3">
                Available Time Slots
              </h3>
              <div className="space-y-2">
                {timeSlots.map((slot) => (
                  <label
                    key={slot.value}
                    className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-smooth ${
                      slot.available
                        ? selectedTimeSlot === slot.value
                          ? 'border-accent bg-accent/5' :'border-border hover:border-accent/30' :'border-border bg-surface opacity-50 cursor-not-allowed'
                    }`}
                  >
                    <input
                      type="radio"
                      name="timeSlot"
                      value={slot.value}
                      checked={selectedTimeSlot === slot.value}
                      onChange={(e) => setSelectedTimeSlot(e.target.value)}
                      disabled={!slot.available}
                      className="w-4 h-4 text-accent"
                    />
                    <span className={`text-sm ${slot.available ? 'text-text-primary' : 'text-text-secondary'}`}>
                      {slot.label}
                    </span>
                    {!slot.available && (
                      <span className="ml-auto text-xs text-text-secondary">
                        Unavailable
                      </span>
                    )}
                  </label>
                ))}
              </div>
            </div>
            
            <Button
              variant="primary"
              size="lg"
              fullWidth
              onClick={handleScheduleCall}
              disabled={!selectedTimeSlot}
              loading={isScheduling}
              iconName="Calendar"
              iconPosition="left"
            >
              {isScheduling ? 'Scheduling...' : 'Schedule Consultation'}
            </Button>
          </div>
        )}
      </div>

      {/* FAQ */}
      <div className="bg-background rounded-xl shadow-card p-8">
        <h2 className="text-2xl font-semibold text-primary mb-6">
          Frequently Asked Questions
        </h2>
        
        <div className="space-y-4">
          {[
            {
              question: "What's your typical response time?",
              answer: "I respond to all inquiries within 24 hours, usually much sooner during business hours."
            },
            {
              question: "Do you work with international clients?",
              answer: "Absolutely! I work with clients worldwide and am comfortable with different time zones."
            },
            {
              question: "What information should I include in my message?",
              answer: "Please include your project goals, timeline, budget range, and any specific requirements or questions you have."
            }
          ].map((faq, index) => (
            <div key={index} className="border-b border-border last:border-b-0 pb-4 last:pb-0">
              <h3 className="font-medium text-text-primary mb-2">
                {faq.question}
              </h3>
              <p className="text-sm text-text-secondary leading-relaxed">
                {faq.answer}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AlternativeContact;