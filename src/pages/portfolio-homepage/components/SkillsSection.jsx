import React from 'react';
import Icon from '../../../components/AppIcon';

const SkillsSection = () => {
  const skillCategories = [
    {
      title: 'Frontend Development',
      icon: 'Monitor',
      skills: [
        { name: 'React', level: 95, icon: 'Code' },
        { name: 'JavaScript', level: 90, icon: 'FileCode' },
        { name: 'TypeScript', level: 85, icon: 'FileCode2' },
        { name: 'CSS/SCSS', level: 88, icon: 'Palette' }
      ]
    },
    {
      title: 'Backend Development',
      icon: 'Server',
      skills: [
        { name: 'Node.js', level: 90, icon: 'Zap' },
        { name: 'Python', level: 85, icon: 'Code2' },
        { name: 'Express.js', level: 88, icon: 'Globe' },
        { name: 'MongoDB', level: 82, icon: 'Database' }
      ]
    },
    {
      title: 'Tools & Technologies',
      icon: 'Wrench',
      skills: [
        { name: 'Git', level: 92, icon: 'GitBranch' },
        { name: 'Docker', level: 78, icon: 'Package' },
        { name: 'AWS', level: 75, icon: 'Cloud' },
        { name: 'Figma', level: 80, icon: 'Layers' }
      ]
    },
    {
      title: 'Soft Skills',
      icon: 'Users',
      skills: [
        { name: 'Problem Solving', level: 95, icon: 'Lightbulb' },
        { name: 'Team Leadership', level: 88, icon: 'Crown' },
        { name: 'Communication', level: 90, icon: 'MessageCircle' },
        { name: 'Project Management', level: 85, icon: 'Target' }
      ]
    }
  ];

  return (
    <section id="skills" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-16">
          <span className="text-accent font-semibold text-sm uppercase tracking-wide">Skills & Expertise</span>
          <h2 className="text-3xl md:text-4xl font-bold text-primary mt-2 mb-4">
            What I Bring to the Table
          </h2>
          <p className="text-text-secondary text-lg max-w-2xl mx-auto">
            A comprehensive skill set built through years of hands-on experience and continuous learning
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <div 
              key={categoryIndex}
              className="bg-surface rounded-lg p-6 card-shadow hover:shadow-interactive transition-smooth"
            >
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center mr-3">
                  <Icon name={category.icon} size={20} className="text-accent" />
                </div>
                <h3 className="text-lg font-semibold text-primary">{category.title}</h3>
              </div>
              
              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex} className="group">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <Icon name={skill.icon} size={16} className="text-text-secondary mr-2" />
                        <span className="text-text-primary font-medium text-sm">{skill.name}</span>
                      </div>
                      <span className="text-text-secondary text-xs">{skill.level}%</span>
                    </div>
                    
                    <div className="w-full bg-border rounded-full h-2">
                      <div 
                        className="bg-accent h-2 rounded-full transition-all duration-1000 ease-out group-hover:bg-accent/80"
                        style={{ width: `${skill.level}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        {/* Additional Skills Tags */}
        <div className="mt-16 text-center">
          <h3 className="text-xl font-semibold text-primary mb-6">Additional Technologies</h3>
          <div className="flex flex-wrap justify-center gap-3">
            {[
              'Redux', 'Next.js', 'Vue.js', 'GraphQL', 'PostgreSQL', 'Redis', 
              'Kubernetes', 'Jenkins', 'Webpack', 'Jest', 'Cypress', 'Tailwind CSS'
            ].map((tech, index) => (
              <span 
                key={index}
                className="px-4 py-2 bg-surface text-text-secondary rounded-full text-sm hover:bg-accent/10 hover:text-accent transition-smooth cursor-default"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkillsSection;