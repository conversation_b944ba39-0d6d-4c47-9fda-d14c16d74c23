import React from 'react';
import { Link } from 'react-router-dom';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const AboutPreview = () => {
  const stats = [
    { label: 'Years Experience', value: '5+', icon: 'Calendar' },
    { label: 'Projects Completed', value: '50+', icon: 'CheckCircle' },
    { label: 'Happy Clients', value: '30+', icon: 'Users' },
    { label: 'Technologies', value: '15+', icon: 'Code' }
  ];

  return (
    <section id="about-preview" className="py-20 bg-surface">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Content */}
          <div>
            <div className="mb-6">
              <span className="text-accent font-semibold text-sm uppercase tracking-wide">About Me</span>
              <h2 className="text-3xl md:text-4xl font-bold text-primary mt-2 mb-4">
                Passionate Developer with a Vision
              </h2>
            </div>
            
            <div className="space-y-4 text-text-secondary text-lg leading-relaxed mb-8">
              <p>
                With over 5 years of experience in full-stack development, I specialize in creating 
                scalable web applications that solve real-world problems. My journey began with a 
                curiosity for how things work, which evolved into a passion for building digital solutions.
              </p>
              
              <p>
                I believe in writing clean, maintainable code and staying updated with the latest 
                technologies. When I'm not coding, you'll find me contributing to open-source projects 
                or mentoring aspiring developers.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <Link to="/about-page">
                <Button 
                  variant="primary"
                  iconName="User"
                  iconPosition="right"
                  className="w-full sm:w-auto"
                >
                  Learn More About Me
                </Button>
              </Link>
              
              <a 
                href="/assets/resume.pdf" 
                download
                className="inline-block"
              >
                <Button 
                  variant="outline"
                  iconName="Download"
                  iconPosition="right"
                  className="w-full sm:w-auto"
                >
                  Download Resume
                </Button>
              </a>
            </div>
          </div>
          
          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-6">
            {stats.map((stat, index) => (
              <div 
                key={index}
                className="bg-background rounded-lg p-6 text-center card-shadow hover:shadow-interactive transition-smooth"
              >
                <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Icon name={stat.icon} size={24} className="text-accent" />
                </div>
                <div className="text-2xl font-bold text-primary mb-1">{stat.value}</div>
                <div className="text-text-secondary text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutPreview;