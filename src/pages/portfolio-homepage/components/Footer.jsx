import React from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { label: 'Home', path: '/portfolio-homepage' },
    { label: 'About', path: '/about-page' },
    { label: 'Projects', path: '/projects-portfolio' },
    { label: 'Blog', path: '/blog-articles-page' },
    { label: 'Contact', path: '/contact-page' }
  ];

  const socialLinks = [
    { name: 'GitHub', icon: 'Github', url: 'https://github.com/alexjohnson' },
    { name: 'LinkedIn', icon: 'Linkedin', url: 'https://linkedin.com/in/alexjohnson' },
    { name: 'Twitter', icon: 'Twitter', url: 'https://twitter.com/alexjohnson' },
    { name: 'Instagram', icon: 'Instagram', url: 'https://instagram.com/alexjohnson' },
    { name: 'Drib<PERSON>', icon: 'Dribbble', url: 'https://dribbble.com/alexjohnson' }
  ];

  const services = [
    'Web Development',
    'Mobile App Development',
    'UI/UX Design',
    'API Development',
    'Technical Consulting',
    'Code Review'
  ];

  return (
    <footer className="bg-primary text-primary-foreground">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-6 lg:px-12 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-accent rounded-md flex items-center justify-center">
                <svg 
                  viewBox="0 0 24 24" 
                  className="w-6 h-6 text-accent-foreground"
                  fill="currentColor"
                >
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                  <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" fill="none"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold">Portfolio Pro</h3>
            </div>
            
            <p className="text-primary-foreground/80 mb-6 leading-relaxed">
              Crafting exceptional digital experiences through innovative web development. 
              Let's build something amazing together.
            </p>
            
            <div className="flex space-x-3">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-primary-foreground/10 hover:bg-accent rounded-full flex items-center justify-center text-primary-foreground hover:text-accent-foreground transition-smooth"
                  aria-label={`Follow on ${social.name}`}
                >
                  <Icon name={social.icon} size={18} />
                </a>
              ))}
            </div>
          </div>
          
          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.path}>
                  <Link
                    to={link.path}
                    className="text-primary-foreground/80 hover:text-accent transition-smooth flex items-center group"
                  >
                    <Icon name="ArrowRight" size={14} className="mr-2 opacity-0 group-hover:opacity-100 transition-smooth" />
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Services</h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <span className="text-primary-foreground/80 flex items-center">
                    <Icon name="Check" size={14} className="mr-2 text-accent" />
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Contact Info & Newsletter */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Get In Touch</h4>
            
            <div className="space-y-4 mb-6">
              <div className="flex items-center">
                <Icon name="Mail" size={16} className="mr-3 text-accent" />
                <a 
                  href="mailto:<EMAIL>"
                  className="text-primary-foreground/80 hover:text-accent transition-smooth"
                >
                  <EMAIL>
                </a>
              </div>
              
              <div className="flex items-center">
                <Icon name="Phone" size={16} className="mr-3 text-accent" />
                <a 
                  href="tel:+1234567890"
                  className="text-primary-foreground/80 hover:text-accent transition-smooth"
                >
                  +****************
                </a>
              </div>
              
              <div className="flex items-center">
                <Icon name="MapPin" size={16} className="mr-3 text-accent" />
                <span className="text-primary-foreground/80">
                  San Francisco, CA
                </span>
              </div>
            </div>
            
            {/* Resume Download */}
            <a 
              href="/assets/resume.pdf" 
              download
              className="inline-block w-full"
            >
              <Button 
                variant="outline"
                iconName="Download"
                iconPosition="right"
                className="w-full border-primary-foreground/20 text-primary-foreground hover:bg-accent hover:text-accent-foreground hover:border-accent"
              >
                Download Resume
              </Button>
            </a>
          </div>
        </div>
      </div>
      
      {/* Bottom Bar */}
      <div className="border-t border-primary-foreground/10">
        <div className="max-w-7xl mx-auto px-6 lg:px-12 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-primary-foreground/60 text-sm mb-4 md:mb-0">
              © {currentYear} Alex Johnson. All rights reserved. Built with React & Tailwind CSS.
            </div>
            
            <div className="flex items-center space-x-6 text-sm">
              <a 
                href="/privacy-policy" 
                className="text-primary-foreground/60 hover:text-accent transition-smooth"
              >
                Privacy Policy
              </a>
              <a 
                href="/terms-of-service" 
                className="text-primary-foreground/60 hover:text-accent transition-smooth"
              >
                Terms of Service
              </a>
              <div className="flex items-center text-primary-foreground/60">
                <Icon name="Heart" size={14} className="mr-1 text-accent" />
                Made with passion
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;