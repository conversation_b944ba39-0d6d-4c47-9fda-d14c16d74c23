import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const BiographySection = () => {
  const [expandedSection, setExpandedSection] = useState(null);

  const biographySections = [
    {
      id: 'background',
      title: 'Professional Background',
      icon: 'Briefcase',
      content: `I'm a passionate full-stack developer with over 5 years of experience in creating innovative web applications and digital solutions. My journey began with a Computer Science degree from Stanford University, where I discovered my love for combining technical expertise with creative problem-solving.\n\nThroughout my career, I've had the privilege of working with startups, established companies, and everything in between. This diverse experience has taught me the importance of adaptability, clear communication, and delivering solutions that truly meet user needs.`
    },
    {
      id: 'philosophy',
      title: 'Work Philosophy',
      icon: 'Lightbulb',
      content: `I believe that great software is built on three pillars: clean code, user-centered design, and continuous learning. Every project I undertake starts with understanding the real problem we're trying to solve, not just the technical requirements.\n\nCollaboration is at the heart of my approach. I've found that the best solutions emerge when diverse perspectives come together, whether that's working with designers, product managers, or fellow developers. I'm always eager to learn from others and share my own insights.`
    },
    {
      id: 'interests',
      title: 'Personal Interests',
      icon: 'Heart',
      content: `When I'm not coding, you'll find me exploring the great outdoors through hiking and photography. I'm an avid reader of both technical blogs and science fiction novels, and I enjoy experimenting with new cooking techniques in my kitchen.\n\nI'm also passionate about mentoring junior developers and contributing to open-source projects. I believe in giving back to the community that has taught me so much throughout my career.`
    }
  ];

  const toggleSection = (sectionId) => {
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };

  return (
    <section id="biography" className="py-16 lg:py-20 bg-background">
      <div className="max-w-4xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            My Story
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            Get to know the person behind the code - my background, philosophy, and what drives me to create exceptional digital experiences.
          </p>
        </div>

        <div className="space-y-6">
          {biographySections.map((section) => (
            <div
              key={section.id}
              className="bg-surface rounded-xl border border-border overflow-hidden transition-smooth hover:shadow-card"
            >
              <button
                onClick={() => toggleSection(section.id)}
                className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-background/50 transition-smooth"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
                    <Icon name={section.icon} size={20} className="text-accent" />
                  </div>
                  <h3 className="text-lg font-semibold text-primary">
                    {section.title}
                  </h3>
                </div>
                <Icon 
                  name="ChevronDown" 
                  size={20} 
                  className={`text-text-secondary transition-transform duration-200 ${
                    expandedSection === section.id ? 'rotate-180' : ''
                  }`}
                />
              </button>
              
              {expandedSection === section.id && (
                <div className="px-6 pb-6">
                  <div className="pl-14">
                    {section.content.split('\n\n').map((paragraph, index) => (
                      <p key={index} className="text-text-secondary leading-relaxed mb-4 last:mb-0">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Core Values */}
        <div className="mt-16">
          <h3 className="text-xl font-semibold text-primary mb-8 text-center">
            Core Values
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                icon: 'Target',
                title: 'Quality First',
                description: 'Every line of code is written with purpose and precision'
              },
              {
                icon: 'Users',
                title: 'User-Centric',
                description: 'Solutions that truly serve the people who use them'
              },
              {
                icon: 'TrendingUp',
                title: 'Continuous Growth',
                description: 'Always learning, always improving, always evolving'
              }
            ].map((value, index) => (
              <div key={index} className="text-center p-6 bg-surface rounded-xl border border-border">
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Icon name={value.icon} size={24} className="text-accent" />
                </div>
                <h4 className="font-semibold text-primary mb-2">{value.title}</h4>
                <p className="text-sm text-text-secondary">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BiographySection;