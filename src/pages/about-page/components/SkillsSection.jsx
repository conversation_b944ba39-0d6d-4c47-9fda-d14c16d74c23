import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const SkillsSection = () => {
  const [activeCategory, setActiveCategory] = useState('technical');

  const skillCategories = {
    technical: {
      title: 'Technical Skills',
      icon: 'Code',
      skills: [
        { name: 'JavaScript', level: 95, category: 'Programming', years: '5+' },
        { name: 'React', level: 90, category: 'Frontend', years: '4+' },
        { name: 'Node.js', level: 85, category: 'Backend', years: '3+' },
        { name: 'TypeScript', level: 80, category: 'Programming', years: '2+' },
        { name: 'Python', level: 75, category: 'Programming', years: '3+' },
        { name: 'PostgreSQL', level: 85, category: 'Database', years: '4+' },
        { name: 'MongoDB', level: 80, category: 'Database', years: '3+' },
        { name: 'AWS', level: 70, category: 'Cloud', years: '2+' },
        { name: 'Docker', level: 75, category: 'DevOps', years: '2+' },
        { name: 'Git', level: 90, category: 'Tools', years: '5+' }
      ]
    },
    design: {
      title: 'Design & UX',
      icon: 'Palette',
      skills: [
        { name: 'UI/UX Design', level: 80, category: 'Design', years: '4+' },
        { name: 'Figma', level: 85, category: 'Tools', years: '3+' },
        { name: 'Adobe Creative Suite', level: 70, category: 'Tools', years: '2+' },
        { name: 'Responsive Design', level: 90, category: 'Frontend', years: '5+' },
        { name: 'Design Systems', level: 75, category: 'Design', years: '2+' },
        { name: 'Prototyping', level: 80, category: 'Design', years: '3+' }
      ]
    },
    soft: {
      title: 'Soft Skills',
      icon: 'Users',
      skills: [
        { name: 'Team Leadership', level: 85, category: 'Management', years: '3+' },
        { name: 'Project Management', level: 80, category: 'Management', years: '4+' },
        { name: 'Communication', level: 90, category: 'Interpersonal', years: '5+' },
        { name: 'Problem Solving', level: 95, category: 'Analytical', years: '5+' },
        { name: 'Mentoring', level: 80, category: 'Leadership', years: '2+' },
        { name: 'Agile/Scrum', level: 85, category: 'Methodology', years: '4+' }
      ]
    }
  };

  const certifications = [
    {
      name: 'AWS Certified Developer',
      issuer: 'Amazon Web Services',
      date: '2023',
      icon: 'Award',
      verified: true
    },
    {
      name: 'React Developer Certification',
      issuer: 'Meta',
      date: '2022',
      icon: 'Award',
      verified: true
    },
    {
      name: 'Scrum Master Certified',
      issuer: 'Scrum Alliance',
      date: '2021',
      icon: 'Award',
      verified: true
    }
  ];

  const getSkillColor = (level) => {
    if (level >= 90) return 'bg-success';
    if (level >= 80) return 'bg-accent';
    if (level >= 70) return 'bg-warning';
    return 'bg-text-secondary';
  };

  const getSkillTextColor = (level) => {
    if (level >= 90) return 'text-success';
    if (level >= 80) return 'text-accent';
    if (level >= 70) return 'text-warning';
    return 'text-text-secondary';
  };

  return (
    <section id="skills" className="py-16 lg:py-20 bg-background">
      <div className="max-w-6xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            Skills & Expertise
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            A comprehensive overview of my technical abilities, design skills, and professional competencies.
          </p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {Object.entries(skillCategories).map(([key, category]) => (
            <button
              key={key}
              onClick={() => setActiveCategory(key)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-smooth ${
                activeCategory === key
                  ? 'bg-accent text-accent-foreground'
                  : 'bg-surface text-text-secondary hover:text-text-primary hover:bg-background'
              }`}
            >
              <Icon name={category.icon} size={18} />
              <span>{category.title}</span>
            </button>
          ))}
        </div>

        {/* Skills Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16">
          {skillCategories[activeCategory].skills.map((skill, index) => (
            <div
              key={index}
              className="bg-surface rounded-xl p-6 border border-border hover:shadow-card transition-smooth"
            >
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="font-semibold text-primary">{skill.name}</h3>
                  <p className="text-sm text-text-secondary">
                    {skill.category} • {skill.years} experience
                  </p>
                </div>
                <div className={`text-lg font-bold ${getSkillTextColor(skill.level)}`}>
                  {skill.level}%
                </div>
              </div>
              
              <div className="w-full bg-border rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-1000 ${getSkillColor(skill.level)}`}
                  style={{ width: `${skill.level}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>

        {/* Certifications */}
        <div className="bg-surface rounded-2xl p-8 border border-border">
          <h3 className="text-xl font-semibold text-primary mb-6 text-center">
            Certifications & Achievements
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {certifications.map((cert, index) => (
              <div
                key={index}
                className="bg-background rounded-xl p-6 border border-border text-center hover:shadow-card transition-smooth"
              >
                <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Icon name={cert.icon} size={24} className="text-accent" />
                </div>
                
                <h4 className="font-semibold text-primary mb-2">{cert.name}</h4>
                <p className="text-sm text-text-secondary mb-2">{cert.issuer}</p>
                <p className="text-xs text-text-secondary mb-3">{cert.date}</p>
                
                {cert.verified && (
                  <div className="flex items-center justify-center space-x-1 text-success">
                    <Icon name="CheckCircle" size={14} />
                    <span className="text-xs font-medium">Verified</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Skills Summary */}
        <div className="mt-12 text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-surface rounded-xl p-6 border border-border">
              <div className="text-2xl font-bold text-accent mb-2">10+</div>
              <div className="text-sm text-text-secondary">Programming Languages</div>
            </div>
            <div className="bg-surface rounded-xl p-6 border border-border">
              <div className="text-2xl font-bold text-accent mb-2">15+</div>
              <div className="text-sm text-text-secondary">Frameworks & Libraries</div>
            </div>
            <div className="bg-surface rounded-xl p-6 border border-border">
              <div className="text-2xl font-bold text-accent mb-2">8+</div>
              <div className="text-sm text-text-secondary">Cloud Platforms</div>
            </div>
            <div className="bg-surface rounded-xl p-6 border border-border">
              <div className="text-2xl font-bold text-accent mb-2">20+</div>
              <div className="text-sm text-text-secondary">Development Tools</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkillsSection;