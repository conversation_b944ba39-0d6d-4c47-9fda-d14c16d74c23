import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';

const TimelineSection = () => {
  const [expandedEntry, setExpandedEntry] = useState(null);

  const timelineData = [
    {
      id: 1,
      period: '2023 - Present',
      role: 'Senior Full Stack Developer',
      company: 'TechCorp Solutions',
      companyLogo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop',
      location: 'San Francisco, CA',
      type: 'Full-time',
      achievements: [
        'Led development of microservices architecture serving 1M+ users',
        'Mentored 5 junior developers and established code review processes',
        'Reduced application load time by 40% through performance optimization',
        'Implemented CI/CD pipeline reducing deployment time by 60%'
      ],
      technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Docker'],
      description: `Leading a team of developers in building scalable web applications using modern technologies. Responsible for architectural decisions, code quality, and mentoring junior team members.`
    },
    {
      id: 2,
      period: '2021 - 2023',
      role: 'Full Stack Developer',
      company: 'StartupXYZ',
      companyLogo: 'https://images.unsplash.com/photo-1549923746-c502d488b3ea?w=100&h=100&fit=crop',
      location: 'Remote',
      type: 'Full-time',
      achievements: [
        'Built MVP from scratch that secured $2M in Series A funding',
        'Developed real-time chat system handling 10K+ concurrent users',
        'Integrated payment processing with 99.9% uptime',
        'Collaborated with design team to create intuitive user interfaces'
      ],
      technologies: ['Vue.js', 'Express.js', 'MongoDB', 'Socket.io', 'Stripe'],
      description: `Joined as the third engineer at a fast-growing startup. Wore multiple hats from frontend development to DevOps, helping scale the platform from 0 to 50,000 users.`
    },
    {
      id: 3,
      period: '2019 - 2021',
      role: 'Frontend Developer',
      company: 'Digital Agency Pro',
      companyLogo: 'https://images.unsplash.com/photo-1572021335469-31706a17aaef?w=100&h=100&fit=crop',
      location: 'New York, NY',
      type: 'Full-time',
      achievements: [
        'Delivered 25+ client projects with 100% on-time completion rate',
        'Specialized in React and modern JavaScript frameworks',
        'Improved client satisfaction scores by 35% through better UX',
        'Established responsive design standards for the agency'
      ],
      technologies: ['React', 'JavaScript', 'SASS', 'Webpack', 'Figma'],
      description: `Focused on creating beautiful, responsive user interfaces for various clients ranging from startups to Fortune 500 companies.`
    },
    {
      id: 4,
      period: '2018 - 2019',
      role: 'Junior Web Developer',
      company: 'WebDev Studios',
      companyLogo: 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=100&h=100&fit=crop',
      location: 'Austin, TX',
      type: 'Full-time',
      achievements: [
        'Completed comprehensive training in modern web technologies',
        'Contributed to 15+ WordPress and custom web projects',
        'Learned agile development methodologies and team collaboration',
        'Received "Rising Star" award for exceptional learning curve'
      ],
      technologies: ['HTML', 'CSS', 'JavaScript', 'WordPress', 'PHP'],
      description: `Started my professional journey learning the fundamentals of web development while contributing to real client projects.`
    }
  ];

  const toggleEntry = (entryId) => {
    setExpandedEntry(expandedEntry === entryId ? null : entryId);
  };

  return (
    <section id="timeline" className="py-16 lg:py-20 bg-surface">
      <div className="max-w-4xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-4">
            Career Journey
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            A timeline of my professional growth, key achievements, and the experiences that shaped my expertise.
          </p>
        </div>

        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-border hidden md:block"></div>

          <div className="space-y-8">
            {timelineData.map((entry, index) => (
              <div key={entry.id} className="relative">
                {/* Timeline Dot */}
                <div className="absolute left-6 w-4 h-4 bg-accent rounded-full border-4 border-background shadow-sm hidden md:block"></div>

                <div className="md:ml-16">
                  <div className="bg-background rounded-xl border border-border overflow-hidden shadow-card">
                    <button
                      onClick={() => toggleEntry(entry.id)}
                      className="w-full p-6 text-left hover:bg-surface/50 transition-smooth"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4 flex-1">
                          <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0 border border-border">
                            <Image
                              src={entry.companyLogo}
                              alt={`${entry.company} logo`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                              <h3 className="text-lg font-semibold text-primary">
                                {entry.role}
                              </h3>
                              <span className="text-sm font-medium text-accent bg-accent/10 px-3 py-1 rounded-full mt-1 sm:mt-0 self-start">
                                {entry.period}
                              </span>
                            </div>
                            
                            <div className="flex flex-col sm:flex-row sm:items-center text-text-secondary text-sm space-y-1 sm:space-y-0 sm:space-x-4">
                              <span className="font-medium">{entry.company}</span>
                              <span className="flex items-center">
                                <Icon name="MapPin" size={14} className="mr-1" />
                                {entry.location}
                              </span>
                              <span className="flex items-center">
                                <Icon name="Clock" size={14} className="mr-1" />
                                {entry.type}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <Icon 
                          name="ChevronDown" 
                          size={20} 
                          className={`text-text-secondary transition-transform duration-200 ml-4 flex-shrink-0 ${
                            expandedEntry === entry.id ? 'rotate-180' : ''
                          }`}
                        />
                      </div>
                    </button>

                    {expandedEntry === entry.id && (
                      <div className="px-6 pb-6 border-t border-border">
                        <div className="pt-6 space-y-6">
                          <p className="text-text-secondary leading-relaxed">
                            {entry.description}
                          </p>

                          <div>
                            <h4 className="font-semibold text-primary mb-3">Key Achievements</h4>
                            <ul className="space-y-2">
                              {entry.achievements.map((achievement, achievementIndex) => (
                                <li key={achievementIndex} className="flex items-start space-x-3">
                                  <Icon name="CheckCircle" size={16} className="text-success mt-0.5 flex-shrink-0" />
                                  <span className="text-text-secondary text-sm">{achievement}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h4 className="font-semibold text-primary mb-3">Technologies Used</h4>
                            <div className="flex flex-wrap gap-2">
                              {entry.technologies.map((tech, techIndex) => (
                                <span
                                  key={techIndex}
                                  className="px-3 py-1 bg-accent/10 text-accent text-sm rounded-full border border-accent/20"
                                >
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TimelineSection;