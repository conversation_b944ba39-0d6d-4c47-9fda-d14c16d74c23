import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import BlogHeader from './components/BlogHeader';
import CategoryFilter from './components/CategoryFilter';
import SearchBar from './components/SearchBar';
import SortOptions from './components/SortOptions';
import FeaturedArticle from './components/FeaturedArticle';
import ArticleCard from './components/ArticleCard';
import Sidebar from './components/Sidebar';
import Pagination from './components/Pagination';
import LoadingState from './components/LoadingState';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';



const BlogArticlesPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [currentPage, setCurrentPage] = useState(1);
  const articlesPerPage = 6;

  // Mock data
  const categories = [
    { id: 'tutorials', name: 'Tutorials', icon: 'BookOpen', count: 12 },
    { id: 'insights', name: 'Industry Insights', icon: 'TrendingUp', count: 8 },
    { id: 'retrospectives', name: 'Project Retrospectives', icon: 'RotateCcw', count: 6 },
    { id: 'career', name: 'Career Advice', icon: 'Briefcase', count: 10 },
    { id: 'technology', name: 'Technology', icon: 'Code', count: 15 },
    { id: 'design', name: 'Design', icon: 'Palette', count: 7 },
  ];

  const allArticles = [
    {
      id: 1,
      title: "Building Scalable React Applications with Modern Architecture",
      excerpt: `Learn how to structure large-scale React applications using modern patterns and best practices.\nExplore component composition, state management strategies, and performance optimization techniques that will help you build maintainable and scalable applications.`,
      category: 'Tutorials',
      categoryId: 'tutorials',
      date: 'Dec 15, 2024',
      readTime: '8 min read',
      image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop',
      comments: 24,
      likes: 156,
      views: '2.1k',
      author: 'Alex Johnson'
    },
    {
      id: 2,
      title: "The Future of Web Development: Trends to Watch in 2025",
      excerpt: `Discover the emerging technologies and methodologies that will shape web development in the coming year.\nFrom AI-powered development tools to new frameworks and performance optimization techniques.`,
      category: 'Industry Insights',
      categoryId: 'insights',
      date: 'Dec 12, 2024',
      readTime: '6 min read',
      image: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=600&h=400&fit=crop',
      comments: 18,
      likes: 203,
      views: '3.2k',
      author: 'Alex Johnson'
    },
    {
      id: 3,
      title: "Lessons Learned from Building a Real-Time Chat Application",
      excerpt: `A deep dive into the challenges and solutions encountered while developing a production-ready chat system.\nExplore WebSocket implementation, message queuing, and scaling strategies for real-time applications.`,
      category: 'Project Retrospectives',
      categoryId: 'retrospectives',
      date: 'Dec 10, 2024',
      readTime: '12 min read',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop',
      comments: 31,
      likes: 189,
      views: '1.8k',
      author: 'Alex Johnson'
    },
    {
      id: 4,
      title: "Mastering CSS Grid: Advanced Layout Techniques",
      excerpt: `Go beyond the basics of CSS Grid and learn advanced techniques for creating complex, responsive layouts.\nDiscover grid areas, subgrid, and how to combine Grid with Flexbox for optimal results.`,
      category: 'Tutorials',
      categoryId: 'tutorials',
      date: 'Dec 8, 2024',
      readTime: '10 min read',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop',
      comments: 15,
      likes: 142,
      views: '1.5k',
      author: 'Alex Johnson'
    },
    {
      id: 5,
      title: "From Junior to Senior: A Developer\'s Growth Journey",
      excerpt: `Practical advice and insights on advancing your career in software development.\nLearn about technical skills, soft skills, and mindset shifts that define senior developers.`,
      category: 'Career Advice',
      categoryId: 'career',
      date: 'Dec 5, 2024',
      readTime: '7 min read',
      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop',
      comments: 42,
      likes: 278,
      views: '4.1k',
      author: 'Alex Johnson'
    },
    {
      id: 6,
      title: "Optimizing React Performance: A Complete Guide",
      excerpt: `Comprehensive strategies for improving React application performance and user experience.\nCover memo, useMemo, useCallback, code splitting, and advanced optimization techniques.`,
      category: 'Technology',
      categoryId: 'technology',
      date: 'Dec 3, 2024',
      readTime: '15 min read',
      image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=600&h=400&fit=crop',
      comments: 28,
      likes: 195,
      views: '2.7k',
      author: 'Alex Johnson'
    },
    {
      id: 7,
      title: "Design Systems: Building Consistent User Interfaces",
      excerpt: `Learn how to create and maintain design systems that scale across multiple products and teams.\nExplore component libraries, design tokens, and documentation strategies.`,
      category: 'Design',
      categoryId: 'design',
      date: 'Nov 30, 2024',
      readTime: '9 min read',
      image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600&h=400&fit=crop',
      comments: 19,
      likes: 167,
      views: '1.9k',
      author: 'Alex Johnson'
    },
    {
      id: 8,
      title: "API Design Best Practices for Modern Applications",
      excerpt: `Essential guidelines for designing robust, scalable, and developer-friendly APIs.\nCover REST principles, GraphQL considerations, authentication, and documentation strategies.`,
      category: 'Technology',
      categoryId: 'technology',
      date: 'Nov 28, 2024',
      readTime: '11 min read',
      image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=600&h=400&fit=crop',
      comments: 22,
      likes: 134,
      views: '1.6k',
      author: 'Alex Johnson'
    }
  ];

  const popularArticles = [
    {
      id: 5,
      title: "From Junior to Senior: A Developer\'s Growth Journey",
      date: 'Dec 5, 2024',
      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=100&h=100&fit=crop',
      views: '4.1k'
    },
    {
      id: 2,
      title: "The Future of Web Development: Trends to Watch in 2025",
      date: 'Dec 12, 2024',
      image: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=100&h=100&fit=crop',
      views: '3.2k'
    },
    {
      id: 6,
      title: "Optimizing React Performance: A Complete Guide",
      date: 'Dec 3, 2024',
      image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=100&h=100&fit=crop',
      views: '2.7k'
    },
    {
      id: 1,
      title: "Building Scalable React Applications with Modern Architecture",
      date: 'Dec 15, 2024',
      image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop',
      views: '2.1k'
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const handleSubscribe = (email) => {
    console.log('Subscribed:', email);
    // Handle subscription logic here
  };

  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handleCategoryChange = (categoryId) => {
    setActiveCategory(categoryId);
    setCurrentPage(1);
  };

  const handleSortChange = (sortOption) => {
    setSortBy(sortOption);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Filter and sort articles
  const filteredArticles = allArticles.filter(article => {
    const matchesSearch = !searchTerm || 
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = activeCategory === 'all' || article.categoryId === activeCategory;
    
    return matchesSearch && matchesCategory;
  });

  const sortedArticles = [...filteredArticles].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.likes - a.likes;
      case 'alphabetical':
        return a.title.localeCompare(b.title);
      case 'newest':
      default:
        return new Date(b.date) - new Date(a.date);
    }
  });

  // Pagination
  const totalPages = Math.ceil(sortedArticles.length / articlesPerPage);
  const startIndex = (currentPage - 1) * articlesPerPage;
  const paginatedArticles = sortedArticles.slice(startIndex, startIndex + articlesPerPage);

  // Featured article (most popular)
  const featuredArticle = allArticles.reduce((prev, current) => 
    (prev.likes > current.likes) ? prev : current
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-surface">
        <Helmet>
          <title>Blog & Articles - Portfolio Pro</title>
          <meta name="description" content="Explore insights, tutorials, and articles on web development, technology, and career growth." />
        </Helmet>
        <Header />
        <main className="pt-20">
          <BlogHeader onSubscribe={handleSubscribe} />
          <div className="max-w-7xl mx-auto px-6 lg:px-12 py-12">
            <LoadingState />
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-surface">
      <Helmet>
        <title>Blog & Articles - Portfolio Pro</title>
        <meta name="description" content="Explore insights, tutorials, and articles on web development, technology, and career growth. Stay updated with the latest trends and best practices." />
        <meta name="keywords" content="blog, articles, web development, tutorials, technology, career advice, programming" />
      </Helmet>

      <Header />
      
      <main className="pt-20">
        <BlogHeader onSubscribe={handleSubscribe} />
        
        <div className="max-w-7xl mx-auto px-6 lg:px-12 py-12">
          <Breadcrumb />
          
          <div className="grid lg:grid-cols-4 gap-8">
            <div className="lg:col-span-3">
              {/* Search and Filters */}
              <div className="mb-8">
                <SearchBar onSearch={handleSearch} searchTerm={searchTerm} />
                <CategoryFilter 
                  categories={categories}
                  activeCategory={activeCategory}
                  onCategoryChange={handleCategoryChange}
                />
                <SortOptions sortBy={sortBy} onSortChange={handleSortChange} />
              </div>

              {/* Featured Article */}
              {!searchTerm && activeCategory === 'all' && (
                <FeaturedArticle article={featuredArticle} />
              )}

              {/* Results Summary */}
              <div className="mb-6">
                <p className="text-text-secondary">
                  {searchTerm || activeCategory !== 'all' ? (
                    <>
                      Showing {paginatedArticles.length} of {filteredArticles.length} articles
                      {searchTerm && ` for "${searchTerm}"`}
                      {activeCategory !== 'all' && ` in ${categories.find(cat => cat.id === activeCategory)?.name}`}
                    </>
                  ) : (
                    `${allArticles.length} articles available`
                  )}
                </p>
              </div>

              {/* Articles Grid */}
              {paginatedArticles.length > 0 ? (
                <>
                  <div className="grid md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    {paginatedArticles.map((article) => (
                      <ArticleCard 
                        key={article.id} 
                        article={article} 
                        searchTerm={searchTerm}
                      />
                    ))}
                  </div>

                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-surface rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon name="Search" size={32} className="text-text-secondary" />
                  </div>
                  <h3 className="text-lg font-semibold text-primary mb-2">No articles found</h3>
                  <p className="text-text-secondary mb-6">
                    Try adjusting your search terms or filters to find what you're looking for.
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setSearchTerm('');
                      setActiveCategory('all');
                      setCurrentPage(1);
                    }}
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24">
                <Sidebar 
                  popularArticles={popularArticles}
                  categories={categories}
                />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default BlogArticlesPage;