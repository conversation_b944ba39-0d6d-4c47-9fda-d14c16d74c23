import React, { useState, useEffect, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import FilterSection from './components/FilterSection';
import SearchAndSort from './components/SearchAndSort';
import ProjectGrid from './components/ProjectGrid';
import ProjectPreviewModal from './components/ProjectPreviewModal';

const ProjectsPortfolio = () => {
  // Mock Projects Data
  const mockProjects = [
    {
      id: 1,
      title: "E-Commerce Platform",
      description: "A full-featured e-commerce platform with user authentication, payment integration, and admin dashboard. Built with modern web technologies for optimal performance.",
      fullDescription: `A comprehensive e-commerce solution featuring user authentication, shopping cart functionality, secure payment processing, order management, and an intuitive admin dashboard. The platform includes advanced features like product search, filtering, wishlist management, and real-time inventory tracking.\n\nThe application is built with performance and scalability in mind, utilizing modern web technologies and best practices for optimal user experience across all devices.`,
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop",
      category: "Web Application",
      technologies: ["React", "Node.js", "MongoDB", "Stripe", "Redux"],
      year: "2024",
      duration: "4 months",
      role: "Full Stack Developer",
      teamSize: "3 developers",
      liveUrl: "https://demo-ecommerce.com",
      githubUrl: "https://github.com/demo/ecommerce"
    },
    {
      id: 2,
      title: "Task Management App",
      description: "A collaborative task management application with real-time updates, team collaboration features, and advanced project tracking capabilities.",
      fullDescription: `A powerful task management solution designed for teams and individuals to organize, track, and collaborate on projects efficiently. Features include real-time collaboration, drag-and-drop task organization, deadline tracking, file attachments, and comprehensive reporting.\n\nThe application supports multiple project views including Kanban boards, Gantt charts, and calendar views, making it suitable for various project management methodologies.`,
      image: "https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?w=800&h=600&fit=crop",
      category: "Mobile App",
      technologies: ["React Native", "Firebase", "TypeScript", "Redux Toolkit"],
      year: "2024",
      duration: "3 months",
      role: "Mobile Developer",
      teamSize: "2 developers",
      liveUrl: "https://demo-taskapp.com"
    },
    {
      id: 3,
      title: "Data Visualization Dashboard",
      description: "An interactive dashboard for data visualization with real-time charts, customizable widgets, and comprehensive analytics reporting.",
      fullDescription: `A sophisticated data visualization platform that transforms complex datasets into intuitive, interactive charts and graphs. The dashboard features real-time data updates, customizable widgets, advanced filtering options, and export capabilities.\n\nBuilt for business intelligence and analytics teams, it supports multiple data sources and provides insights through various visualization types including bar charts, line graphs, heat maps, and geographic visualizations.`,
      image: "https://images.pixabay.com/photo/2016/11/27/21/42/stock-1863880_1280.jpg?w=800&h=600&fit=crop",
      category: "Data Science",
      technologies: ["React", "D3.js", "Python", "PostgreSQL", "Docker"],
      year: "2023",
      duration: "5 months",
      role: "Frontend Developer",
      teamSize: "4 developers",
      liveUrl: "https://demo-dashboard.com",
      githubUrl: "https://github.com/demo/dashboard"
    },
    {
      id: 4,
      title: "Social Media Platform",
      description: "A modern social media platform with real-time messaging, content sharing, and advanced privacy controls for enhanced user experience.",
      fullDescription: `A comprehensive social media platform featuring real-time messaging, photo and video sharing, story functionality, and advanced privacy controls. The platform includes features like user profiles, friend connections, content feeds, and notification systems.\n\nBuilt with scalability and user privacy in mind, the platform supports millions of users with real-time interactions and content delivery optimization.`,
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop",
      category: "Web Application",
      technologies: ["Vue.js", "Express.js", "Socket.io", "Redis", "AWS"],
      year: "2023",
      duration: "6 months",
      role: "Full Stack Developer",
      teamSize: "5 developers",
      liveUrl: "https://demo-social.com"
    },
    {
      id: 5,
      title: "AI-Powered Chatbot",
      description: "An intelligent chatbot system with natural language processing, machine learning capabilities, and multi-platform integration.",
      fullDescription: `An advanced AI-powered chatbot system that leverages natural language processing and machine learning to provide intelligent, context-aware responses. The system includes sentiment analysis, intent recognition, and continuous learning capabilities.\n\nIntegrated across multiple platforms including web, mobile, and messaging apps, the chatbot provides 24/7 customer support with human-like interactions and seamless escalation to human agents when needed.`,
      image: "https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg?w=800&h=600&fit=crop",
      category: "AI/ML",
      technologies: ["Python", "TensorFlow", "Flask", "NLP", "Docker"],
      year: "2023",
      duration: "4 months",
      role: "AI Developer",
      teamSize: "3 developers"
    },
    {
      id: 6,
      title: "Fitness Tracking App",
      description: "A comprehensive fitness tracking application with workout planning, progress monitoring, and social features for fitness enthusiasts.",
      fullDescription: `A complete fitness tracking solution that helps users monitor their workouts, track progress, and achieve their fitness goals. Features include workout planning, exercise libraries, progress analytics, nutrition tracking, and social challenges.\n\nThe app integrates with wearable devices and provides personalized recommendations based on user activity patterns and fitness goals.`,
      image: "https://images.pixabay.com/photo/2017/08/07/14/02/people-2604149_1280.jpg?w=800&h=600&fit=crop",
      category: "Mobile App",
      technologies: ["Flutter", "Dart", "Firebase", "HealthKit", "Google Fit"],
      year: "2024",
      duration: "3 months",
      role: "Mobile Developer",
      teamSize: "2 developers",
      liveUrl: "https://demo-fitness.com"
    }
  ];

  // State Management
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTechnologies, setSelectedTechnologies] = useState([]);
  const [sortBy, setSortBy] = useState('newest');
  const [isLoading, setIsLoading] = useState(false);
  const [previewProject, setPreviewProject] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Extract unique categories and technologies
  const categories = useMemo(() => {
    return [...new Set(mockProjects.map(project => project.category))];
  }, []);

  const technologies = useMemo(() => {
    const allTechs = mockProjects.flatMap(project => project.technologies);
    return [...new Set(allTechs)].sort();
  }, []);

  // Filter and sort projects
  const filteredProjects = useMemo(() => {
    let filtered = mockProjects;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.technologies.some(tech => tech.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Apply technology filter
    if (selectedTechnologies.length > 0) {
      filtered = filtered.filter(project =>
        selectedTechnologies.some(tech => project.technologies.includes(tech))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return parseInt(b.year) - parseInt(a.year);
        case 'oldest':
          return parseInt(a.year) - parseInt(b.year);
        case 'alphabetical':
          return a.title.localeCompare(b.title);
        case 'popularity':
          return (b.liveUrl ? 1 : 0) - (a.liveUrl ? 1 : 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [searchQuery, selectedCategory, selectedTechnologies, sortBy]);

  // Calculate active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (selectedCategory !== 'all') count++;
    if (selectedTechnologies.length > 0) count += selectedTechnologies.length;
    return count;
  }, [selectedCategory, selectedTechnologies]);

  // Handle filter changes
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  const handleTechnologyChange = (technologies) => {
    setSelectedTechnologies(technologies);
  };

  const handleClearFilters = () => {
    setSelectedCategory('all');
    setSelectedTechnologies([]);
    setSearchQuery('');
  };

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handleSortChange = (sort) => {
    setSortBy(sort);
  };

  const handleProjectPreview = (project) => {
    setPreviewProject(project);
    setIsPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewOpen(false);
    setPreviewProject(null);
  };

  // Simulate loading state
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [selectedCategory, selectedTechnologies, searchQuery, sortBy]);

  return (
    <>
      <Helmet>
        <title>Projects Portfolio - Portfolio Pro</title>
        <meta name="description" content="Explore my professional projects and work samples. Browse through web applications, mobile apps, and data science projects with detailed case studies." />
        <meta name="keywords" content="portfolio, projects, web development, mobile apps, data science, React, Node.js" />
      </Helmet>

      <div className="min-h-screen bg-background">
        <Header />
        
        <main className="pt-20 lg:pt-24">
          <div className="flex">
            {/* Desktop Filter Sidebar */}
            <FilterSection
              categories={categories}
              technologies={technologies}
              selectedCategory={selectedCategory}
              selectedTechnologies={selectedTechnologies}
              onCategoryChange={handleCategoryChange}
              onTechnologyChange={handleTechnologyChange}
              onClearFilters={handleClearFilters}
              activeFiltersCount={activeFiltersCount}
            />

            {/* Main Content */}
            <div className="flex-1 lg:pl-0">
              <div className="max-w-7xl mx-auto px-6 lg:px-12 py-8 lg:py-12">
                <Breadcrumb />
                
                {/* Page Header */}
                <div className="mb-8 lg:mb-12">
                  <h1 className="text-3xl lg:text-4xl font-bold text-text-primary mb-4">
                    My Projects
                  </h1>
                  <p className="text-lg text-text-secondary max-w-3xl">
                    Explore my professional work and personal projects. Each project represents a unique challenge 
                    and showcases different aspects of my technical expertise and problem-solving abilities.
                  </p>
                </div>

                {/* Mobile Filter Section */}
                <div className="lg:hidden">
                  <FilterSection
                    categories={categories}
                    technologies={technologies}
                    selectedCategory={selectedCategory}
                    selectedTechnologies={selectedTechnologies}
                    onCategoryChange={handleCategoryChange}
                    onTechnologyChange={handleTechnologyChange}
                    onClearFilters={handleClearFilters}
                    activeFiltersCount={activeFiltersCount}
                  />
                </div>

                {/* Search and Sort */}
                <SearchAndSort
                  searchQuery={searchQuery}
                  onSearchChange={handleSearchChange}
                  sortBy={sortBy}
                  onSortChange={handleSortChange}
                  resultsCount={filteredProjects.length}
                />

                {/* Projects Grid */}
                <ProjectGrid
                  projects={filteredProjects}
                  onProjectPreview={handleProjectPreview}
                  isLoading={isLoading}
                />
              </div>
            </div>
          </div>
        </main>

        {/* Project Preview Modal */}
        <ProjectPreviewModal
          project={previewProject}
          isOpen={isPreviewOpen}
          onClose={handleClosePreview}
        />
      </div>
    </>
  );
};

export default ProjectsPortfolio;