{"$schema": "https://raw.githubusercontent.com/jsonresume/resume-schema/v1.0.0/schema.json", "basics": {"name": "<PERSON>", "label": "Principal Technical Consultant | Cloud and Platform Engineering", "image": "", "url": "https://www.linkedin.com/in/nicholas-gerasimatos", "summary": "Specializing in innovative cloud and platform solutions, I align technology with client goals to drive impactful results. With deep expertise in open-source and cloud technologies, I communicate complex systems in a clear, accessible manner and prioritize a client-centered approach. I bring a proven track record in designing and implementing scalable, secure, resilient solutions, leading modernization initiatives, and developing actionable architectures that achieve measurable business outcomes.", "location": {"countryCode": "US", "address": "United States"}, "profiles": [{"network": "LinkedIn", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://www.linkedin.com/in/ngerasimatos/"}]}, "work": [{"name": "AHEAD", "position": "Principal - Platform Engineering and Modernization", "startDate": "2024-05-31", "endDate": "2024-12-31", "highlights": [], "summary": "Emerging Technologies Expertise:\n\nMaintain a leading-edge understanding of emerging technologies, with a particular focus on artificial intelligence, and machine learning. Implement AI-driven solutions that streamline operations, and unlock new business value.\n\nAdvanced Solution Development:\n\nArchitect, design, and implement cutting-edge solutions leveraging open-source frameworks and cloud-native technologies, including Infrastructure as Code (IaC), Platform as a Service (PaaS), and Infrastructure as a Service (IaaS). Integrate advanced AI/ML and AI capabilities to enhance system intelligence, automate workflows, and drive improvements in performance, scalability, and cost efficiency.\n\nCloud and Hybrid Architecture Optimization:\n\nOptimize enterprise cloud and hybrid infrastructures by applying distributed computing principles and best practices. Conduct in-depth architectural assessments to identify bottlenecks, reduce latency, and maximize system uptime. Deliver robust, resilient solutions that support critical business operations and ensure seamless scalability for large-scale enterprise clients.\n\nEffective Technical Communication:\n\nTranslate complex technical concepts into clear, actionable insights for both technical and non-technical stakeholders. Develop and deliver compelling presentations and technical documentation to secure executive buy-in, expedite project approvals, and accelerate delivery timelines. \n\nLeadership in Cross-Functional Delivery:\n\nLead diverse, cross-functional teams through the full project lifecycle, from initial scoping to final delivery. Utilize agile methodologies and GitOps practices to drive collaboration, transparency, and continuous improvement.\n\nAdvocacy for Best Practices:\n\nActively champion industry best practices in cloud architecture and software delivery. Mentor teams, develop and disseminate technical guidelines, and establish governance frameworks to elevate the quality and consistency of project outcomes.", "url": "https://www.linkedin.com/company/ahead_2/"}, {"name": "Amazon Web Services (AWS)", "position": "Partner Cloud Architect", "startDate": "2022-12-31", "endDate": "2024-05-31", "highlights": [], "summary": "Cloud Solution Architecture and Optimization:\n\nDesign, implement, and optimize scalable, secure, and cost-effective AWS cloud solutions with a focus on resiliency, high availability, performance, and cost efficiency. Leverage ROSA/OpenShift, AWS Bedrock, and various other AWS services to deliver intelligent, automated solutions that drive business value.\n\nCross-Functional Collaboration:\n\nCollaborate with partners, customers, and cross-functional teams to understand diverse requirements and translate them into robust cloud architectures.\n\nComplex Cloud Infrastructure Deployment:\n\nArchitect and deploy sophisticated cloud infrastructures tailored to client needs, ensuring seamless integration of AI/ML and generative AI solutions for improved performance and automation.\n\nTraining and Enablement Leadership:\n\nLead training sessions and workshops for internal teams and clients, focusing on Red Hat, Open-Source, and AWS architecture best practices, security compliance, and the adoption of emerging technologies. \n\nPartner and Community Engagement:\n\nConduct internal and partner training, participate in industry events, and engage in global webinars and roadshows to showcase innovative AWS and partner solutions. \n\nTechnical Curriculum Development:\n\nBuild and implement comprehensive education programs for technical teams, including Immersion Days, Game Days, workshops, and technical documentation that highlight AWS services and partner integrations.\n\nSales Enablement and Competitive Positioning:\n\nCreate concise battle cards to showcase partner solutions differentiators and value. Develop playbooks outlining synergies between partner offerings and AWS services. \n\nCustomer Advocacy and Success Story Development:\n\nCollect and curate customer references and success stories, including co-branded content and showcases of GSI/ISV-developed solutions that demonstrate the impact of AWS and partner driven cloud transformations.", "url": "https://www.linkedin.com/company/amazon-web-services/"}, {"name": "Red Hat", "position": " Emerging Technologies Cloud Engineer ", "startDate": "2015-12-31", "endDate": "2022-12-31", "highlights": [], "summary": "Strategic Cloud Modernization and Digital Transformation:\n\nLead modernization and digital transformation initiatives by driving strategic cloud adoption. Unlock new business opportunities and deliver measurable business benefits through the integration of emerging technologies, blockchain, edge computing, OpenShift, AI, ML, and serverless architectures. Enable organizations to become more agile and future-ready.\n\nStakeholder Engagement and Consensus Building:\n\nClearly articulate the value of cloud solutions and emerging technologies to a wide range of stakeholders. Address concerns, answer technical and business questions, and build consensus to ensure buy-in from executive, technical, and operational teams.\n\nSeamless Transition to Cloud-Centric Operating Models:\n\nFacilitate smooth transitions to cloud-centric operating models by ensuring effective integration of advanced technologies such as GenAI, and AI/ML into existing and new workflows. Oversee the adoption of hybrid and multi-cloud strategies to maximize flexibility and support ongoing innovation.\n\nCloud Readiness Assessments and Migration Strategy:\n\nDevelop and implement tailored migration strategies and multiphase roadmaps that align short-term wins with long-term business objectives. Ensure each migration is smooth and cost-effective.\n\nThought Leadership and Industry Influence:\n\nEstablish thought leadership by publishing technical articles, delivering presentations at industry conferences, and actively participating in cloud and emerging technology communities. Share insights on digital transformation, cloud adoption, and practical applications of AI, and ML. \n\nCollaboration and Innovation Culture:\n\nBuild strong partnerships with internal teams, cross-functional stakeholders, and technology vendors to drive continuous innovation. Foster a culture of learning and collaboration that encourages exploration and adoption of emerging technologies in cloud-based solutions.", "url": "https://www.linkedin.com/company/red-hat/"}, {"name": "FICO", "position": "Director of Cloud Service Platforms", "startDate": "2013-12-31", "endDate": "2015-12-31", "highlights": [], "summary": "Strategic Cloud Platform Planning and Implementation:\n\nLead the strategic planning, design, and implementation of cloud service platforms for organizations of varying scale and complexity. Align cloud solutions with business needs to ensure robust, scalable, and secure platforms that support growth and innovation.\n\nDigital Transformation and Service Delivery:\n\nDrive digital transformation initiatives by optimizing infrastructure performance and ensuring seamless delivery of cloud services to both internal and external stakeholders. Focus on enhancing agility, reliability, and user experience across the organization.\n\nComprehensive Cloud Strategy Development:\n\nDevelop and execute comprehensive cloud strategies that align with organizational objectives. Lead cross-functional teams to deliver cloud services that meet business goals, enhance operational efficiency, and foster a culture of innovation.\n\nExpertise in Cloud Service Models:\n\nDemonstrate deep expertise in infrastructure-as-a-service (IaaS), platform-as-a-service (PaaS), and software-as-a-service (SaaS) models. Select and implement effective service models to meet diverse business requirements.\n\nTeam Leadership and Talent Development:\n\nFoster a high-performance culture by building collaborative teams, recruiting top talent, and providing mentorship and professional development opportunities. Empower team members to excel in cloud technologies and deliver outstanding results.\n\nSecurity, Compliance, and Data Protection:\nEnsure compliance with industry standards and regulations by establishing robust security controls and data protection measures. Proactively manage risk and safeguard organizational data across all cloud environments.\n\nVendor and Partner Relationship Management:\nEstablish and maintain strong relationships with cloud service providers, vendors, and partners. Manage service level agreements (SLAs), and drive value-added partnerships that support organizational goals and deliver long-term value.", "url": "https://www.linkedin.com/company/amazon-web-services"}, {"name": "American Express", "position": "Senior Data Architect", "startDate": "2010-12-31", "endDate": "2014-12-31", "highlights": [], "summary": "Designed and implemented scalable, performant, and secure data solutions driving data-driven insights and business value\n\nApplied data architecture principles and a wide range of technologies to solve complex business challenges\n\nCollaborated with cross-functional teams to translate diverse requirements into robust, actionable data solutions aligned with strategic organizational goals\n\nCrafted conceptual, logical, and physical data models ensuring data integrity, accessibility, and optimal organization for diverse analytical workloads\n\nAligned data architectures with enterprise-wide data governance standards and incorporated long-term scalability considerations\n\nDesigned big data environments (Hadoop, Spark, etc.) enabling acquisition, transformation, and analysis of high-volume, high-velocity, and high-variety datasets\n\nArchitected solutions empowering advanced analytics, machine learning initiatives, and real-time decision-making\n\nDemonstrated expertise in data privacy regulations, security frameworks, and access control methodologies\n\nExtensive experience with cloud-native or hybrid data solutions (AWS, Azure, GCP) optimizing cost and performance through strategic selection of cloud services and technologies\n\nIdentified root causes of data-related bottlenecks and inefficiencies, proposing innovative solutions balancing immediate needs with long-term sustainability", "url": "https://www.linkedin.com/company/american-express/"}, {"name": "VCE", "position": "Principal Architect", "startDate": "2009-12-31", "endDate": "2012-12-31", "highlights": [], "summary": "Led the design and implementation of complex and innovative solutions, collaborating with cross-functional teams and stakeholders to align technology initiatives with business goals, enhance performance, and drive organizational growth.\n\nDemonstrated expertise in architecting scalable, reliable, and secure solutions across a wide range of domains and technologies, with a deep understanding of architectural patterns, best practices, and industry standards.\n\nDeveloped technology roadmaps and long-term architectural visions, leading and mentoring teams to foster a culture of innovation and drive initiatives that deliver value to the organization.\n\nPossessed comprehensive knowledge of various technologies, frameworks, and platforms across cloud computing, distributed systems, microservices, and enterprise architecture, leveraging emerging technologies to drive business innovation and competitive advantage.\n\nDesigned and implemented large-scale, distributed, and highly available systems, integrating disparate systems and ensuring interoperability, with proficiency in scalability, performance optimization, and capacity planning.\n\nDemonstrated excellent communication and interpersonal skills, engaging and influencing stakeholders at all levels of the organization, building relationships, fostering collaboration, and presenting complex technical concepts in a clear and concise manner.", "url": "https://www.linkedin.com/company/vce/"}, {"name": "Microsoft", "position": "Senior System Engineer", "startDate": "2006-12-31", "endDate": "2009-12-31", "highlights": [], "summary": "Designed, implemented, and managed the Microsoft CORE software development infrastructure platform\n\nLeveraged Microsoft and open-source technologies to optimize system performance, enhance security, and streamline operations\n\nDrove successful projects and collaborated with cross-functional teams to deliver innovative solutions aligned with business objectives\n\nDemonstrated deep knowledge and hands-on experience with Microsoft technologies such as Windows Server, Active Directory, Microsoft Exchange, Microsoft 365, Azure, and PowerShell\n\nProficient in designing, configuring, and managing Microsoft-based systems and services   Architected and designed scalable and resilient systems based on Microsoft technologies\n\nSkilled in capacity planning, performance tuning, and optimizing infrastructure for maximum efficiency and availability\n\nIntegrated on-premises systems with Microsoft Azure and proficient in hybrid cloud design, migration strategies, and managing cloud-based infrastructure\n\nStrong understanding of Microsoft security technologies and best practices, including Identity and Access Management, Active Directory security, and data protection\n\nImplemented security controls and ensured compliance with industry standards\n\nDemonstrated exceptional analytical and problem-solving skills to diagnose and resolve", "url": "https://www.linkedin.com/company/microsoft/", "location": "Greater Seattle Area"}], "volunteer": [{"organization": "HUMANE SOCIETY", "position": "Volunteer", "startDate": "2010-01-01", "endDate": "2015-12-31", "summary": "Volunteer work supporting animal welfare and community outreach programs.", "highlights": [], "url": "https://www.linkedin.com/company/9539229"}], "awards": [{"title": "RED HAT HONORS OUTSTANDING ACHIEVEMENTS IN OPEN SOURCE WITH NINTH ANNUAL RED HAT INNOVATION AWARDS", "date": "2015-10-26", "awarder": "Red Hat", "summary": "Recognizing striking flexibility, scalability, cost effectiveness, performance, and security within an infrastructure.\n\nWinner: FICO\nLeading analytics software company FICO helps businesses in more than 90 countries make better decisions that drive higher levels of growth, profitability and customer satisfaction. FICO wanted to extend its successful, high-end analytics business into new industries and markets. To capitalize on the growing demand for Big Data analytics among companies of all sizes, the company sought to complement its on-premise solution with a web-based service. Using OpenShift Enterprise, the company developed FICO® Analytic Cloud. The new Platform-as-a-Service (PaaS) offering has driven more than US$10 million in sales in a short time frame, winning business from companies that otherwise couldn't have afforded the time or cost of implementing FICO’s sophisticated analytics solutions. Using the Red Hat technology, FICO reduced infrastructure operations staffing by 20 percent, saved hundreds of thousands of dollars in hardware costs, and improved time to market by 70 percent."}], "certificates": [{"name": "Certified to architect, deploy and implement Vblock 100/200/300/700 infrastructure systems", "issuer": "VCE"}, {"name": "VMware Certified Professional - Data Center Virtualization", "issuer": "VMware"}, {"name": "VMware Sales Professional, Application Modernization, Data Management, Business Continuity, Virtualization of Business Critical Applications, Management, Cloud IaaS, Desktop Virtualization", "issuer": "VMware"}, {"name": "VMware Technical Sales Professional - Business Continuity, Virtualization of Business Critical Applications, Data Management,  Infrastructure Virtualization", "issuer": "VMware"}, {"name": "VMware Certified Associate - Data Center Virtualization, Cloud, Workforce Mobility", "issuer": "VMware"}, {"name": "Red Hat Certificate of Expertise in Platform-as-a-Service", "issuer": "Red Hat"}, {"name": "Red Hat Certificate of Expertise in Data Virtualization", "issuer": "Red Hat"}, {"name": "Red Hat Certificate of Expertise in Clustering and Storage Management", "issuer": "Red Hat"}, {"name": "AWS Certified Solutions Architect - Associate", "issuer": "Amazon Web Services (AWS)", "endDate": "2025-02-28", "startDate": "2023-02-28", "url": "https://www.credly.com/badges/3f30bd86-6157-4c5d-9ff5-5b47f38cdb07/public_url"}, {"name": "Google Cloud - Introduction to Generative AI", "issuer": "Google", "startDate": "2023-06-30", "url": "https://cdn.qwiklabs.com/%2FTntrCzBhpKkF9LHUgvevvIKQb2%2Bufpupa1zPSlY%2Fcs%3D"}], "publications": [{"name": "DELL-EMC MERGER LEAVES IT PROS HOPEFUL AND CONCERNED", "publisher": "Search Storage", "releaseDate": "2015-12-31", "summary": "<PERSON>, director of cloud service and engineering at Fair Isaac Corp. (FICO), sees the merger as a way for Dell to \"finally get enterprise-grade storage\" because \"Compellent was not really enterprise-ready.\" He also said the deal is also good for VMware to \"hopefully allow them to spin and eliminate the influence of EMC and increase their innovation.\"\n\nBut Gerasimatos doesn't see the merger having an impact on FICO's purchasing plans. He said the company is shifting to OpenStack, Ceph software-defined storage and public cloud options to try to move away from VMware Enterprise, vCloud Director and EMC's VMAX, VNX and Avamar within two to three years. He said the main impediment is waiting for hardware depreciation.\n\n\"The ROI is not there\" with VMware/EMC products, Gerasimat<PERSON> wrote in an e-mail. He added that FICO staff prefers OpenStack options because they can participate in the software evolution and modify source code to fit their specific needs. \"Also, EMC and VMware have a very poor OpenStack story that is less than impressive,\" he added.\n\nFICO uses SolidFire all-flash arrays for workloads that need high performance or replication and Cisco's UCS as its hardware standard. Only legacy environments use EMC and NetApp, according to <PERSON><PERSON><PERSON><PERSON><PERSON>. ", "url": "http://searchstorage.techtarget.com/news/4500255416/Dell-EMC-merger-leaves-IT-pros-hopeful-and-concerned"}, {"name": "FICO CHOOSES RED HAT TO DEPLOY OPENSTACK, <PERSON>NAGEMENT, AND STORAGE SOLUTIONS FOR AGIL<PERSON> CLOUD INFRASTRUCTURE", "publisher": "Market Watch", "releaseDate": "2015-12-31", "summary": "<PERSON>, Cloud Development Director, FICO \n“With Red Hat's OpenStack on Cisco UCS and Ceph, we've been able to create an elastic scalable infrastructure that delivers all of the benefits of cloud – speed of innovation, agility, the ability to deliver Software-as-a-Service – but with the ability to securely manage our resources in a private datacenter. This gives us the cloud platform we need to create FICO's offerings, but with control of our data, workloads, compliance and security.”", "url": "http://www.marketwatch.com/story/fico-chooses-red-hat-to-deploy-openstack-management-and-storage-solutions-for-agile-cloud-infrastructure-2015-10-26"}, {"name": "WHAT’S BEHIND THE ODD COUPLE MICROSOFT-RED HAT PARTNERSHIP", "publisher": "Network World", "releaseDate": "2015-12-31", "summary": "Red Hat customers seemed to embrace the news too. “I think it’s a big win for both companies but a bigger win for Red Hat since Microsoft is now ‘all in’ with their distribution and technologies,” says <PERSON>, director of cloud services engineering at FICO, a big Red Hat user.", "url": "http://www.networkworld.com/article/3001391/microsoft-subnet/what-s-behind-the-odd-couple-microsoft-red-hat-partnership.html"}, {"name": "WILL O<PERSON><PERSON> SOURCE STORAGE MAKE THE HYPER SCALE DREAM REAL?", "publisher": "The Register", "releaseDate": "2015-12-31", "summary": "Open-source software has become an important presence in many areas of IT, and now, as storage increasingly becomes software-defined storage, it is storage's turn. The darling of the open-source storage movement – though it is by no means the only viable and popular option – is Ceph.\n\nA unified storage platform originally developed for a PhD dissertation in California, Ceph has become one of the most popular, if not THE most popular, storage layers for OpenStack deployments, and with OpenStack leading the cloud computing charge, Ceph is benefiting considerably.", "url": "http://www.theregister.co.uk/2015/11/09/open_source_hyperscale_storage/?mt=1447100084309"}, {"name": "FICO EMBRACES OPENSTACK | #OPENSTACK", "publisher": "SiliconANGLE", "releaseDate": "2015-08-21", "summary": "FICO’s agressive adoption model\n\n“We adopted it aggressively,” said <PERSON><PERSON><PERSON><PERSON><PERSON> when asked how FICO made the transition to OpenStack. FICO’s timeframe from zero to a fully functioning solution was just 12 months.\n“What we needed to do was to become a little more agile as we were going global, so that pushed us to go toward the more OpenStack design,” he said. <PERSON><PERSON><PERSON><PERSON><PERSON> credits FICO’s close relationship with Red Hat, Inc. as being mutually beneficial.\n‘Don’t be afraid; embrace it!’\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> sees the benefits of OpenStack as the low point of entry, scalability, software-defined networking and storage without having to pay the penalties. He lists problems encountered during the transition, including how the FICO operations team struggled to get their heads around the distributed scale-out design, as well as difficulties finding qualified engineers with open-source experience. But despite the challenges getting up and going, <PERSON><PERSON><PERSON><PERSON><PERSON> said that FICO is very happy with open source and encourages others to follow in the company’s footsteps.\n\n“Everyone is learning as they go along,” he stated. “Every major company, even including Microsoft, is embracing containers and the new scale-out design architecture. I would say don’t be afraid; embrace it!”", "url": "http://siliconangle.com/blog/2015/08/21/fico-embraces-openstack-openstack/"}, {"name": "FICO PROVES THE MAINSTREAM OPENSTACK ADOPTION POINT", "publisher": "Forbes", "releaseDate": "2015-06-09", "summary": "Analysis of FICO's successful OpenStack adoption as a mainstream enterprise use case, demonstrating the maturity and viability of OpenStack for large-scale production environments.", "url": "http://www.forbes.com/sites/benkepes/2015/06/09/fico-proves-the-mainstream-openstack-adoption-point/"}, {"name": "FICO SAYS <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ENTERPRISE IS READY FOR PRIMETIME", "publisher": "SearchCloudComputing", "releaseDate": "2015-05-01", "summary": "FICO, an analytics software company in San Jose, Calif., uses Red Hat to deploy its OpenStack private cloud as opposed to one of the larger public cloud options because it's a more distributed architecture with a higher number of availability zones internationally, said <PERSON>, director of engineering and cloud services.\n\nThe workloads have been in production for 90 days and everything has gone smoothly so far, but there are still issues with some of the projects not maturing fast enough, Gerasimatos said.\n\n\"The extensibility of Neutron, it's more for flat networks and it's not great for large scalable infrastructure,\" G<PERSON><PERSON><PERSON><PERSON> said.  \"A lot of that is supposedly changing in Kilo but we would have liked to have seen that a few versions ago.\"", "url": "http://searchcloudcomputing.techtarget.com/news/4500246812/OpenStack-enterprise-users-say-its-ready-for-primetime"}, {"name": "HOW <PERSON>OR<PERSON><PERSON> WORKS IN CONTAINERS", "publisher": "OpenStack Superuser", "releaseDate": "2015-04-01", "summary": "<PERSON>, senior director of cloud services engineering at FICO, dives into the lack of persistent storage with containers and how Docker volumes and data containers provide a fix.", "url": "http://superuser.openstack.org/articles/how-storage-works-in-containers?awesm=awe.sm_jOEqS"}, {"name": "HOW TO BUILD A LARGE SCALE MULTI-TENANT CLOUD SOLUTION", "publisher": "LinkedIn", "releaseDate": "2015-03-01", "summary": "Its not terribly difficult to design and build a turnkey integrated configured SDDC ready to use solution. \nHowever building one that completely abstracts the compute, storage and network physical resources and provides multiple tenants a pool of logical resources along with all the necessary management, operational and application level services and allows to scale resources with seamless addition of new rack units. ", "url": "https://www.linkedin.com/pulse/how-build-large-scale-multi-tenant-cloud-solution-gerasimatos?trk=prof-post"}, {"name": "TH<PERSON><PERSON> FICO IS A CREDIT SCORING COMPANY? NOPE: IT'S ABOUT LARGE-SCALE ANALYTICS", "publisher": "OpenStack Superuser", "releaseDate": "2015-02-01", "summary": "“We’re always known as a credit-scoring company, but we’re a actually large-scale analytics company,” says <PERSON>, director of engineering and cloud services at FICO. While 90 percent of all lending decisions in the U.S. currently rely on FICO scores, in 2013 the company launched the FICO analytic cloud for creating, customizing and deploying analytic-driven applications and services. “There are a lot of financial and government institutions that we integrate with and we spend a lot of time deploying in different countries.”\n\nIn just the last 12-18 months, he says that the company has tripled in size and number of deployments thanks to OpenStack — reaching Australia, Turkey South Africa, China, Japan in addition to spanning the United States.", "url": "http://superuser.openstack.org/articles/think-fico-is-a-credit-scoring-company-nope-it-s-about-large-scale-analytics"}], "skills": [{"name": "OpenStack", "level": "", "keywords": []}, {"name": "Cloud Computing IaaS", "level": "", "keywords": []}, {"name": "Continuous Integration and Continuous Delivery (CI/CD)", "level": "", "keywords": []}, {"name": "GitOps", "level": "", "keywords": []}, {"name": "Business Ownership", "level": "", "keywords": []}, {"name": "Containers", "level": "", "keywords": []}, {"name": "OpenShift", "level": "", "keywords": []}, {"name": "Amazon EKS", "level": "", "keywords": []}, {"name": "<PERSON><PERSON>", "level": "", "keywords": []}, {"name": "<PERSON>er", "level": "", "keywords": []}, {"name": "Hybrid Cloud", "level": "", "keywords": []}, {"name": "Red Hat Linux", "level": "", "keywords": []}, {"name": "VMware", "level": "", "keywords": []}, {"name": "Amazon Web Services (AWS)", "level": "", "keywords": []}, {"name": "Private Cloud", "level": "", "keywords": []}, {"name": "Converged Infrastructure", "level": "", "keywords": []}, {"name": "Cloud Development", "level": "", "keywords": []}, {"name": "Research", "level": "", "keywords": []}, {"name": "Defining Requirements", "level": "", "keywords": []}, {"name": "Cloud Security", "level": "", "keywords": []}, {"name": "Kubernetes", "level": "", "keywords": []}, {"name": "Cloud Applications", "level": "", "keywords": []}, {"name": "Engineering", "level": "", "keywords": []}, {"name": "Red Hat Enterprise Linux (RHEL)", "level": "", "keywords": []}, {"name": "Requirements Gathering", "level": "", "keywords": []}, {"name": "Large Language Models (LLM)", "level": "", "keywords": []}, {"name": "Open Systems Architecture", "level": "", "keywords": []}, {"name": "Amazon Bedrock", "level": "", "keywords": []}, {"name": "Distributed Systems", "level": "", "keywords": []}, {"name": "Systems Engineering", "level": "", "keywords": []}, {"name": "Professional Services", "level": "", "keywords": []}, {"name": "Virtualization", "level": "", "keywords": []}, {"name": "Windows Server", "level": "", "keywords": []}, {"name": "Management", "level": "", "keywords": []}, {"name": "Software Defined Storage", "level": "", "keywords": []}, {"name": "Linux", "level": "", "keywords": []}, {"name": "Cloud Computing", "level": "", "keywords": []}, {"name": "Software Defined Networking", "level": "", "keywords": []}, {"name": "Open-Source Software", "level": "", "keywords": []}, {"name": "Release Engineering", "level": "", "keywords": []}, {"name": "Capacity Planning", "level": "", "keywords": []}, {"name": "DevOps", "level": "", "keywords": []}, {"name": "Software Quality Assurance", "level": "", "keywords": []}, {"name": "IaaS", "level": "", "keywords": []}, {"name": "Machine Learning", "level": "", "keywords": []}, {"name": "Vagrant", "level": "", "keywords": []}, {"name": "Software Development", "level": "", "keywords": []}, {"name": "PaaS", "level": "", "keywords": []}, {"name": "Artificial Intelligence (AI)", "level": "", "keywords": []}], "languages": [{"fluency": "Native Speaker", "language": "English"}], "interests": [], "references": [{"name": "<PERSON>", "reference": "<PERSON> is a very bright individual in high demand due to his extensive leadership and problem solving skills.  You only need to glance at the list of high profile companies <PERSON> has worked for - which is impressive in itself but once you interview <PERSON>, that is when you realize <PERSON> is the real deal."}, {"name": "Al Eskew", "reference": "I had the opportunity and privilege of working with <PERSON> during his time with Amex.  His technical expertise is of the highest caliber and I would highly recommend him in any of his listed skill sets.  "}, {"name": "<PERSON>", "reference": "I have had the opportunity to work with <PERSON> at FICO for the past two years designing the architecture of FICO's cloud infrastructure.  There are few people out in the industry that equal <PERSON>'s combined knowledge of Cloud, Virtualization, Compute and Storage.  The most impressive part of working with <PERSON> is not just that he is knowledgeable but more importantly he's an outstanding communicator and leader, both for the teams he represents as well as working with FICO's business partners, which I am fortunate to take part in."}, {"name": "<PERSON>", "reference": "I had pleasure to have <PERSON> on our team at AMEX. <PERSON> is a dedicated technologist and trusted advisor in his field. His dedication and commitment to his craft is very impressive. Always striving to achieve the client’s goals and a team player. I would recommend <PERSON> to anyone looking to solve challenging objectives."}, {"name": "<PERSON>", "reference": "<PERSON> is one of the most talented professionals I have had the honor to work with in technology.  He came in to address performance issues with the virtual environment and a mis-configured UCS system.  <PERSON> hit the ground running, extremely knowledgeable and confident.  He proposed bold changes and produced big results very quickly.  He is a subject matter expert across many disciples and continues to embrace emerging technologies."}, {"name": "<PERSON>", "reference": "I had the pleasure of working with <PERSON> during his Residency at PayPal for VCE. <PERSON> was instrumental in the initial implementation of the Vblock infrastructure, as well as leading the Managed Services team that was onsite. <PERSON> is the consummate professional and worked very closely with the customer, understanding their needs and offering direction, when required. He worked well with the other team members to ensure the highest level of customer satisfaction. He brought a lot to the table on the technical side, applying his vast experience to this new implementation. <PERSON> is a hard worker and will stay with an issue until it becomes fully resolved, no matter the timeframe or the effort required."}], "projects": [], "meta": {"version": "v1.0.0", "canonical": "https://github.com/jsonresume/resume-schema/blob/v1.0.0/schema.json"}}