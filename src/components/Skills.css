.skills-section {
  padding: var(--space-64) var(--space-32);
  background-color: var(--color-background);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.skills-container {
  max-width: var(--container-xl);
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-48);
}

.skills-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.skills-header h2 {
  font-size: var(--font-size-4xl);
  color: var(--color-text);
  margin-bottom: var(--space-16);
  font-weight: var(--font-weight-semibold);
}

.skills-header p {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.category-tabs {
  display: flex;
  gap: var(--space-16);
  flex-wrap: wrap;
  justify-content: center;
  padding: 0 var(--space-16);
}

.category-tab {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-16) var(--space-24);
  border: none;
  border-radius: var(--radius-lg);
  background: var(--color-surface);
  color: var(--color-text);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.category-tab svg {
  font-size: var(--font-size-lg);
}

.category-tab:hover {
  background: var(--color-primary-hover);
  transform: translateY(-2px);
}

.category-tab.active {
  background: var(--color-primary);
  box-shadow: var(--shadow-lg);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--space-24);
  padding: var(--space-16);
}

.skill-card {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  color: var(--color-text);
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  cursor: pointer;
  will-change: transform;
  transition: border-color var(--duration-normal) var(--ease-standard);
}

.skill-card:hover {
  border-color: var(--color-primary);
}

.skill-icon {
  font-size: var(--font-size-3xl);
  color: var(--color-primary);
  margin-bottom: var(--space-12);
}

.skill-card h3 {
  font-size: var(--font-size-xl);
  margin: 0;
  color: var(--color-text);
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-16);
  margin-top: var(--space-32);
}

.nav-button {
  background: var(--color-surface);
  border: none;
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-text);
  transition: all var(--duration-normal) var(--ease-standard);
}

.nav-button:hover:not(:disabled) {
  background: var(--color-primary);
  transform: scale(1.1);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-indicator {
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
  .skills-section {
    padding: var(--space-32) var(--space-16);
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .category-tab {
    padding: var(--space-12) var(--space-16);
    font-size: var(--font-size-sm);
  }
}


.skill-tag:hover .skill-hover-card {
  opacity: 1;
  transform: translateX(-50%) translateY(-0.5rem);
}

.category-label {
  display: block;
  margin-top: var(--space-8);
  color: var(--color-success);
  font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
  .skills-controls {
    flex-direction: column;
    gap: var(--space-24);
  }

  .tag-cloud-container {
    min-height: 50vh;
  }
}