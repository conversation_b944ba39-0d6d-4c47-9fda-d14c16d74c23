
import React, { useState } from 'react';
import FadeInUp from './FadeInUp.jsx';
import resumeData from '../services/resumeData.jsx';
import './Experience.css';

const Experience = () => {
  const [expanded, setExpanded] = useState(0);

  const toggleExpand = (index) => {
    if (expanded === index) {
      setExpanded(null);
    } else {
      setExpanded(index);
    }
  };

  const experiences = resumeData.getFormattedWorkExperience();

  return (
    <section id="experience" className="experience">
      <div className="container">
        <h2 className="section-title">Experience</h2>
        <div className="timeline">
          {experiences.map((item, index) => (
            <FadeInUp key={index}>
              <div className="timeline-item" data-company={item.company}>
                <div className="timeline-content">
                  <div className="timeline-header" onClick={() => toggleExpand(index)}>
                    <h3 className="job-title">{item.title}</h3>
                    <span className="company">{item.company}</span>
                    <span className="duration">{item.duration}</span>
                    {item.location && <span className="location">{item.location}</span>}
                    <button className={`expand-btn ${expanded === index ? 'active' : ''}`} aria-label="Expand details">
                      {expanded === index ? '−' : '+'}
                    </button>
                  </div>
                  <div className={`timeline-details ${expanded === index ? 'expanded' : ''}`}>
                    <ul className="responsibilities">
                      {item.responsibilities.map((responsibility, i) => (
                        <li key={i}>{responsibility}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </FadeInUp>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Experience;
