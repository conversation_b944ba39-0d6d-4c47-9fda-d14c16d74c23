import React, { useState, useEffect } from 'react';
import { useLocation, Link } from 'react-router-dom';
import Icon from '../AppIcon';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  const navigationItems = [
    { label: 'Home', path: '/portfolio-homepage', icon: 'Home' },
    { label: 'About', path: '/about-page', icon: 'User' },
    { label: 'Projects', path: '/projects-portfolio', icon: 'FolderOpen' },
    { label: 'Blog', path: '/blog-articles-page', icon: 'FileText' },
    { label: 'Contact', path: '/contact-page', icon: 'Mail' },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const isActivePath = (path) => {
    return location.pathname === path;
  };

  return (
    <>
      <header 
        className={`fixed top-0 left-0 right-0 z-100 bg-background/95 backdrop-blur-sm transition-all duration-fast ${
          isScrolled ? 'nav-shadow' : ''
        }`}
      >
        <div className="max-w-7xl mx-auto px-6 lg:px-12">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <Link 
              to="/portfolio-homepage" 
              className="flex items-center space-x-3 group transition-smooth hover:opacity-80"
            >
              <div className="w-8 h-8 lg:w-10 lg:h-10 bg-primary rounded-md flex items-center justify-center">
                <svg 
                  viewBox="0 0 24 24" 
                  className="w-5 h-5 lg:w-6 lg:h-6 text-primary-foreground"
                  fill="currentColor"
                >
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                  <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" fill="none"/>
                </svg>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-lg lg:text-xl font-semibold text-primary">
                  Portfolio Pro
                </h1>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`px-4 py-2 rounded-sm text-sm font-medium transition-smooth relative ${
                    isActivePath(item.path)
                      ? 'text-accent bg-accent/10' :'text-text-secondary hover:text-text-primary hover:bg-surface'
                  }`}
                  aria-current={isActivePath(item.path) ? 'page' : undefined}
                >
                  <span className="flex items-center space-x-2">
                    <Icon name={item.icon} size={16} />
                    <span>{item.label}</span>
                  </span>
                  {isActivePath(item.path) && (
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-accent rounded-full" />
                  )}
                </Link>
              ))}
            </nav>

            {/* Mobile Menu Button */}
            <button
              onClick={toggleMobileMenu}
              className="md:hidden p-2 rounded-sm text-text-secondary hover:text-text-primary hover:bg-surface transition-smooth"
              aria-label="Toggle navigation menu"
              aria-expanded={isMobileMenuOpen}
            >
              <Icon 
                name={isMobileMenuOpen ? 'X' : 'Menu'} 
                size={24} 
              />
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Navigation Menu */}
      <div 
        className={`fixed inset-0 z-200 md:hidden transition-layout ${
          isMobileMenuOpen ? 'visible' : 'invisible'
        }`}
      >
        {/* Backdrop */}
        <div 
          className={`absolute inset-0 bg-primary/20 backdrop-blur-sm transition-opacity duration-fast ${
            isMobileMenuOpen ? 'opacity-100' : 'opacity-0'
          }`}
          onClick={toggleMobileMenu}
        />
        
        {/* Menu Panel */}
        <div 
          className={`absolute top-0 right-0 w-80 max-w-[85vw] h-full bg-background shadow-interactive transform transition-transform duration-normal ${
            isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
          }`}
        >
          <div className="flex items-center justify-between p-6 border-b border-border">
            <h2 className="text-lg font-semibold text-primary">Navigation</h2>
            <button
              onClick={toggleMobileMenu}
              className="p-2 rounded-sm text-text-secondary hover:text-text-primary hover:bg-surface transition-smooth"
              aria-label="Close navigation menu"
            >
              <Icon name="X" size={24} />
            </button>
          </div>
          
          <nav className="p-6">
            <ul className="space-y-2">
              {navigationItems.map((item) => (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={`flex items-center space-x-3 px-4 py-3 rounded-md text-base font-medium transition-smooth ${
                      isActivePath(item.path)
                        ? 'text-accent bg-accent/10 border-l-2 border-accent' :'text-text-secondary hover:text-text-primary hover:bg-surface'
                    }`}
                    aria-current={isActivePath(item.path) ? 'page' : undefined}
                  >
                    <Icon name={item.icon} size={20} />
                    <span>{item.label}</span>
                    {isActivePath(item.path) && (
                      <Icon name="ChevronRight" size={16} className="ml-auto" />
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </>
  );
};

export default Header;